ISO-10303-21;
HEADER;
 FILE_DESCRIPTION(('a Product shape'),'1');
 FILE_NAME('Euclid  Shape Model','1998-09-10T11:25:01',('Author Name'),(
    'MATRA-DATAVISION'),'OL-2.0B','EUCLID','Authorisation status');
 FILE_SCHEMA(('AUTOMOTIVE_DESIGN_CC1 { 1 2 10303 214 -1 1 3  2}'));
ENDSEC;
DATA;
#1 = PRODUCT_RELATED_PRODUCT_CATEGORY('Undefined Category','Undefined De
scription',(#2));
#2 = PRODUCT('the product name','the product name','void',(#3));
#3 = MECHANICAL_CONTEXT('Mechanical',#4,'Assembly');
#4 = APPLICATION_CONTEXT('EUCLID');
#5 = APPLICATION_PROTOCOL_DEFINITION('CommitteeDraft','automotive_design
',1997,#4);
#6 = SHAPE_DEFINITION_REPRESENTATION(#7,#11);
#7 = PRODUCT_DEFINITION_SHAPE('void','void',#8);
#8 = PRODUCT_DEFINITION('void','void',#9,#10);
#9 = PRODUCT_DEFINITION_FORMATION('ID','void',#2);
#10 = PRODUCT_DEFINITION_CONTEXT('as proposed',#4,'First_Design');
#11 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#12),#1236);
#12 = MANIFOLD_SOLID_BREP('',#13);
#13 = CLOSED_SHELL('',(#14,#257,#558,#709,#803,#874,#944,#1052,#1151,
    #1232));
#14 = ADVANCED_FACE('',(#15),#49,.F.);
#15 = FACE_BOUND('',#16,.F.);
#16 = EDGE_LOOP('',(#17,#136,#186,#214));
#17 = ORIENTED_EDGE('',*,*,#18,.T.);
#18 = EDGE_CURVE('',#19,#21,#23,.T.);
#19 = VERTEX_POINT('',#20);
#20 = CARTESIAN_POINT('',(-27.8196811084,0.423702927757,5.43633));
#21 = VERTEX_POINT('',#22);
#22 = CARTESIAN_POINT('',(-7.976546275424,0.423702927757,5.43633));
#23 = SURFACE_CURVE('',#24,(#48,#80),.PCURVE_S2.);
#24 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#25,#26,#27,#28,#29,#30,#31,#32,
    #33,#34,#35,#36,#37,#38,#39,#40,#41,#42,#43,#44,#45,#46,#47),
  .UNSPECIFIED.,.F.,.F.,(4,2,2,2,2,3,2,2,2,2,4),(-9.753048731913,
    -8.657376849694,-4.328688424847,-2.164344212423,-1.082172106212,
    0.E+000,1.082172106212,2.164344212423,4.328688424847,8.657376849694,
    9.753048731913),.UNSPECIFIED.);
#25 = CARTESIAN_POINT('',(-27.8196811084,0.423702927757,5.43633));
#26 = CARTESIAN_POINT('',(-27.44664177115,0.423702927757,5.566853116015)
  );
#27 = CARTESIAN_POINT('',(-27.07201055731,0.423702927757,5.693350129935)
  );
#28 = CARTESIAN_POINT('',(-25.21253263571,0.423702927757,6.298546031822)
  );
#29 = CARTESIAN_POINT('',(-23.71979442537,0.423702927757,6.713431512354)
  );
#30 = CARTESIAN_POINT('',(-21.51106042838,0.423702927757,7.221607932559)
  );
#31 = CARTESIAN_POINT('',(-20.78124758741,0.423702927757,7.372657524772)
  );
#32 = CARTESIAN_POINT('',(-19.696293462,0.423702927757,7.56017012803));
#33 = CARTESIAN_POINT('',(-19.33642071715,0.423702927757,7.616304970701)
  );
#34 = CARTESIAN_POINT('',(-18.61775332841,0.423702927757,7.702450972357)
  );
#35 = CARTESIAN_POINT('',(-18.25883772732,0.423702927757,7.731450038723)
  );
#36 = CARTESIAN_POINT('',(-17.89811369191,0.423702927757,7.731450038723)
  );
#37 = CARTESIAN_POINT('',(-17.53738965651,0.423702927757,7.731450038723)
  );
#38 = CARTESIAN_POINT('',(-17.17847405541,0.423702927757,7.702450972357)
  );
#39 = CARTESIAN_POINT('',(-16.45980666667,0.423702927757,7.616304970701)
  );
#40 = CARTESIAN_POINT('',(-16.09993392183,0.423702927757,7.56017012803)
  );
#41 = CARTESIAN_POINT('',(-15.01497979641,0.423702927757,7.372657524772)
  );
#42 = CARTESIAN_POINT('',(-14.28516695545,0.423702927757,7.221607932559)
  );
#43 = CARTESIAN_POINT('',(-12.07643295845,0.423702927757,6.713431512354)
  );
#44 = CARTESIAN_POINT('',(-10.58369474811,0.423702927757,6.298546031822)
  );
#45 = CARTESIAN_POINT('',(-8.724216826515,0.423702927757,5.693350129935)
  );
#46 = CARTESIAN_POINT('',(-8.349585612678,0.423702927757,5.566853116015)
  );
#47 = CARTESIAN_POINT('',(-7.976546275424,0.423702927757,5.43633));
#48 = PCURVE('',#49,#54);
#49 = PLANE('',#50);
#50 = AXIS2_PLACEMENT_3D('',#51,#52,#53);
#51 = CARTESIAN_POINT('',(-2.898113691917,0.423702927757,7.93633));
#52 = DIRECTION('',(0.E+000,1.,0.E+000));
#53 = DIRECTION('',(0.E+000,0.E+000,1.));
#54 = DEFINITIONAL_REPRESENTATION('',(#55),#79);
#55 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#56,#57,#58,#59,#60,#61,#62,#63,
    #64,#65,#66,#67,#68,#69,#70,#71,#72,#73,#74,#75,#76,#77,#78),
  .UNSPECIFIED.,.F.,.F.,(4,2,2,2,2,3,2,2,2,2,4),(-9.753048731913,
    -8.657376849694,-4.328688424847,-2.164344212423,-1.082172106212,
    0.E+000,1.082172106212,2.164344212423,4.328688424847,8.657376849694,
    9.753048731913),.UNSPECIFIED.);
#56 = CARTESIAN_POINT('',(-2.5,-24.92156741649));
#57 = CARTESIAN_POINT('',(-2.369476883985,-24.54852807923));
#58 = CARTESIAN_POINT('',(-2.242979870065,-24.1738968654));
#59 = CARTESIAN_POINT('',(-1.637783968178,-22.31441894379));
#60 = CARTESIAN_POINT('',(-1.222898487646,-20.82168073346));
#61 = CARTESIAN_POINT('',(-0.714722067441,-18.61294673646));
#62 = CARTESIAN_POINT('',(-0.563672475228,-17.88313389549));
#63 = CARTESIAN_POINT('',(-0.37615987197,-16.79817977008));
#64 = CARTESIAN_POINT('',(-0.320025029299,-16.43830702524));
#65 = CARTESIAN_POINT('',(-0.233879027643,-15.71963963649));
#66 = CARTESIAN_POINT('',(-0.204879961277,-15.3607240354));
#67 = CARTESIAN_POINT('',(-0.204879961277,-15.));
#68 = CARTESIAN_POINT('',(-0.204879961277,-14.63927596459));
#69 = CARTESIAN_POINT('',(-0.233879027643,-14.2803603635));
#70 = CARTESIAN_POINT('',(-0.320025029299,-13.56169297476));
#71 = CARTESIAN_POINT('',(-0.37615987197,-13.20182022991));
#72 = CARTESIAN_POINT('',(-0.563672475228,-12.1168661045));
#73 = CARTESIAN_POINT('',(-0.714722067441,-11.38705326353));
#74 = CARTESIAN_POINT('',(-1.222898487646,-9.178319266539));
#75 = CARTESIAN_POINT('',(-1.637783968178,-7.685581056201));
#76 = CARTESIAN_POINT('',(-2.242979870065,-5.826103134599));
#77 = CARTESIAN_POINT('',(-2.369476883985,-5.451471920761));
#78 = CARTESIAN_POINT('',(-2.5,-5.078432583508));
#79 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#80 = PCURVE('',#81,#86);
#81 = TOROIDAL_SURFACE('',#82,8.25,54.873718663856);
#82 = AXIS2_PLACEMENT_3D('',#83,#84,#85);
#83 = CARTESIAN_POINT('',(-17.89811369191,-0.826297072243,-46.31367));
#84 = DIRECTION('',(0.E+000,-2.22044604925E-016,-1.));
#85 = DIRECTION('',(0.E+000,1.,-2.22044604925E-016));
#86 = DEFINITIONAL_REPRESENTATION('',(#87),#135);
#87 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#88,#89,#90,#91,#92,#93,#94,#95,
    #96,#97,#98,#99,#100,#101,#102,#103,#104,#105,#106,#107,#108,#109,
    #110,#111,#112,#113,#114,#115,#116,#117,#118,#119,#120,#121,#122,
    #123,#124,#125,#126,#127,#128,#129,#130,#131,#132,#133,#134),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(-9.753048731913,
    -9.309728335008,-8.866407938103,-8.423087541198,-7.979767144292,
    -7.536446747387,-7.093126350482,-6.649805953577,-6.206485556672,
    -5.763165159767,-5.319844762862,-4.876524365957,-4.433203969051,
    -3.989883572146,-3.546563175241,-3.103242778336,-2.659922381431,
    -2.216601984526,-1.773281587621,-1.329961190715,-0.88664079381,
    -0.443320396905,-3.552713678801E-015,0.443320396905,0.88664079381,
    1.329961190715,1.773281587621,2.216601984526,2.659922381431,
    3.103242778336,3.546563175241,3.989883572146,4.433203969051,
    4.876524365957,5.319844762862,5.763165159767,6.206485556672,
    6.649805953577,7.093126350482,7.536446747387,7.979767144292,
    8.423087541198,8.866407938103,9.309728335008,9.753048731913),
  .UNSPECIFIED.);
#88 = CARTESIAN_POINT('',(1.696124157963,-1.90983622455));
#89 = CARTESIAN_POINT('',(1.698012122136,-1.906942595099));
#90 = CARTESIAN_POINT('',(1.701959356034,-1.901155814072));
#91 = CARTESIAN_POINT('',(1.7084874644,-1.89248391648));
#92 = CARTESIAN_POINT('',(1.715711359905,-1.883824473771));
#93 = CARTESIAN_POINT('',(1.723736411497,-1.875190974748));
#94 = CARTESIAN_POINT('',(1.732699041614,-1.866586665031));
#95 = CARTESIAN_POINT('',(1.742767310299,-1.858016637247));
#96 = CARTESIAN_POINT('',(1.754151612829,-1.849486074495));
#97 = CARTESIAN_POINT('',(1.767118375587,-1.841000912303));
#98 = CARTESIAN_POINT('',(1.782009704244,-1.832567946628));
#99 = CARTESIAN_POINT('',(1.799271790701,-1.824195173621));
#100 = CARTESIAN_POINT('',(1.819496092675,-1.815892420892));
#101 = CARTESIAN_POINT('',(1.843483731328,-1.80767162354));
#102 = CARTESIAN_POINT('',(1.872335628537,-1.799549911231));
#103 = CARTESIAN_POINT('',(1.907592262986,-1.791552841646));
#104 = CARTESIAN_POINT('',(1.951530266856,-1.783707862826));
#105 = CARTESIAN_POINT('',(2.007543357991,-1.776058774724));
#106 = CARTESIAN_POINT('',(2.080862934592,-1.768674005145));
#107 = CARTESIAN_POINT('',(2.179681847184,-1.761668166881));
#108 = CARTESIAN_POINT('',(2.317050874271,-1.755240554276));
#109 = CARTESIAN_POINT('',(2.512524285783,-1.74974343193));
#110 = CARTESIAN_POINT('',(2.788441810662,-1.745797607263));
#111 = CARTESIAN_POINT('',(3.14159265359,-1.744297890708));
#112 = CARTESIAN_POINT('',(3.494743496518,-1.745797607263));
#113 = CARTESIAN_POINT('',(3.770661021397,-1.74974343193));
#114 = CARTESIAN_POINT('',(3.966134432908,-1.755240554276));
#115 = CARTESIAN_POINT('',(4.103503459996,-1.761668166881));
#116 = CARTESIAN_POINT('',(4.202322372587,-1.768674005145));
#117 = CARTESIAN_POINT('',(4.275641949188,-1.776058774724));
#118 = CARTESIAN_POINT('',(4.331655040323,-1.783707862826));
#119 = CARTESIAN_POINT('',(4.375593044193,-1.791552841646));
#120 = CARTESIAN_POINT('',(4.410849678643,-1.799549911231));
#121 = CARTESIAN_POINT('',(4.439701575852,-1.80767162354));
#122 = CARTESIAN_POINT('',(4.463689214504,-1.815892420892));
#123 = CARTESIAN_POINT('',(4.483913516478,-1.824195173621));
#124 = CARTESIAN_POINT('',(4.501175602935,-1.832567946628));
#125 = CARTESIAN_POINT('',(4.516066931592,-1.841000912303));
#126 = CARTESIAN_POINT('',(4.529033694351,-1.849486074495));
#127 = CARTESIAN_POINT('',(4.54041799688,-1.858016637247));
#128 = CARTESIAN_POINT('',(4.550486265566,-1.866586665031));
#129 = CARTESIAN_POINT('',(4.559448895683,-1.875190974748));
#130 = CARTESIAN_POINT('',(4.567473947275,-1.883824473771));
#131 = CARTESIAN_POINT('',(4.57469784278,-1.89248391648));
#132 = CARTESIAN_POINT('',(4.581225951145,-1.901155814072));
#133 = CARTESIAN_POINT('',(4.585173185044,-1.906942595099));
#134 = CARTESIAN_POINT('',(4.587061149217,-1.90983622455));
#135 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#136 = ORIENTED_EDGE('',*,*,#137,.T.);
#137 = EDGE_CURVE('',#21,#138,#140,.T.);
#138 = VERTEX_POINT('',#139);
#139 = CARTESIAN_POINT('',(-10.50301396304,0.423702927757,2.93633));
#140 = SURFACE_CURVE('',#141,(#145,#152),.PCURVE_S2.);
#141 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#142,#143,#144),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(0.E+000,
3.554299705008),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.010587075049,1.)) REPRESENTATION_ITEM('') );
#142 = CARTESIAN_POINT('',(-7.976546275424,0.423702927757,5.43633));
#143 = CARTESIAN_POINT('',(-9.420242096928,0.423702927757,4.003957457804
    ));
#144 = CARTESIAN_POINT('',(-10.50301396304,0.423702927757,2.93633));
#145 = PCURVE('',#49,#146);
#146 = DEFINITIONAL_REPRESENTATION('',(#147),#151);
#147 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#148,#149,#150),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(0.E+000,
3.554299705008),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.010587075049,1.)) REPRESENTATION_ITEM('') );
#148 = CARTESIAN_POINT('',(-2.5,-5.078432583508));
#149 = CARTESIAN_POINT('',(-3.932372542196,-6.522128405011));
#150 = CARTESIAN_POINT('',(-5.,-7.604900271125));
#151 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#152 = PCURVE('',#153,#158);
#153 = CONICAL_SURFACE('',#154,7.5,0.785398163397);
#154 = AXIS2_PLACEMENT_3D('',#155,#156,#157);
#155 = CARTESIAN_POINT('',(-17.89811369191,-0.826297072243,-12.06367));
#156 = DIRECTION('',(0.E+000,-2.22044604925E-016,-1.));
#157 = DIRECTION('',(-1.,-1.224606353822E-016,2.719172340232E-032));
#158 = DEFINITIONAL_REPRESENTATION('',(#159),#185);
#159 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#160,#161,#162,#163,#164,#165,
    #166,#167,#168,#169,#170,#171,#172,#173,#174,#175,#176,#177,#178,
    #179,#180,#181,#182,#183,#184),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,
    1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.1615590775,
    0.323118155001,0.484677232501,0.646236310001,0.807795387502,
    0.969354465002,1.130913542503,1.292472620003,1.454031697503,
    1.615590775004,1.777149852504,1.938708930004,2.100268007505,
    2.261827085005,2.423386162505,2.584945240006,2.746504317506,
    2.908063395007,3.069622472507,3.231181550007,3.392740627508,
    3.554299705008),.QUASI_UNIFORM_KNOTS.);
#160 = CARTESIAN_POINT('',(6.157857476012,-17.5));
#161 = CARTESIAN_POINT('',(6.157304831179,-17.45613532843));
#162 = CARTESIAN_POINT('',(6.156192803831,-17.369034253));
#163 = CARTESIAN_POINT('',(6.154504167531,-17.24022922767));
#164 = CARTESIAN_POINT('',(6.15279455739,-17.11323215337));
#165 = CARTESIAN_POINT('',(6.15106358201,-16.98800586851));
#166 = CARTESIAN_POINT('',(6.149310841213,-16.86451413191));
#167 = CARTESIAN_POINT('',(6.147535924576,-16.74272172673));
#168 = CARTESIAN_POINT('',(6.145738411434,-16.62259438981));
#169 = CARTESIAN_POINT('',(6.143917870468,-16.5040987895));
#170 = CARTESIAN_POINT('',(6.142073859396,-16.38720249214));
#171 = CARTESIAN_POINT('',(6.140205924616,-16.27187393322));
#172 = CARTESIAN_POINT('',(6.138313600849,-16.15808238872));
#173 = CARTESIAN_POINT('',(6.136396410758,-16.04579794794));
#174 = CARTESIAN_POINT('',(6.134453864564,-15.93499148723));
#175 = CARTESIAN_POINT('',(6.132485459637,-15.82563464481));
#176 = CARTESIAN_POINT('',(6.130490680076,-15.7176997966));
#177 = CARTESIAN_POINT('',(6.128468996271,-15.61116003298));
#178 = CARTESIAN_POINT('',(6.126419864458,-15.50598913612));
#179 = CARTESIAN_POINT('',(6.124342726204,-15.40216155983));
#180 = CARTESIAN_POINT('',(6.122237008073,-15.29965240364));
#181 = CARTESIAN_POINT('',(6.120102120531,-15.19843741256));
#182 = CARTESIAN_POINT('',(6.11793745959,-15.09849288457));
#183 = CARTESIAN_POINT('',(6.116474085284,-15.03269491212));
#184 = CARTESIAN_POINT('',(6.11573722796,-15.));
#185 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#186 = ORIENTED_EDGE('',*,*,#187,.T.);
#187 = EDGE_CURVE('',#138,#188,#190,.T.);
#188 = VERTEX_POINT('',#189);
#189 = CARTESIAN_POINT('',(-25.29321342079,0.423702927757,2.93633));
#190 = SURFACE_CURVE('',#191,(#195,#202),.PCURVE_S2.);
#191 = LINE('',#192,#193);
#192 = CARTESIAN_POINT('',(-2.898113691917,0.423702927757,2.93633));
#193 = VECTOR('',#194,1.);
#194 = DIRECTION('',(-1.,0.E+000,0.E+000));
#195 = PCURVE('',#49,#196);
#196 = DEFINITIONAL_REPRESENTATION('',(#197),#201);
#197 = LINE('',#198,#199);
#198 = CARTESIAN_POINT('',(-5.,0.E+000));
#199 = VECTOR('',#200,1.);
#200 = DIRECTION('',(0.E+000,-1.));
#201 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#202 = PCURVE('',#203,#208);
#203 = PLANE('',#204);
#204 = AXIS2_PLACEMENT_3D('',#205,#206,#207);
#205 = CARTESIAN_POINT('',(-2.898113691917,0.423702927757,2.93633));
#206 = DIRECTION('',(0.E+000,0.E+000,-1.));
#207 = DIRECTION('',(1.,0.E+000,0.E+000));
#208 = DEFINITIONAL_REPRESENTATION('',(#209),#213);
#209 = LINE('',#210,#211);
#210 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#211 = VECTOR('',#212,1.);
#212 = DIRECTION('',(-1.,0.E+000));
#213 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#214 = ORIENTED_EDGE('',*,*,#215,.T.);
#215 = EDGE_CURVE('',#188,#19,#216,.T.);
#216 = SURFACE_CURVE('',#217,(#221,#228),.PCURVE_S2.);
#217 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#218,#219,#220),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(0.E+000,
3.554299705008),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.010587075049,1.)) REPRESENTATION_ITEM('') );
#218 = CARTESIAN_POINT('',(-25.29321342079,0.423702927757,2.93633));
#219 = CARTESIAN_POINT('',(-26.3759852869,0.423702927757,4.003957457804)
  );
#220 = CARTESIAN_POINT('',(-27.8196811084,0.423702927757,5.43633));
#221 = PCURVE('',#49,#222);
#222 = DEFINITIONAL_REPRESENTATION('',(#223),#227);
#223 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#224,#225,#226),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(0.E+000,
3.554299705008),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.010587075049,1.)) REPRESENTATION_ITEM('') );
#224 = CARTESIAN_POINT('',(-5.,-22.39509972887));
#225 = CARTESIAN_POINT('',(-3.932372542196,-23.47787159498));
#226 = CARTESIAN_POINT('',(-2.5,-24.92156741649));
#227 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#228 = PCURVE('',#153,#229);
#229 = DEFINITIONAL_REPRESENTATION('',(#230),#256);
#230 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#231,#232,#233,#234,#235,#236,
    #237,#238,#239,#240,#241,#242,#243,#244,#245,#246,#247,#248,#249,
    #250,#251,#252,#253,#254,#255),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,
    1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.1615590775,
    0.323118155001,0.484677232501,0.646236310001,0.807795387502,
    0.969354465002,1.130913542503,1.292472620003,1.454031697503,
    1.615590775004,1.777149852504,1.938708930004,2.100268007505,
    2.261827085005,2.423386162505,2.584945240006,2.746504317506,
    2.908063395007,3.069622472507,3.231181550007,3.392740627508,
    3.554299705008),.QUASI_UNIFORM_KNOTS.);
#231 = CARTESIAN_POINT('',(3.309040732809,-15.));
#232 = CARTESIAN_POINT('',(3.308303875485,-15.03269491212));
#233 = CARTESIAN_POINT('',(3.306840501179,-15.09849288457));
#234 = CARTESIAN_POINT('',(3.304675840238,-15.19843741256));
#235 = CARTESIAN_POINT('',(3.302540952696,-15.29965240364));
#236 = CARTESIAN_POINT('',(3.300435234565,-15.40216155983));
#237 = CARTESIAN_POINT('',(3.298358096311,-15.50598913612));
#238 = CARTESIAN_POINT('',(3.296308964498,-15.61116003298));
#239 = CARTESIAN_POINT('',(3.294287280694,-15.7176997966));
#240 = CARTESIAN_POINT('',(3.292292501133,-15.82563464481));
#241 = CARTESIAN_POINT('',(3.290324096205,-15.93499148723));
#242 = CARTESIAN_POINT('',(3.288381550011,-16.04579794794));
#243 = CARTESIAN_POINT('',(3.286464359921,-16.15808238872));
#244 = CARTESIAN_POINT('',(3.284572036153,-16.27187393322));
#245 = CARTESIAN_POINT('',(3.282704101374,-16.38720249214));
#246 = CARTESIAN_POINT('',(3.280860090302,-16.5040987895));
#247 = CARTESIAN_POINT('',(3.279039549336,-16.62259438981));
#248 = CARTESIAN_POINT('',(3.277242036193,-16.74272172673));
#249 = CARTESIAN_POINT('',(3.275467119556,-16.86451413191));
#250 = CARTESIAN_POINT('',(3.273714378759,-16.98800586851));
#251 = CARTESIAN_POINT('',(3.271983403379,-17.11323215337));
#252 = CARTESIAN_POINT('',(3.270273793238,-17.24022922767));
#253 = CARTESIAN_POINT('',(3.268585156938,-17.369034253));
#254 = CARTESIAN_POINT('',(3.26747312959,-17.45613532843));
#255 = CARTESIAN_POINT('',(3.266920484758,-17.5));
#256 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#257 = ADVANCED_FACE('',(#258),#272,.F.);
#258 = FACE_BOUND('',#259,.F.);
#259 = EDGE_LOOP('',(#260,#290,#335,#425,#515));
#260 = ORIENTED_EDGE('',*,*,#261,.T.);
#261 = EDGE_CURVE('',#262,#264,#266,.T.);
#262 = VERTEX_POINT('',#263);
#263 = CARTESIAN_POINT('',(-25.29321342079,-2.076297072243,2.93633));
#264 = VERTEX_POINT('',#265);
#265 = CARTESIAN_POINT('',(-10.50301396304,-2.076297072243,2.93633));
#266 = SURFACE_CURVE('',#267,(#271,#283),.PCURVE_S2.);
#267 = LINE('',#268,#269);
#268 = CARTESIAN_POINT('',(-32.89811369191,-2.076297072243,2.93633));
#269 = VECTOR('',#270,1.);
#270 = DIRECTION('',(1.,0.E+000,0.E+000));
#271 = PCURVE('',#272,#277);
#272 = PLANE('',#273);
#273 = AXIS2_PLACEMENT_3D('',#274,#275,#276);
#274 = CARTESIAN_POINT('',(-32.89811369191,-2.076297072243,7.93633));
#275 = DIRECTION('',(0.E+000,-1.,0.E+000));
#276 = DIRECTION('',(0.E+000,0.E+000,-1.));
#277 = DEFINITIONAL_REPRESENTATION('',(#278),#282);
#278 = LINE('',#279,#280);
#279 = CARTESIAN_POINT('',(5.,0.E+000));
#280 = VECTOR('',#281,1.);
#281 = DIRECTION('',(0.E+000,1.));
#282 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#283 = PCURVE('',#203,#284);
#284 = DEFINITIONAL_REPRESENTATION('',(#285),#289);
#285 = LINE('',#286,#287);
#286 = CARTESIAN_POINT('',(-30.,2.5));
#287 = VECTOR('',#288,1.);
#288 = DIRECTION('',(1.,0.E+000));
#289 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#290 = ORIENTED_EDGE('',*,*,#291,.T.);
#291 = EDGE_CURVE('',#264,#292,#294,.T.);
#292 = VERTEX_POINT('',#293);
#293 = CARTESIAN_POINT('',(-7.976546275424,-2.076297072243,5.43633));
#294 = SURFACE_CURVE('',#295,(#299,#306),.PCURVE_S2.);
#295 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#296,#297,#298),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(0.E+000,
3.554299705008),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.010587075051,1.)) REPRESENTATION_ITEM('') );
#296 = CARTESIAN_POINT('',(-10.50301396304,-2.076297072243,2.93633));
#297 = CARTESIAN_POINT('',(-9.420242096928,-2.076297072243,
    4.003957457804));
#298 = CARTESIAN_POINT('',(-7.976546275424,-2.076297072243,5.43633));
#299 = PCURVE('',#272,#300);
#300 = DEFINITIONAL_REPRESENTATION('',(#301),#305);
#301 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#302,#303,#304),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(0.E+000,
3.554299705008),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.010587075051,1.)) REPRESENTATION_ITEM('') );
#302 = CARTESIAN_POINT('',(5.,22.395099728875));
#303 = CARTESIAN_POINT('',(3.932372542196,23.477871594989));
#304 = CARTESIAN_POINT('',(2.5,24.921567416492));
#305 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#306 = PCURVE('',#153,#307);
#307 = DEFINITIONAL_REPRESENTATION('',(#308),#334);
#308 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#309,#310,#311,#312,#313,#314,
    #315,#316,#317,#318,#319,#320,#321,#322,#323,#324,#325,#326,#327,
    #328,#329,#330,#331,#332,#333),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,
    1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.1615590775,
    0.323118155001,0.484677232501,0.646236310001,0.807795387502,
    0.969354465002,1.130913542503,1.292472620003,1.454031697503,
    1.615590775004,1.777149852504,1.938708930004,2.100268007505,
    2.261827085005,2.423386162505,2.584945240006,2.746504317506,
    2.908063395007,3.069622472507,3.231181550007,3.392740627508,
    3.554299705008),.QUASI_UNIFORM_KNOTS.);
#309 = CARTESIAN_POINT('',(0.16744807922,-15.));
#310 = CARTESIAN_POINT('',(0.166711221896,-15.03269491212));
#311 = CARTESIAN_POINT('',(0.16524784759,-15.09849288457));
#312 = CARTESIAN_POINT('',(0.163083186648,-15.19843741256));
#313 = CARTESIAN_POINT('',(0.160948299106,-15.29965240364));
#314 = CARTESIAN_POINT('',(0.158842580975,-15.40216155983));
#315 = CARTESIAN_POINT('',(0.156765442722,-15.50598913612));
#316 = CARTESIAN_POINT('',(0.154716310909,-15.61116003298));
#317 = CARTESIAN_POINT('',(0.152694627104,-15.7176997966));
#318 = CARTESIAN_POINT('',(0.150699847543,-15.82563464481));
#319 = CARTESIAN_POINT('',(0.148731442616,-15.93499148723));
#320 = CARTESIAN_POINT('',(0.146788896422,-16.04579794794));
#321 = CARTESIAN_POINT('',(0.144871706331,-16.15808238872));
#322 = CARTESIAN_POINT('',(0.142979382563,-16.27187393322));
#323 = CARTESIAN_POINT('',(0.141111447784,-16.38720249214));
#324 = CARTESIAN_POINT('',(0.139267436712,-16.5040987895));
#325 = CARTESIAN_POINT('',(0.137446895746,-16.62259438981));
#326 = CARTESIAN_POINT('',(0.135649382603,-16.74272172673));
#327 = CARTESIAN_POINT('',(0.133874465967,-16.86451413191));
#328 = CARTESIAN_POINT('',(0.132121725169,-16.98800586851));
#329 = CARTESIAN_POINT('',(0.130390749789,-17.11323215337));
#330 = CARTESIAN_POINT('',(0.128681139649,-17.24022922767));
#331 = CARTESIAN_POINT('',(0.126992503349,-17.369034253));
#332 = CARTESIAN_POINT('',(0.125880476001,-17.45613532843));
#333 = CARTESIAN_POINT('',(0.125327831168,-17.5));
#334 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#335 = ORIENTED_EDGE('',*,*,#336,.T.);
#336 = EDGE_CURVE('',#292,#337,#339,.T.);
#337 = VERTEX_POINT('',#338);
#338 = CARTESIAN_POINT('',(-17.89811369191,-2.076297072243,
    7.731450038723));
#339 = SURFACE_CURVE('',#340,(#364,#391),.PCURVE_S2.);
#340 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#341,#342,#343,#344,#345,#346,
    #347,#348,#349,#350,#351,#352,#353,#354,#355,#356,#357,#358,#359,
    #360,#361,#362,#363),.UNSPECIFIED.,.F.,.F.,(4,2,2,2,2,3,2,2,2,2,4),(
    -9.753048731913,-8.657376849694,-4.328688424847,-2.164344212423,
    -1.082172106212,0.E+000,1.082172106212,2.164344212423,4.328688424847
    ,8.657376849694,9.753048731913),.UNSPECIFIED.);
#341 = CARTESIAN_POINT('',(-7.976546275424,-2.076297072243,5.43633));
#342 = CARTESIAN_POINT('',(-8.349585612678,-2.076297072243,
    5.566853116015));
#343 = CARTESIAN_POINT('',(-8.724216826515,-2.076297072243,
    5.693350129935));
#344 = CARTESIAN_POINT('',(-10.58369474811,-2.076297072243,
    6.298546031822));
#345 = CARTESIAN_POINT('',(-12.07643295845,-2.076297072243,
    6.713431512354));
#346 = CARTESIAN_POINT('',(-14.28516695545,-2.076297072243,
    7.221607932559));
#347 = CARTESIAN_POINT('',(-15.01497979641,-2.076297072243,
    7.372657524772));
#348 = CARTESIAN_POINT('',(-16.09993392183,-2.076297072243,7.56017012803
    ));
#349 = CARTESIAN_POINT('',(-16.45980666667,-2.076297072243,
    7.616304970701));
#350 = CARTESIAN_POINT('',(-17.17847405541,-2.076297072243,
    7.702450972357));
#351 = CARTESIAN_POINT('',(-17.53738965651,-2.076297072243,
    7.731450038723));
#352 = CARTESIAN_POINT('',(-17.89811369191,-2.076297072243,
    7.731450038723));
#353 = CARTESIAN_POINT('',(-18.25883772732,-2.076297072243,
    7.731450038723));
#354 = CARTESIAN_POINT('',(-18.61775332841,-2.076297072243,
    7.702450972357));
#355 = CARTESIAN_POINT('',(-19.33642071715,-2.076297072243,
    7.616304970701));
#356 = CARTESIAN_POINT('',(-19.696293462,-2.076297072243,7.56017012803)
  );
#357 = CARTESIAN_POINT('',(-20.78124758741,-2.076297072243,
    7.372657524772));
#358 = CARTESIAN_POINT('',(-21.51106042838,-2.076297072243,
    7.221607932559));
#359 = CARTESIAN_POINT('',(-23.71979442537,-2.076297072243,
    6.713431512354));
#360 = CARTESIAN_POINT('',(-25.21253263571,-2.076297072243,
    6.298546031822));
#361 = CARTESIAN_POINT('',(-27.07201055731,-2.076297072243,
    5.693350129935));
#362 = CARTESIAN_POINT('',(-27.44664177115,-2.076297072243,
    5.566853116015));
#363 = CARTESIAN_POINT('',(-27.8196811084,-2.076297072243,5.43633));
#364 = PCURVE('',#272,#365);
#365 = DEFINITIONAL_REPRESENTATION('',(#366),#390);
#366 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#367,#368,#369,#370,#371,#372,
    #373,#374,#375,#376,#377,#378,#379,#380,#381,#382,#383,#384,#385,
    #386,#387,#388,#389),.UNSPECIFIED.,.F.,.F.,(4,2,2,2,2,3,2,2,2,2,4),(
    -9.753048731913,-8.657376849694,-4.328688424847,-2.164344212423,
    -1.082172106212,0.E+000,1.082172106212,2.164344212423,4.328688424847
    ,8.657376849694,9.753048731913),.UNSPECIFIED.);
#367 = CARTESIAN_POINT('',(2.5,24.921567416492));
#368 = CARTESIAN_POINT('',(2.369476883985,24.548528079239));
#369 = CARTESIAN_POINT('',(2.242979870065,24.173896865401));
#370 = CARTESIAN_POINT('',(1.637783968178,22.314418943799));
#371 = CARTESIAN_POINT('',(1.222898487646,20.821680733461));
#372 = CARTESIAN_POINT('',(0.714722067441,18.612946736467));
#373 = CARTESIAN_POINT('',(0.563672475228,17.883133895499));
#374 = CARTESIAN_POINT('',(0.37615987197,16.798179770085));
#375 = CARTESIAN_POINT('',(0.320025029299,16.43830702524));
#376 = CARTESIAN_POINT('',(0.233879027643,15.719639636498));
#377 = CARTESIAN_POINT('',(0.204879961277,15.360724035404));
#378 = CARTESIAN_POINT('',(0.204879961277,15.));
#379 = CARTESIAN_POINT('',(0.204879961277,14.639275964596));
#380 = CARTESIAN_POINT('',(0.233879027643,14.280360363502));
#381 = CARTESIAN_POINT('',(0.320025029299,13.56169297476));
#382 = CARTESIAN_POINT('',(0.37615987197,13.201820229915));
#383 = CARTESIAN_POINT('',(0.563672475228,12.116866104502));
#384 = CARTESIAN_POINT('',(0.714722067441,11.387053263533));
#385 = CARTESIAN_POINT('',(1.222898487646,9.178319266539));
#386 = CARTESIAN_POINT('',(1.637783968178,7.685581056201));
#387 = CARTESIAN_POINT('',(2.242979870065,5.826103134599));
#388 = CARTESIAN_POINT('',(2.369476883985,5.451471920761));
#389 = CARTESIAN_POINT('',(2.5,5.078432583508));
#390 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#391 = PCURVE('',#392,#397);
#392 = TOROIDAL_SURFACE('',#393,8.25,54.873718663856);
#393 = AXIS2_PLACEMENT_3D('',#394,#395,#396);
#394 = CARTESIAN_POINT('',(-17.89811369191,-0.826297072243,-46.31367));
#395 = DIRECTION('',(0.E+000,-2.22044604925E-016,-1.));
#396 = DIRECTION('',(0.E+000,1.,-2.22044604925E-016));
#397 = DEFINITIONAL_REPRESENTATION('',(#398),#424);
#398 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#399,#400,#401,#402,#403,#404,
    #405,#406,#407,#408,#409,#410,#411,#412,#413,#414,#415,#416,#417,
    #418,#419,#420,#421,#422,#423),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,
    1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(-9.753048731913,-9.309728335008,
    -8.866407938103,-8.423087541198,-7.979767144292,-7.536446747387,
    -7.093126350482,-6.649805953577,-6.206485556672,-5.763165159767,
    -5.319844762862,-4.876524365957,-4.433203969051,-3.989883572146,
    -3.546563175241,-3.103242778336,-2.659922381431,-2.216601984526,
    -1.773281587621,-1.329961190715,-0.88664079381,-0.443320396905,
    0.E+000),.UNSPECIFIED.);
#399 = CARTESIAN_POINT('',(4.837716811553,-1.90983622455));
#400 = CARTESIAN_POINT('',(4.839604775725,-1.906942595099));
#401 = CARTESIAN_POINT('',(4.843552009624,-1.901155814072));
#402 = CARTESIAN_POINT('',(4.85008011799,-1.89248391648));
#403 = CARTESIAN_POINT('',(4.857304013494,-1.883824473771));
#404 = CARTESIAN_POINT('',(4.865329065086,-1.875190974748));
#405 = CARTESIAN_POINT('',(4.874291695205,-1.866586665031));
#406 = CARTESIAN_POINT('',(4.884359963883,-1.858016637247));
#407 = CARTESIAN_POINT('',(4.895744266443,-1.849486074495));
#408 = CARTESIAN_POINT('',(4.908711029087,-1.841000912302));
#409 = CARTESIAN_POINT('',(4.923602358169,-1.832567946629));
#410 = CARTESIAN_POINT('',(4.940864443041,-1.824195173617));
#411 = CARTESIAN_POINT('',(4.961088750932,-1.815892420907));
#412 = CARTESIAN_POINT('',(4.985076367501,-1.807671623483));
#413 = CARTESIAN_POINT('',(5.013928347127,-1.799549911442));
#414 = CARTESIAN_POINT('',(5.04918467399,-1.79155284086));
#415 = CARTESIAN_POINT('',(5.09312382579,-1.783707865756));
#416 = CARTESIAN_POINT('',(5.149132632791,-1.776058763789));
#417 = CARTESIAN_POINT('',(5.222468198,-1.768674045956));
#418 = CARTESIAN_POINT('',(5.321227440292,-1.761668014571));
#419 = CARTESIAN_POINT('',(5.458819159968,-1.755241122705));
#420 = CARTESIAN_POINT('',(5.653461471425,-1.749741310524));
#421 = CARTESIAN_POINT('',(5.932480703933,-1.745805524459));
#422 = CARTESIAN_POINT('',(6.160197445577,-1.744780737041));
#423 = CARTESIAN_POINT('',(6.28318530718,-1.744797796227));
#424 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#425 = ORIENTED_EDGE('',*,*,#426,.T.);
#426 = EDGE_CURVE('',#337,#427,#429,.T.);
#427 = VERTEX_POINT('',#428);
#428 = CARTESIAN_POINT('',(-27.8196811084,-2.076297072243,5.43633));
#429 = SURFACE_CURVE('',#430,(#454,#481),.PCURVE_S2.);
#430 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#431,#432,#433,#434,#435,#436,
    #437,#438,#439,#440,#441,#442,#443,#444,#445,#446,#447,#448,#449,
    #450,#451,#452,#453),.UNSPECIFIED.,.F.,.F.,(4,2,2,2,2,3,2,2,2,2,4),(
    -9.753048731913,-8.657376849694,-4.328688424847,-2.164344212423,
    -1.082172106212,0.E+000,1.082172106212,2.164344212423,4.328688424847
    ,8.657376849694,9.753048731913),.UNSPECIFIED.);
#431 = CARTESIAN_POINT('',(-7.976546275424,-2.076297072243,5.43633));
#432 = CARTESIAN_POINT('',(-8.349585612678,-2.076297072243,
    5.566853116015));
#433 = CARTESIAN_POINT('',(-8.724216826515,-2.076297072243,
    5.693350129935));
#434 = CARTESIAN_POINT('',(-10.58369474811,-2.076297072243,
    6.298546031822));
#435 = CARTESIAN_POINT('',(-12.07643295845,-2.076297072243,
    6.713431512354));
#436 = CARTESIAN_POINT('',(-14.28516695545,-2.076297072243,
    7.221607932559));
#437 = CARTESIAN_POINT('',(-15.01497979641,-2.076297072243,
    7.372657524772));
#438 = CARTESIAN_POINT('',(-16.09993392183,-2.076297072243,7.56017012803
    ));
#439 = CARTESIAN_POINT('',(-16.45980666667,-2.076297072243,
    7.616304970701));
#440 = CARTESIAN_POINT('',(-17.17847405541,-2.076297072243,
    7.702450972357));
#441 = CARTESIAN_POINT('',(-17.53738965651,-2.076297072243,
    7.731450038723));
#442 = CARTESIAN_POINT('',(-17.89811369191,-2.076297072243,
    7.731450038723));
#443 = CARTESIAN_POINT('',(-18.25883772732,-2.076297072243,
    7.731450038723));
#444 = CARTESIAN_POINT('',(-18.61775332841,-2.076297072243,
    7.702450972357));
#445 = CARTESIAN_POINT('',(-19.33642071715,-2.076297072243,
    7.616304970701));
#446 = CARTESIAN_POINT('',(-19.696293462,-2.076297072243,7.56017012803)
  );
#447 = CARTESIAN_POINT('',(-20.78124758741,-2.076297072243,
    7.372657524772));
#448 = CARTESIAN_POINT('',(-21.51106042838,-2.076297072243,
    7.221607932559));
#449 = CARTESIAN_POINT('',(-23.71979442537,-2.076297072243,
    6.713431512354));
#450 = CARTESIAN_POINT('',(-25.21253263571,-2.076297072243,
    6.298546031822));
#451 = CARTESIAN_POINT('',(-27.07201055731,-2.076297072243,
    5.693350129935));
#452 = CARTESIAN_POINT('',(-27.44664177115,-2.076297072243,
    5.566853116015));
#453 = CARTESIAN_POINT('',(-27.8196811084,-2.076297072243,5.43633));
#454 = PCURVE('',#272,#455);
#455 = DEFINITIONAL_REPRESENTATION('',(#456),#480);
#456 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#457,#458,#459,#460,#461,#462,
    #463,#464,#465,#466,#467,#468,#469,#470,#471,#472,#473,#474,#475,
    #476,#477,#478,#479),.UNSPECIFIED.,.F.,.F.,(4,2,2,2,2,3,2,2,2,2,4),(
    -9.753048731913,-8.657376849694,-4.328688424847,-2.164344212423,
    -1.082172106212,0.E+000,1.082172106212,2.164344212423,4.328688424847
    ,8.657376849694,9.753048731913),.UNSPECIFIED.);
#457 = CARTESIAN_POINT('',(2.5,24.921567416492));
#458 = CARTESIAN_POINT('',(2.369476883985,24.548528079239));
#459 = CARTESIAN_POINT('',(2.242979870065,24.173896865401));
#460 = CARTESIAN_POINT('',(1.637783968178,22.314418943799));
#461 = CARTESIAN_POINT('',(1.222898487646,20.821680733461));
#462 = CARTESIAN_POINT('',(0.714722067441,18.612946736467));
#463 = CARTESIAN_POINT('',(0.563672475228,17.883133895499));
#464 = CARTESIAN_POINT('',(0.37615987197,16.798179770085));
#465 = CARTESIAN_POINT('',(0.320025029299,16.43830702524));
#466 = CARTESIAN_POINT('',(0.233879027643,15.719639636498));
#467 = CARTESIAN_POINT('',(0.204879961277,15.360724035404));
#468 = CARTESIAN_POINT('',(0.204879961277,15.));
#469 = CARTESIAN_POINT('',(0.204879961277,14.639275964596));
#470 = CARTESIAN_POINT('',(0.233879027643,14.280360363502));
#471 = CARTESIAN_POINT('',(0.320025029299,13.56169297476));
#472 = CARTESIAN_POINT('',(0.37615987197,13.201820229915));
#473 = CARTESIAN_POINT('',(0.563672475228,12.116866104502));
#474 = CARTESIAN_POINT('',(0.714722067441,11.387053263533));
#475 = CARTESIAN_POINT('',(1.222898487646,9.178319266539));
#476 = CARTESIAN_POINT('',(1.637783968178,7.685581056201));
#477 = CARTESIAN_POINT('',(2.242979870065,5.826103134599));
#478 = CARTESIAN_POINT('',(2.369476883985,5.451471920761));
#479 = CARTESIAN_POINT('',(2.5,5.078432583508));
#480 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#481 = PCURVE('',#482,#487);
#482 = TOROIDAL_SURFACE('',#483,8.25,54.873718663856);
#483 = AXIS2_PLACEMENT_3D('',#484,#485,#486);
#484 = CARTESIAN_POINT('',(-17.89811369191,-0.826297072243,-46.31367));
#485 = DIRECTION('',(0.E+000,-2.22044604925E-016,-1.));
#486 = DIRECTION('',(0.E+000,1.,-2.22044604925E-016));
#487 = DEFINITIONAL_REPRESENTATION('',(#488),#514);
#488 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#489,#490,#491,#492,#493,#494,
    #495,#496,#497,#498,#499,#500,#501,#502,#503,#504,#505,#506,#507,
    #508,#509,#510,#511,#512,#513),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,
    1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.443320396905,
    0.88664079381,1.329961190715,1.773281587621,2.216601984526,
    2.659922381431,3.103242778336,3.546563175241,3.989883572146,
    4.433203969051,4.876524365957,5.319844762862,5.763165159767,
    6.206485556672,6.649805953577,7.093126350482,7.536446747387,
    7.979767144292,8.423087541198,8.866407938103,9.309728335008,
    9.753048731913),.QUASI_UNIFORM_KNOTS.);
#489 = CARTESIAN_POINT('',(0.E+000,-1.744797796227));
#490 = CARTESIAN_POINT('',(0.122987861602,-1.744780737041));
#491 = CARTESIAN_POINT('',(0.350704603246,-1.745805524459));
#492 = CARTESIAN_POINT('',(0.629723835754,-1.749741310524));
#493 = CARTESIAN_POINT('',(0.824366147211,-1.755241122705));
#494 = CARTESIAN_POINT('',(0.961957866888,-1.761668014571));
#495 = CARTESIAN_POINT('',(1.06071710918,-1.768674045956));
#496 = CARTESIAN_POINT('',(1.134052674389,-1.776058763789));
#497 = CARTESIAN_POINT('',(1.190061481389,-1.783707865756));
#498 = CARTESIAN_POINT('',(1.23400063319,-1.79155284086));
#499 = CARTESIAN_POINT('',(1.269256960052,-1.799549911442));
#500 = CARTESIAN_POINT('',(1.298108939679,-1.807671623483));
#501 = CARTESIAN_POINT('',(1.322096556248,-1.815892420907));
#502 = CARTESIAN_POINT('',(1.342320864139,-1.824195173617));
#503 = CARTESIAN_POINT('',(1.359582949011,-1.832567946629));
#504 = CARTESIAN_POINT('',(1.374474278092,-1.841000912302));
#505 = CARTESIAN_POINT('',(1.387441040737,-1.849486074495));
#506 = CARTESIAN_POINT('',(1.398825343297,-1.858016637247));
#507 = CARTESIAN_POINT('',(1.408893611974,-1.866586665031));
#508 = CARTESIAN_POINT('',(1.417856242094,-1.875190974748));
#509 = CARTESIAN_POINT('',(1.425881293685,-1.883824473771));
#510 = CARTESIAN_POINT('',(1.43310518919,-1.89248391648));
#511 = CARTESIAN_POINT('',(1.439633297555,-1.901155814072));
#512 = CARTESIAN_POINT('',(1.443580531454,-1.906942595099));
#513 = CARTESIAN_POINT('',(1.445468495627,-1.90983622455));
#514 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#515 = ORIENTED_EDGE('',*,*,#516,.T.);
#516 = EDGE_CURVE('',#427,#262,#517,.T.);
#517 = SURFACE_CURVE('',#518,(#522,#529),.PCURVE_S2.);
#518 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#519,#520,#521),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(0.E+000,
3.554299705008),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.010587075051,1.)) REPRESENTATION_ITEM('') );
#519 = CARTESIAN_POINT('',(-27.8196811084,-2.076297072243,5.43633));
#520 = CARTESIAN_POINT('',(-26.3759852869,-2.076297072243,4.003957457804
    ));
#521 = CARTESIAN_POINT('',(-25.29321342079,-2.076297072243,2.93633));
#522 = PCURVE('',#272,#523);
#523 = DEFINITIONAL_REPRESENTATION('',(#524),#528);
#524 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#525,#526,#527),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((3,3),(0.E+000,
3.554299705008),.PIECEWISE_BEZIER_KNOTS.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,
1.010587075051,1.)) REPRESENTATION_ITEM('') );
#525 = CARTESIAN_POINT('',(2.5,5.078432583508));
#526 = CARTESIAN_POINT('',(3.932372542196,6.522128405011));
#527 = CARTESIAN_POINT('',(5.,7.604900271125));
#528 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#529 = PCURVE('',#153,#530);
#530 = DEFINITIONAL_REPRESENTATION('',(#531),#557);
#531 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#532,#533,#534,#535,#536,#537,
    #538,#539,#540,#541,#542,#543,#544,#545,#546,#547,#548,#549,#550,
    #551,#552,#553,#554,#555,#556),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,
    1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.1615590775,
    0.323118155001,0.484677232501,0.646236310001,0.807795387502,
    0.969354465002,1.130913542503,1.292472620003,1.454031697503,
    1.615590775004,1.777149852504,1.938708930004,2.100268007505,
    2.261827085005,2.423386162505,2.584945240006,2.746504317506,
    2.908063395007,3.069622472507,3.231181550007,3.392740627508,
    3.554299705008),.QUASI_UNIFORM_KNOTS.);
#532 = CARTESIAN_POINT('',(3.016264822422,-17.5));
#533 = CARTESIAN_POINT('',(3.015712177589,-17.45613532843));
#534 = CARTESIAN_POINT('',(3.014600150241,-17.369034253));
#535 = CARTESIAN_POINT('',(3.012911513941,-17.24022922767));
#536 = CARTESIAN_POINT('',(3.011201903801,-17.11323215337));
#537 = CARTESIAN_POINT('',(3.00947092842,-16.98800586851));
#538 = CARTESIAN_POINT('',(3.007718187623,-16.86451413191));
#539 = CARTESIAN_POINT('',(3.005943270986,-16.74272172673));
#540 = CARTESIAN_POINT('',(3.004145757844,-16.62259438981));
#541 = CARTESIAN_POINT('',(3.002325216878,-16.5040987895));
#542 = CARTESIAN_POINT('',(3.000481205806,-16.38720249214));
#543 = CARTESIAN_POINT('',(2.998613271026,-16.27187393322));
#544 = CARTESIAN_POINT('',(2.996720947259,-16.15808238872));
#545 = CARTESIAN_POINT('',(2.994803757168,-16.04579794794));
#546 = CARTESIAN_POINT('',(2.992861210974,-15.93499148723));
#547 = CARTESIAN_POINT('',(2.990892806047,-15.82563464481));
#548 = CARTESIAN_POINT('',(2.988898026486,-15.7176997966));
#549 = CARTESIAN_POINT('',(2.986876342681,-15.61116003298));
#550 = CARTESIAN_POINT('',(2.984827210868,-15.50598913612));
#551 = CARTESIAN_POINT('',(2.982750072614,-15.40216155983));
#552 = CARTESIAN_POINT('',(2.980644354484,-15.29965240364));
#553 = CARTESIAN_POINT('',(2.978509466942,-15.19843741256));
#554 = CARTESIAN_POINT('',(2.976344806,-15.09849288457));
#555 = CARTESIAN_POINT('',(2.974881431694,-15.03269491212));
#556 = CARTESIAN_POINT('',(2.97414457437,-15.));
#557 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#558 = ADVANCED_FACE('',(#559),#203,.F.);
#559 = FACE_BOUND('',#560,.F.);
#560 = EDGE_LOOP('',(#561,#562,#612,#660,#661));
#561 = ORIENTED_EDGE('',*,*,#187,.F.);
#562 = ORIENTED_EDGE('',*,*,#563,.T.);
#563 = EDGE_CURVE('',#138,#564,#566,.T.);
#564 = VERTEX_POINT('',#565);
#565 = CARTESIAN_POINT('',(-10.39811369191,-0.826297072243,2.93633));
#566 = SURFACE_CURVE('',#567,(#572,#583),.PCURVE_S2.);
#567 = CIRCLE('',#568,7.5);
#568 = AXIS2_PLACEMENT_3D('',#569,#570,#571);
#569 = CARTESIAN_POINT('',(-17.89811369191,-0.826297072243,2.93633));
#570 = DIRECTION('',(0.E+000,0.E+000,-1.));
#571 = DIRECTION('',(1.,0.E+000,0.E+000));
#572 = PCURVE('',#203,#573);
#573 = DEFINITIONAL_REPRESENTATION('',(#574),#582);
#574 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#575,#576,#577,#578,#579,#580
,#581),.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.E+000,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM('
') );
#575 = CARTESIAN_POINT('',(-7.5,1.25));
#576 = CARTESIAN_POINT('',(-7.5,14.240381056767));
#577 = CARTESIAN_POINT('',(-18.75,7.745190528383));
#578 = CARTESIAN_POINT('',(-30.,1.25));
#579 = CARTESIAN_POINT('',(-18.75,-5.245190528383));
#580 = CARTESIAN_POINT('',(-7.5,-11.74038105676));
#581 = CARTESIAN_POINT('',(-7.5,1.25));
#582 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#583 = PCURVE('',#153,#584);
#584 = DEFINITIONAL_REPRESENTATION('',(#585),#611);
#585 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#586,#587,#588,#589,#590,#591,
    #592,#593,#594,#595,#596,#597,#598,#599,#600,#601,#602,#603,#604,
    #605,#606,#607,#608,#609,#610),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,
    1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(6.11573722796,6.123348504288,
    6.130959780616,6.138571056944,6.146182333273,6.153793609601,
    6.161404885929,6.169016162257,6.176627438585,6.184238714913,
    6.191849991242,6.19946126757,6.207072543898,6.214683820226,
    6.222295096554,6.229906372882,6.237517649211,6.245128925539,
    6.252740201867,6.260351478195,6.267962754523,6.275574030851,
    6.28318530718),.QUASI_UNIFORM_KNOTS.);
#586 = CARTESIAN_POINT('',(6.11573722796,-15.));
#587 = CARTESIAN_POINT('',(6.118274320069,-15.));
#588 = CARTESIAN_POINT('',(6.123348504288,-15.));
#589 = CARTESIAN_POINT('',(6.130959780616,-15.));
#590 = CARTESIAN_POINT('',(6.138571056944,-15.));
#591 = CARTESIAN_POINT('',(6.146182333273,-15.));
#592 = CARTESIAN_POINT('',(6.153793609601,-15.));
#593 = CARTESIAN_POINT('',(6.161404885929,-15.));
#594 = CARTESIAN_POINT('',(6.169016162257,-15.));
#595 = CARTESIAN_POINT('',(6.176627438585,-15.));
#596 = CARTESIAN_POINT('',(6.184238714913,-15.));
#597 = CARTESIAN_POINT('',(6.191849991242,-15.));
#598 = CARTESIAN_POINT('',(6.19946126757,-15.));
#599 = CARTESIAN_POINT('',(6.207072543898,-15.));
#600 = CARTESIAN_POINT('',(6.214683820226,-15.));
#601 = CARTESIAN_POINT('',(6.222295096554,-15.));
#602 = CARTESIAN_POINT('',(6.229906372882,-15.));
#603 = CARTESIAN_POINT('',(6.237517649211,-15.));
#604 = CARTESIAN_POINT('',(6.245128925539,-15.));
#605 = CARTESIAN_POINT('',(6.252740201867,-15.));
#606 = CARTESIAN_POINT('',(6.260351478195,-15.));
#607 = CARTESIAN_POINT('',(6.267962754523,-15.));
#608 = CARTESIAN_POINT('',(6.275574030851,-15.));
#609 = CARTESIAN_POINT('',(6.28064821507,-15.));
#610 = CARTESIAN_POINT('',(6.28318530718,-15.));
#611 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#612 = ORIENTED_EDGE('',*,*,#613,.T.);
#613 = EDGE_CURVE('',#564,#264,#614,.T.);
#614 = SURFACE_CURVE('',#615,(#620,#631),.PCURVE_S2.);
#615 = CIRCLE('',#616,7.5);
#616 = AXIS2_PLACEMENT_3D('',#617,#618,#619);
#617 = CARTESIAN_POINT('',(-17.89811369191,-0.826297072243,2.93633));
#618 = DIRECTION('',(0.E+000,0.E+000,-1.));
#619 = DIRECTION('',(1.,0.E+000,0.E+000));
#620 = PCURVE('',#203,#621);
#621 = DEFINITIONAL_REPRESENTATION('',(#622),#630);
#622 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#623,#624,#625,#626,#627,#628
,#629),.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.E+000,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM('
') );
#623 = CARTESIAN_POINT('',(-7.5,1.25));
#624 = CARTESIAN_POINT('',(-7.5,14.240381056767));
#625 = CARTESIAN_POINT('',(-18.75,7.745190528383));
#626 = CARTESIAN_POINT('',(-30.,1.25));
#627 = CARTESIAN_POINT('',(-18.75,-5.245190528383));
#628 = CARTESIAN_POINT('',(-7.5,-11.74038105676));
#629 = CARTESIAN_POINT('',(-7.5,1.25));
#630 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#631 = PCURVE('',#153,#632);
#632 = DEFINITIONAL_REPRESENTATION('',(#633),#659);
#633 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#634,#635,#636,#637,#638,#639,
    #640,#641,#642,#643,#644,#645,#646,#647,#648,#649,#650,#651,#652,
    #653,#654,#655,#656,#657,#658),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,
    1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(8.881784197001E-016,
    7.611276328169E-003,1.522255265634E-002,2.28338289845E-002,
    3.044510531267E-002,3.805638164084E-002,4.566765796901E-002,
    5.327893429717E-002,6.089021062534E-002,6.850148695351E-002,
    7.611276328168E-002,8.372403960985E-002,9.133531593801E-002,
    9.894659226618E-002,0.106557868594,0.114169144923,0.121780421251,
    0.129391697579,0.137002973907,0.144614250235,0.152225526563,
    0.159836802892,0.16744807922),.QUASI_UNIFORM_KNOTS.);
#634 = CARTESIAN_POINT('',(1.021405182655E-015,-15.));
#635 = CARTESIAN_POINT('',(2.53709210939E-003,-15.));
#636 = CARTESIAN_POINT('',(7.611276328169E-003,-15.));
#637 = CARTESIAN_POINT('',(1.522255265634E-002,-15.));
#638 = CARTESIAN_POINT('',(2.28338289845E-002,-15.));
#639 = CARTESIAN_POINT('',(3.044510531267E-002,-15.));
#640 = CARTESIAN_POINT('',(3.805638164084E-002,-15.));
#641 = CARTESIAN_POINT('',(4.566765796901E-002,-15.));
#642 = CARTESIAN_POINT('',(5.327893429717E-002,-15.));
#643 = CARTESIAN_POINT('',(6.089021062534E-002,-15.));
#644 = CARTESIAN_POINT('',(6.850148695351E-002,-15.));
#645 = CARTESIAN_POINT('',(7.611276328168E-002,-15.));
#646 = CARTESIAN_POINT('',(8.372403960985E-002,-15.));
#647 = CARTESIAN_POINT('',(9.133531593801E-002,-15.));
#648 = CARTESIAN_POINT('',(9.894659226618E-002,-15.));
#649 = CARTESIAN_POINT('',(0.106557868594,-15.));
#650 = CARTESIAN_POINT('',(0.114169144923,-15.));
#651 = CARTESIAN_POINT('',(0.121780421251,-15.));
#652 = CARTESIAN_POINT('',(0.129391697579,-15.));
#653 = CARTESIAN_POINT('',(0.137002973907,-15.));
#654 = CARTESIAN_POINT('',(0.144614250235,-15.));
#655 = CARTESIAN_POINT('',(0.152225526563,-15.));
#656 = CARTESIAN_POINT('',(0.159836802892,-15.));
#657 = CARTESIAN_POINT('',(0.16491098711,-15.));
#658 = CARTESIAN_POINT('',(0.16744807922,-15.));
#659 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#660 = ORIENTED_EDGE('',*,*,#261,.F.);
#661 = ORIENTED_EDGE('',*,*,#662,.T.);
#662 = EDGE_CURVE('',#262,#188,#663,.T.);
#663 = SURFACE_CURVE('',#664,(#669,#680),.PCURVE_S2.);
#664 = CIRCLE('',#665,7.5);
#665 = AXIS2_PLACEMENT_3D('',#666,#667,#668);
#666 = CARTESIAN_POINT('',(-17.89811369191,-0.826297072243,2.93633));
#667 = DIRECTION('',(0.E+000,0.E+000,-1.));
#668 = DIRECTION('',(1.,0.E+000,0.E+000));
#669 = PCURVE('',#203,#670);
#670 = DEFINITIONAL_REPRESENTATION('',(#671),#679);
#671 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#672,#673,#674,#675,#676,#677
,#678),.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2,2,2,2,1),(
    -2.094395102393,0.E+000,2.094395102393,4.188790204786,6.28318530718,
8.377580409573),.UNSPECIFIED.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,1.,0.5,1.)) REPRESENTATION_ITEM('
') );
#672 = CARTESIAN_POINT('',(-7.5,1.25));
#673 = CARTESIAN_POINT('',(-7.5,14.240381056767));
#674 = CARTESIAN_POINT('',(-18.75,7.745190528383));
#675 = CARTESIAN_POINT('',(-30.,1.25));
#676 = CARTESIAN_POINT('',(-18.75,-5.245190528383));
#677 = CARTESIAN_POINT('',(-7.5,-11.74038105676));
#678 = CARTESIAN_POINT('',(-7.5,1.25));
#679 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#680 = PCURVE('',#153,#681);
#681 = DEFINITIONAL_REPRESENTATION('',(#682),#708);
#682 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#683,#684,#685,#686,#687,#688,
    #689,#690,#691,#692,#693,#694,#695,#696,#697,#698,#699,#700,#701,
    #702,#703,#704,#705,#706,#707),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,
    1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(2.97414457437,2.989367127026,
    3.004589679683,3.019812232339,3.035034784995,3.050257337652,
    3.065479890308,3.080702442964,3.095924995621,3.111147548277,
    3.126370100933,3.14159265359,3.156815206246,3.172037758902,
    3.187260311559,3.202482864215,3.217705416871,3.232927969528,
    3.248150522184,3.26337307484,3.278595627497,3.293818180153,
    3.309040732809),.QUASI_UNIFORM_KNOTS.);
#683 = CARTESIAN_POINT('',(2.97414457437,-15.));
#684 = CARTESIAN_POINT('',(2.979218758589,-15.));
#685 = CARTESIAN_POINT('',(2.989367127026,-15.));
#686 = CARTESIAN_POINT('',(3.004589679683,-15.));
#687 = CARTESIAN_POINT('',(3.019812232339,-15.));
#688 = CARTESIAN_POINT('',(3.035034784995,-15.));
#689 = CARTESIAN_POINT('',(3.050257337652,-15.));
#690 = CARTESIAN_POINT('',(3.065479890308,-15.));
#691 = CARTESIAN_POINT('',(3.080702442964,-15.));
#692 = CARTESIAN_POINT('',(3.095924995621,-15.));
#693 = CARTESIAN_POINT('',(3.111147548277,-15.));
#694 = CARTESIAN_POINT('',(3.126370100933,-15.));
#695 = CARTESIAN_POINT('',(3.14159265359,-15.));
#696 = CARTESIAN_POINT('',(3.156815206246,-15.));
#697 = CARTESIAN_POINT('',(3.172037758902,-15.));
#698 = CARTESIAN_POINT('',(3.187260311559,-15.));
#699 = CARTESIAN_POINT('',(3.202482864215,-15.));
#700 = CARTESIAN_POINT('',(3.217705416871,-15.));
#701 = CARTESIAN_POINT('',(3.232927969528,-15.));
#702 = CARTESIAN_POINT('',(3.248150522184,-15.));
#703 = CARTESIAN_POINT('',(3.26337307484,-15.));
#704 = CARTESIAN_POINT('',(3.278595627497,-15.));
#705 = CARTESIAN_POINT('',(3.293818180153,-15.));
#706 = CARTESIAN_POINT('',(3.303966548591,-15.));
#707 = CARTESIAN_POINT('',(3.309040732809,-15.));
#708 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#709 = ADVANCED_FACE('',(#710),#482,.F.);
#710 = FACE_BOUND('',#711,.T.);
#711 = EDGE_LOOP('',(#712,#713,#781));
#712 = ORIENTED_EDGE('',*,*,#426,.T.);
#713 = ORIENTED_EDGE('',*,*,#714,.F.);
#714 = EDGE_CURVE('',#715,#427,#717,.T.);
#715 = VERTEX_POINT('',#716);
#716 = CARTESIAN_POINT('',(-17.89811369191,-10.82629707224,5.43633));
#717 = SURFACE_CURVE('',#718,(#723,#752),.PCURVE_S2.);
#718 = CIRCLE('',#719,10.);
#719 = AXIS2_PLACEMENT_3D('',#720,#721,#722);
#720 = CARTESIAN_POINT('',(-17.89811369191,-0.826297072243,5.43633));
#721 = DIRECTION('',(0.E+000,-2.22044604925E-016,-1.));
#722 = DIRECTION('',(1.,0.E+000,0.E+000));
#723 = PCURVE('',#482,#724);
#724 = DEFINITIONAL_REPRESENTATION('',(#725),#751);
#725 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#726,#727,#728,#729,#730,#731,
    #732,#733,#734,#735,#736,#737,#738,#739,#740,#741,#742,#743,#744,
    #745,#746,#747,#748,#749,#750),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,
    1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(1.570796326795,1.636499440232,
    1.70220255367,1.767905667108,1.833608780545,1.899311893983,
    1.96501500742,2.030718120858,2.096421234296,2.162124347733,
    2.227827461171,2.293530574608,2.359233688046,2.424936801483,
    2.490639914921,2.556343028359,2.622046141796,2.687749255234,
    2.753452368671,2.819155482109,2.884858595547,2.950561708984,
    3.016264822422),.QUASI_UNIFORM_KNOTS.);
#726 = CARTESIAN_POINT('',(0.E+000,-1.90983622455));
#727 = CARTESIAN_POINT('',(2.190103781253E-002,-1.90983622455));
#728 = CARTESIAN_POINT('',(6.570311343758E-002,-1.90983622455));
#729 = CARTESIAN_POINT('',(0.131406226875,-1.90983622455));
#730 = CARTESIAN_POINT('',(0.197109340313,-1.90983622455));
#731 = CARTESIAN_POINT('',(0.26281245375,-1.90983622455));
#732 = CARTESIAN_POINT('',(0.328515567188,-1.90983622455));
#733 = CARTESIAN_POINT('',(0.394218680625,-1.90983622455));
#734 = CARTESIAN_POINT('',(0.459921794063,-1.90983622455));
#735 = CARTESIAN_POINT('',(0.525624907501,-1.90983622455));
#736 = CARTESIAN_POINT('',(0.591328020938,-1.90983622455));
#737 = CARTESIAN_POINT('',(0.657031134376,-1.90983622455));
#738 = CARTESIAN_POINT('',(0.722734247813,-1.90983622455));
#739 = CARTESIAN_POINT('',(0.788437361251,-1.90983622455));
#740 = CARTESIAN_POINT('',(0.854140474689,-1.90983622455));
#741 = CARTESIAN_POINT('',(0.919843588126,-1.90983622455));
#742 = CARTESIAN_POINT('',(0.985546701564,-1.90983622455));
#743 = CARTESIAN_POINT('',(1.051249815001,-1.90983622455));
#744 = CARTESIAN_POINT('',(1.116952928439,-1.90983622455));
#745 = CARTESIAN_POINT('',(1.182656041876,-1.90983622455));
#746 = CARTESIAN_POINT('',(1.248359155314,-1.90983622455));
#747 = CARTESIAN_POINT('',(1.314062268752,-1.90983622455));
#748 = CARTESIAN_POINT('',(1.379765382189,-1.90983622455));
#749 = CARTESIAN_POINT('',(1.423567457814,-1.90983622455));
#750 = CARTESIAN_POINT('',(1.445468495627,-1.90983622455));
#751 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#752 = PCURVE('',#153,#753);
#753 = DEFINITIONAL_REPRESENTATION('',(#754),#780);
#754 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#755,#756,#757,#758,#759,#760,
    #761,#762,#763,#764,#765,#766,#767,#768,#769,#770,#771,#772,#773,
    #774,#775,#776,#777,#778,#779),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,
    1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(1.570796326795,1.636499440232,
    1.70220255367,1.767905667108,1.833608780545,1.899311893983,
    1.96501500742,2.030718120858,2.096421234296,2.162124347733,
    2.227827461171,2.293530574608,2.359233688046,2.424936801483,
    2.490639914921,2.556343028359,2.622046141796,2.687749255234,
    2.753452368671,2.819155482109,2.884858595547,2.950561708984,
    3.016264822422),.QUASI_UNIFORM_KNOTS.);
#755 = CARTESIAN_POINT('',(1.570796326795,-17.5));
#756 = CARTESIAN_POINT('',(1.592697364607,-17.5));
#757 = CARTESIAN_POINT('',(1.636499440232,-17.5));
#758 = CARTESIAN_POINT('',(1.70220255367,-17.5));
#759 = CARTESIAN_POINT('',(1.767905667108,-17.5));
#760 = CARTESIAN_POINT('',(1.833608780545,-17.5));
#761 = CARTESIAN_POINT('',(1.899311893983,-17.5));
#762 = CARTESIAN_POINT('',(1.96501500742,-17.5));
#763 = CARTESIAN_POINT('',(2.030718120858,-17.5));
#764 = CARTESIAN_POINT('',(2.096421234296,-17.5));
#765 = CARTESIAN_POINT('',(2.162124347733,-17.5));
#766 = CARTESIAN_POINT('',(2.227827461171,-17.5));
#767 = CARTESIAN_POINT('',(2.293530574608,-17.5));
#768 = CARTESIAN_POINT('',(2.359233688046,-17.5));
#769 = CARTESIAN_POINT('',(2.424936801483,-17.5));
#770 = CARTESIAN_POINT('',(2.490639914921,-17.5));
#771 = CARTESIAN_POINT('',(2.556343028359,-17.5));
#772 = CARTESIAN_POINT('',(2.622046141796,-17.5));
#773 = CARTESIAN_POINT('',(2.687749255234,-17.5));
#774 = CARTESIAN_POINT('',(2.753452368671,-17.5));
#775 = CARTESIAN_POINT('',(2.819155482109,-17.5));
#776 = CARTESIAN_POINT('',(2.884858595547,-17.5));
#777 = CARTESIAN_POINT('',(2.950561708984,-17.5));
#778 = CARTESIAN_POINT('',(2.994363784609,-17.5));
#779 = CARTESIAN_POINT('',(3.016264822422,-17.5));
#780 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#781 = ORIENTED_EDGE('',*,*,#782,.F.);
#782 = EDGE_CURVE('',#337,#715,#783,.T.);
#783 = SURFACE_CURVE('',#784,(#789,#796),.PCURVE_S2.);
#784 = CIRCLE('',#785,54.873718663856);
#785 = AXIS2_PLACEMENT_3D('',#786,#787,#788);
#786 = CARTESIAN_POINT('',(-17.89811369191,7.423702927757,-46.31367));
#787 = DIRECTION('',(1.,0.E+000,0.E+000));
#788 = DIRECTION('',(0.E+000,1.,-2.22044604925E-016));
#789 = PCURVE('',#482,#790);
#790 = DEFINITIONAL_REPRESENTATION('',(#791),#795);
#791 = LINE('',#792,#793);
#792 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#793 = VECTOR('',#794,1.);
#794 = DIRECTION('',(0.E+000,-1.));
#795 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#796 = PCURVE('',#392,#797);
#797 = DEFINITIONAL_REPRESENTATION('',(#798),#802);
#798 = LINE('',#799,#800);
#799 = CARTESIAN_POINT('',(6.28318530718,0.E+000));
#800 = VECTOR('',#801,1.);
#801 = DIRECTION('',(0.E+000,-1.));
#802 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#803 = ADVANCED_FACE('',(#804),#392,.F.);
#804 = FACE_BOUND('',#805,.T.);
#805 = EDGE_LOOP('',(#806,#872,#873));
#806 = ORIENTED_EDGE('',*,*,#807,.F.);
#807 = EDGE_CURVE('',#292,#715,#808,.T.);
#808 = SURFACE_CURVE('',#809,(#814,#843),.PCURVE_S2.);
#809 = CIRCLE('',#810,10.);
#810 = AXIS2_PLACEMENT_3D('',#811,#812,#813);
#811 = CARTESIAN_POINT('',(-17.89811369191,-0.826297072243,5.43633));
#812 = DIRECTION('',(0.E+000,-2.22044604925E-016,-1.));
#813 = DIRECTION('',(1.,0.E+000,0.E+000));
#814 = PCURVE('',#392,#815);
#815 = DEFINITIONAL_REPRESENTATION('',(#816),#842);
#816 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#817,#818,#819,#820,#821,#822,
    #823,#824,#825,#826,#827,#828,#829,#830,#831,#832,#833,#834,#835,
    #836,#837,#838,#839,#840,#841),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,
    1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.125327831168,0.191030944606,
    0.256734058043,0.322437171481,0.388140284918,0.453843398356,
    0.519546511794,0.585249625231,0.650952738669,0.716655852106,
    0.782358965544,0.848062078981,0.913765192419,0.979468305857,
    1.045171419294,1.110874532732,1.176577646169,1.242280759607,
    1.307983873045,1.373686986482,1.43939009992,1.505093213357,
    1.570796326795),.QUASI_UNIFORM_KNOTS.);
#817 = CARTESIAN_POINT('',(4.837716811553,-1.90983622455));
#818 = CARTESIAN_POINT('',(4.859617849365,-1.90983622455));
#819 = CARTESIAN_POINT('',(4.90341992499,-1.90983622455));
#820 = CARTESIAN_POINT('',(4.969123038428,-1.90983622455));
#821 = CARTESIAN_POINT('',(5.034826151866,-1.90983622455));
#822 = CARTESIAN_POINT('',(5.100529265303,-1.90983622455));
#823 = CARTESIAN_POINT('',(5.166232378741,-1.90983622455));
#824 = CARTESIAN_POINT('',(5.231935492178,-1.90983622455));
#825 = CARTESIAN_POINT('',(5.297638605616,-1.90983622455));
#826 = CARTESIAN_POINT('',(5.363341719053,-1.90983622455));
#827 = CARTESIAN_POINT('',(5.429044832491,-1.90983622455));
#828 = CARTESIAN_POINT('',(5.494747945929,-1.90983622455));
#829 = CARTESIAN_POINT('',(5.560451059366,-1.90983622455));
#830 = CARTESIAN_POINT('',(5.626154172804,-1.90983622455));
#831 = CARTESIAN_POINT('',(5.691857286241,-1.90983622455));
#832 = CARTESIAN_POINT('',(5.757560399679,-1.90983622455));
#833 = CARTESIAN_POINT('',(5.823263513117,-1.90983622455));
#834 = CARTESIAN_POINT('',(5.888966626554,-1.90983622455));
#835 = CARTESIAN_POINT('',(5.954669739992,-1.90983622455));
#836 = CARTESIAN_POINT('',(6.020372853429,-1.90983622455));
#837 = CARTESIAN_POINT('',(6.086075966867,-1.90983622455));
#838 = CARTESIAN_POINT('',(6.151779080304,-1.90983622455));
#839 = CARTESIAN_POINT('',(6.217482193742,-1.90983622455));
#840 = CARTESIAN_POINT('',(6.261284269367,-1.90983622455));
#841 = CARTESIAN_POINT('',(6.28318530718,-1.90983622455));
#842 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#843 = PCURVE('',#153,#844);
#844 = DEFINITIONAL_REPRESENTATION('',(#845),#871);
#845 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#846,#847,#848,#849,#850,#851,
    #852,#853,#854,#855,#856,#857,#858,#859,#860,#861,#862,#863,#864,
    #865,#866,#867,#868,#869,#870),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,
    1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.125327831168,0.191030944606,
    0.256734058043,0.322437171481,0.388140284918,0.453843398356,
    0.519546511794,0.585249625231,0.650952738669,0.716655852106,
    0.782358965544,0.848062078981,0.913765192419,0.979468305857,
    1.045171419294,1.110874532732,1.176577646169,1.242280759607,
    1.307983873045,1.373686986482,1.43939009992,1.505093213357,
    1.570796326795),.QUASI_UNIFORM_KNOTS.);
#846 = CARTESIAN_POINT('',(0.125327831168,-17.5));
#847 = CARTESIAN_POINT('',(0.147228868981,-17.5));
#848 = CARTESIAN_POINT('',(0.191030944606,-17.5));
#849 = CARTESIAN_POINT('',(0.256734058043,-17.5));
#850 = CARTESIAN_POINT('',(0.322437171481,-17.5));
#851 = CARTESIAN_POINT('',(0.388140284918,-17.5));
#852 = CARTESIAN_POINT('',(0.453843398356,-17.5));
#853 = CARTESIAN_POINT('',(0.519546511794,-17.5));
#854 = CARTESIAN_POINT('',(0.585249625231,-17.5));
#855 = CARTESIAN_POINT('',(0.650952738669,-17.5));
#856 = CARTESIAN_POINT('',(0.716655852106,-17.5));
#857 = CARTESIAN_POINT('',(0.782358965544,-17.5));
#858 = CARTESIAN_POINT('',(0.848062078981,-17.5));
#859 = CARTESIAN_POINT('',(0.913765192419,-17.5));
#860 = CARTESIAN_POINT('',(0.979468305857,-17.5));
#861 = CARTESIAN_POINT('',(1.045171419294,-17.5));
#862 = CARTESIAN_POINT('',(1.110874532732,-17.5));
#863 = CARTESIAN_POINT('',(1.176577646169,-17.5));
#864 = CARTESIAN_POINT('',(1.242280759607,-17.5));
#865 = CARTESIAN_POINT('',(1.307983873045,-17.5));
#866 = CARTESIAN_POINT('',(1.373686986482,-17.5));
#867 = CARTESIAN_POINT('',(1.43939009992,-17.5));
#868 = CARTESIAN_POINT('',(1.505093213357,-17.5));
#869 = CARTESIAN_POINT('',(1.548895288982,-17.5));
#870 = CARTESIAN_POINT('',(1.570796326795,-17.5));
#871 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#872 = ORIENTED_EDGE('',*,*,#336,.T.);
#873 = ORIENTED_EDGE('',*,*,#782,.T.);
#874 = ADVANCED_FACE('',(#875),#81,.F.);
#875 = FACE_BOUND('',#876,.T.);
#876 = EDGE_LOOP('',(#877,#878));
#877 = ORIENTED_EDGE('',*,*,#18,.T.);
#878 = ORIENTED_EDGE('',*,*,#879,.F.);
#879 = EDGE_CURVE('',#19,#21,#880,.T.);
#880 = SURFACE_CURVE('',#881,(#886,#915),.PCURVE_S2.);
#881 = CIRCLE('',#882,10.);
#882 = AXIS2_PLACEMENT_3D('',#883,#884,#885);
#883 = CARTESIAN_POINT('',(-17.89811369191,-0.826297072243,5.43633));
#884 = DIRECTION('',(0.E+000,-2.22044604925E-016,-1.));
#885 = DIRECTION('',(1.,0.E+000,0.E+000));
#886 = PCURVE('',#81,#887);
#887 = DEFINITIONAL_REPRESENTATION('',(#888),#914);
#888 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#889,#890,#891,#892,#893,#894,
    #895,#896,#897,#898,#899,#900,#901,#902,#903,#904,#905,#906,#907,
    #908,#909,#910,#911,#912,#913),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,
    1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(3.266920484758,3.398326711633,
    3.529732938508,3.661139165383,3.792545392259,3.923951619134,
    4.055357846009,4.186764072884,4.318170299759,4.449576526634,
    4.58098275351,4.712388980385,4.84379520726,4.975201434135,
    5.10660766101,5.238013887885,5.369420114761,5.500826341636,
    5.632232568511,5.763638795386,5.895045022261,6.026451249136,
    6.157857476012),.QUASI_UNIFORM_KNOTS.);
#889 = CARTESIAN_POINT('',(1.696124157963,-1.90983622455));
#890 = CARTESIAN_POINT('',(1.739926233588,-1.90983622455));
#891 = CARTESIAN_POINT('',(1.827530384838,-1.90983622455));
#892 = CARTESIAN_POINT('',(1.958936611713,-1.90983622455));
#893 = CARTESIAN_POINT('',(2.090342838588,-1.90983622455));
#894 = CARTESIAN_POINT('',(2.221749065464,-1.90983622455));
#895 = CARTESIAN_POINT('',(2.353155292339,-1.90983622455));
#896 = CARTESIAN_POINT('',(2.484561519214,-1.90983622455));
#897 = CARTESIAN_POINT('',(2.615967746089,-1.90983622455));
#898 = CARTESIAN_POINT('',(2.747373972964,-1.90983622455));
#899 = CARTESIAN_POINT('',(2.878780199839,-1.90983622455));
#900 = CARTESIAN_POINT('',(3.010186426715,-1.90983622455));
#901 = CARTESIAN_POINT('',(3.14159265359,-1.90983622455));
#902 = CARTESIAN_POINT('',(3.272998880465,-1.90983622455));
#903 = CARTESIAN_POINT('',(3.40440510734,-1.90983622455));
#904 = CARTESIAN_POINT('',(3.535811334215,-1.90983622455));
#905 = CARTESIAN_POINT('',(3.66721756109,-1.90983622455));
#906 = CARTESIAN_POINT('',(3.798623787966,-1.90983622455));
#907 = CARTESIAN_POINT('',(3.930030014841,-1.90983622455));
#908 = CARTESIAN_POINT('',(4.061436241716,-1.90983622455));
#909 = CARTESIAN_POINT('',(4.192842468591,-1.90983622455));
#910 = CARTESIAN_POINT('',(4.324248695466,-1.90983622455));
#911 = CARTESIAN_POINT('',(4.455654922341,-1.90983622455));
#912 = CARTESIAN_POINT('',(4.543259073592,-1.90983622455));
#913 = CARTESIAN_POINT('',(4.587061149217,-1.90983622455));
#914 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#915 = PCURVE('',#153,#916);
#916 = DEFINITIONAL_REPRESENTATION('',(#917),#943);
#917 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#918,#919,#920,#921,#922,#923,
    #924,#925,#926,#927,#928,#929,#930,#931,#932,#933,#934,#935,#936,
    #937,#938,#939,#940,#941,#942),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,
    1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(3.266920484758,3.398326711633,
    3.529732938508,3.661139165383,3.792545392259,3.923951619134,
    4.055357846009,4.186764072884,4.318170299759,4.449576526634,
    4.58098275351,4.712388980385,4.84379520726,4.975201434135,
    5.10660766101,5.238013887885,5.369420114761,5.500826341636,
    5.632232568511,5.763638795386,5.895045022261,6.026451249136,
    6.157857476012),.QUASI_UNIFORM_KNOTS.);
#918 = CARTESIAN_POINT('',(3.266920484758,-17.5));
#919 = CARTESIAN_POINT('',(3.310722560383,-17.5));
#920 = CARTESIAN_POINT('',(3.398326711633,-17.5));
#921 = CARTESIAN_POINT('',(3.529732938508,-17.5));
#922 = CARTESIAN_POINT('',(3.661139165383,-17.5));
#923 = CARTESIAN_POINT('',(3.792545392259,-17.5));
#924 = CARTESIAN_POINT('',(3.923951619134,-17.5));
#925 = CARTESIAN_POINT('',(4.055357846009,-17.5));
#926 = CARTESIAN_POINT('',(4.186764072884,-17.5));
#927 = CARTESIAN_POINT('',(4.318170299759,-17.5));
#928 = CARTESIAN_POINT('',(4.449576526634,-17.5));
#929 = CARTESIAN_POINT('',(4.58098275351,-17.5));
#930 = CARTESIAN_POINT('',(4.712388980385,-17.5));
#931 = CARTESIAN_POINT('',(4.84379520726,-17.5));
#932 = CARTESIAN_POINT('',(4.975201434135,-17.5));
#933 = CARTESIAN_POINT('',(5.10660766101,-17.5));
#934 = CARTESIAN_POINT('',(5.238013887885,-17.5));
#935 = CARTESIAN_POINT('',(5.369420114761,-17.5));
#936 = CARTESIAN_POINT('',(5.500826341636,-17.5));
#937 = CARTESIAN_POINT('',(5.632232568511,-17.5));
#938 = CARTESIAN_POINT('',(5.763638795386,-17.5));
#939 = CARTESIAN_POINT('',(5.895045022261,-17.5));
#940 = CARTESIAN_POINT('',(6.026451249136,-17.5));
#941 = CARTESIAN_POINT('',(6.114055400386,-17.5));
#942 = CARTESIAN_POINT('',(6.157857476012,-17.5));
#943 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#944 = ADVANCED_FACE('',(#945),#153,.T.);
#945 = FACE_BOUND('',#946,.T.);
#946 = EDGE_LOOP('',(#947,#948,#949,#950,#951,#952,#953,#954,#955,#956,
    #957,#980,#1051));
#947 = ORIENTED_EDGE('',*,*,#613,.T.);
#948 = ORIENTED_EDGE('',*,*,#291,.T.);
#949 = ORIENTED_EDGE('',*,*,#807,.T.);
#950 = ORIENTED_EDGE('',*,*,#714,.T.);
#951 = ORIENTED_EDGE('',*,*,#516,.T.);
#952 = ORIENTED_EDGE('',*,*,#662,.T.);
#953 = ORIENTED_EDGE('',*,*,#215,.T.);
#954 = ORIENTED_EDGE('',*,*,#879,.T.);
#955 = ORIENTED_EDGE('',*,*,#137,.T.);
#956 = ORIENTED_EDGE('',*,*,#563,.T.);
#957 = ORIENTED_EDGE('',*,*,#958,.T.);
#958 = EDGE_CURVE('',#564,#959,#961,.T.);
#959 = VERTEX_POINT('',#960);
#960 = CARTESIAN_POINT('',(-12.89811369191,-0.826297072243,0.43633));
#961 = SEAM_CURVE('',#962,(#966,#973),.PCURVE_S2.);
#962 = LINE('',#963,#964);
#963 = CARTESIAN_POINT('',(-25.39811369191,-0.826297072243,-12.06367));
#964 = VECTOR('',#965,1.);
#965 = DIRECTION('',(-0.707106781187,-2.436019915756E-016,
    -0.707106781187));
#966 = PCURVE('',#153,#967);
#967 = DEFINITIONAL_REPRESENTATION('',(#968),#972);
#968 = LINE('',#969,#970);
#969 = CARTESIAN_POINT('',(6.28318530718,0.E+000));
#970 = VECTOR('',#971,1.);
#971 = DIRECTION('',(0.E+000,1.));
#972 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#973 = PCURVE('',#153,#974);
#974 = DEFINITIONAL_REPRESENTATION('',(#975),#979);
#975 = LINE('',#976,#977);
#976 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#977 = VECTOR('',#978,1.);
#978 = DIRECTION('',(0.E+000,1.));
#979 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#980 = ORIENTED_EDGE('',*,*,#981,.F.);
#981 = EDGE_CURVE('',#959,#959,#982,.T.);
#982 = SURFACE_CURVE('',#983,(#988,#1017),.PCURVE_S2.);
#983 = CIRCLE('',#984,5.);
#984 = AXIS2_PLACEMENT_3D('',#985,#986,#987);
#985 = CARTESIAN_POINT('',(-17.89811369191,-0.826297072243,0.43633));
#986 = DIRECTION('',(0.E+000,-2.22044604925E-016,-1.));
#987 = DIRECTION('',(1.,0.E+000,0.E+000));
#988 = PCURVE('',#153,#989);
#989 = DEFINITIONAL_REPRESENTATION('',(#990),#1016);
#990 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#991,#992,#993,#994,#995,#996,
    #997,#998,#999,#1000,#1001,#1002,#1003,#1004,#1005,#1006,#1007,#1008
    ,#1009,#1010,#1011,#1012,#1013,#1014,#1015),.UNSPECIFIED.,.F.,.F.,(4
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,
    0.285599332145,0.571198664289,0.856797996434,1.142397328578,
    1.427996660723,1.713595992867,1.999195325012,2.284794657156,
    2.570393989301,2.855993321445,3.14159265359,3.427191985734,
    3.712791317879,3.998390650023,4.283989982168,4.569589314312,
    4.855188646457,5.140787978601,5.426387310746,5.711986642891,
    5.997585975035,6.28318530718),.QUASI_UNIFORM_KNOTS.);
#991 = CARTESIAN_POINT('',(1.33226762955E-016,-12.5));
#992 = CARTESIAN_POINT('',(9.519977738151E-002,-12.5));
#993 = CARTESIAN_POINT('',(0.285599332145,-12.5));
#994 = CARTESIAN_POINT('',(0.571198664289,-12.5));
#995 = CARTESIAN_POINT('',(0.856797996434,-12.5));
#996 = CARTESIAN_POINT('',(1.142397328578,-12.5));
#997 = CARTESIAN_POINT('',(1.427996660723,-12.5));
#998 = CARTESIAN_POINT('',(1.713595992867,-12.5));
#999 = CARTESIAN_POINT('',(1.999195325012,-12.5));
#1000 = CARTESIAN_POINT('',(2.284794657156,-12.5));
#1001 = CARTESIAN_POINT('',(2.570393989301,-12.5));
#1002 = CARTESIAN_POINT('',(2.855993321445,-12.5));
#1003 = CARTESIAN_POINT('',(3.14159265359,-12.5));
#1004 = CARTESIAN_POINT('',(3.427191985734,-12.5));
#1005 = CARTESIAN_POINT('',(3.712791317879,-12.5));
#1006 = CARTESIAN_POINT('',(3.998390650023,-12.5));
#1007 = CARTESIAN_POINT('',(4.283989982168,-12.5));
#1008 = CARTESIAN_POINT('',(4.569589314312,-12.5));
#1009 = CARTESIAN_POINT('',(4.855188646457,-12.5));
#1010 = CARTESIAN_POINT('',(5.140787978601,-12.5));
#1011 = CARTESIAN_POINT('',(5.426387310746,-12.5));
#1012 = CARTESIAN_POINT('',(5.711986642891,-12.5));
#1013 = CARTESIAN_POINT('',(5.997585975035,-12.5));
#1014 = CARTESIAN_POINT('',(6.187985529798,-12.5));
#1015 = CARTESIAN_POINT('',(6.28318530718,-12.5));
#1016 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1017 = PCURVE('',#1018,#1023);
#1018 = CYLINDRICAL_SURFACE('',#1019,5.);
#1019 = AXIS2_PLACEMENT_3D('',#1020,#1021,#1022);
#1020 = CARTESIAN_POINT('',(-17.89811369191,-0.826297072243,-16.60362));
#1021 = DIRECTION('',(0.E+000,-2.22044604925E-016,-1.));
#1022 = DIRECTION('',(1.,0.E+000,0.E+000));
#1023 = DEFINITIONAL_REPRESENTATION('',(#1024),#1050);
#1024 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#1025,#1026,#1027,#1028,#1029,
    #1030,#1031,#1032,#1033,#1034,#1035,#1036,#1037,#1038,#1039,#1040,
    #1041,#1042,#1043,#1044,#1045,#1046,#1047,#1048,#1049),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),
  (0.E+000,0.285599332145,0.571198664289,0.856797996434,1.142397328578,
    1.427996660723,1.713595992867,1.999195325012,2.284794657156,
    2.570393989301,2.855993321445,3.14159265359,3.427191985734,
    3.712791317879,3.998390650023,4.283989982168,4.569589314312,
    4.855188646457,5.140787978601,5.426387310746,5.711986642891,
    5.997585975035,6.28318530718),.QUASI_UNIFORM_KNOTS.);
#1025 = CARTESIAN_POINT('',(0.E+000,-17.03995));
#1026 = CARTESIAN_POINT('',(9.519977738151E-002,-17.03995));
#1027 = CARTESIAN_POINT('',(0.285599332145,-17.03995));
#1028 = CARTESIAN_POINT('',(0.571198664289,-17.03995));
#1029 = CARTESIAN_POINT('',(0.856797996434,-17.03995));
#1030 = CARTESIAN_POINT('',(1.142397328578,-17.03995));
#1031 = CARTESIAN_POINT('',(1.427996660723,-17.03995));
#1032 = CARTESIAN_POINT('',(1.713595992867,-17.03995));
#1033 = CARTESIAN_POINT('',(1.999195325012,-17.03995));
#1034 = CARTESIAN_POINT('',(2.284794657156,-17.03995));
#1035 = CARTESIAN_POINT('',(2.570393989301,-17.03995));
#1036 = CARTESIAN_POINT('',(2.855993321445,-17.03995));
#1037 = CARTESIAN_POINT('',(3.14159265359,-17.03995));
#1038 = CARTESIAN_POINT('',(3.427191985734,-17.03995));
#1039 = CARTESIAN_POINT('',(3.712791317879,-17.03995));
#1040 = CARTESIAN_POINT('',(3.998390650023,-17.03995));
#1041 = CARTESIAN_POINT('',(4.283989982168,-17.03995));
#1042 = CARTESIAN_POINT('',(4.569589314312,-17.03995));
#1043 = CARTESIAN_POINT('',(4.855188646457,-17.03995));
#1044 = CARTESIAN_POINT('',(5.140787978601,-17.03995));
#1045 = CARTESIAN_POINT('',(5.426387310746,-17.03995));
#1046 = CARTESIAN_POINT('',(5.711986642891,-17.03995));
#1047 = CARTESIAN_POINT('',(5.997585975035,-17.03995));
#1048 = CARTESIAN_POINT('',(6.187985529798,-17.03995));
#1049 = CARTESIAN_POINT('',(6.28318530718,-17.03995));
#1050 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1051 = ORIENTED_EDGE('',*,*,#958,.F.);
#1052 = ADVANCED_FACE('',(#1053),#1018,.T.);
#1053 = FACE_BOUND('',#1054,.T.);
#1054 = EDGE_LOOP('',(#1055,#1128,#1149,#1150));
#1055 = ORIENTED_EDGE('',*,*,#1056,.F.);
#1056 = EDGE_CURVE('',#1057,#1057,#1059,.T.);
#1057 = VERTEX_POINT('',#1058);
#1058 = CARTESIAN_POINT('',(-12.89811369191,-0.826297072243,-33.64357));
#1059 = SURFACE_CURVE('',#1060,(#1065,#1094),.PCURVE_S2.);
#1060 = CIRCLE('',#1061,5.);
#1061 = AXIS2_PLACEMENT_3D('',#1062,#1063,#1064);
#1062 = CARTESIAN_POINT('',(-17.89811369191,-0.826297072243,-33.64357));
#1063 = DIRECTION('',(0.E+000,-2.22044604925E-016,-1.));
#1064 = DIRECTION('',(1.,0.E+000,0.E+000));
#1065 = PCURVE('',#1018,#1066);
#1066 = DEFINITIONAL_REPRESENTATION('',(#1067),#1093);
#1067 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#1068,#1069,#1070,#1071,#1072,
    #1073,#1074,#1075,#1076,#1077,#1078,#1079,#1080,#1081,#1082,#1083,
    #1084,#1085,#1086,#1087,#1088,#1089,#1090,#1091,#1092),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),
  (0.E+000,0.285599332145,0.571198664289,0.856797996434,1.142397328578,
    1.427996660723,1.713595992867,1.999195325012,2.284794657156,
    2.570393989301,2.855993321445,3.14159265359,3.427191985734,
    3.712791317879,3.998390650023,4.283989982168,4.569589314312,
    4.855188646457,5.140787978601,5.426387310746,5.711986642891,
    5.997585975035,6.28318530718),.QUASI_UNIFORM_KNOTS.);
#1068 = CARTESIAN_POINT('',(0.E+000,17.03995));
#1069 = CARTESIAN_POINT('',(9.519977738151E-002,17.03995));
#1070 = CARTESIAN_POINT('',(0.285599332145,17.03995));
#1071 = CARTESIAN_POINT('',(0.571198664289,17.03995));
#1072 = CARTESIAN_POINT('',(0.856797996434,17.03995));
#1073 = CARTESIAN_POINT('',(1.142397328578,17.03995));
#1074 = CARTESIAN_POINT('',(1.427996660723,17.03995));
#1075 = CARTESIAN_POINT('',(1.713595992867,17.03995));
#1076 = CARTESIAN_POINT('',(1.999195325012,17.03995));
#1077 = CARTESIAN_POINT('',(2.284794657156,17.03995));
#1078 = CARTESIAN_POINT('',(2.570393989301,17.03995));
#1079 = CARTESIAN_POINT('',(2.855993321445,17.03995));
#1080 = CARTESIAN_POINT('',(3.14159265359,17.03995));
#1081 = CARTESIAN_POINT('',(3.427191985734,17.03995));
#1082 = CARTESIAN_POINT('',(3.712791317879,17.03995));
#1083 = CARTESIAN_POINT('',(3.998390650023,17.03995));
#1084 = CARTESIAN_POINT('',(4.283989982168,17.03995));
#1085 = CARTESIAN_POINT('',(4.569589314312,17.03995));
#1086 = CARTESIAN_POINT('',(4.855188646457,17.03995));
#1087 = CARTESIAN_POINT('',(5.140787978601,17.03995));
#1088 = CARTESIAN_POINT('',(5.426387310746,17.03995));
#1089 = CARTESIAN_POINT('',(5.711986642891,17.03995));
#1090 = CARTESIAN_POINT('',(5.997585975035,17.03995));
#1091 = CARTESIAN_POINT('',(6.187985529798,17.03995));
#1092 = CARTESIAN_POINT('',(6.28318530718,17.03995));
#1093 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1094 = PCURVE('',#1095,#1100);
#1095 = CONICAL_SURFACE('',#1096,4.53995,0.785398163397);
#1096 = AXIS2_PLACEMENT_3D('',#1097,#1098,#1099);
#1097 = CARTESIAN_POINT('',(-17.89811369191,-0.826297072243,-43.18352));
#1098 = DIRECTION('',(0.E+000,-2.22044604925E-016,-1.));
#1099 = DIRECTION('',(-1.,-1.224606353822E-016,2.719172340232E-032));
#1100 = DEFINITIONAL_REPRESENTATION('',(#1101),#1127);
#1101 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#1102,#1103,#1104,#1105,#1106,
    #1107,#1108,#1109,#1110,#1111,#1112,#1113,#1114,#1115,#1116,#1117,
    #1118,#1119,#1120,#1121,#1122,#1123,#1124,#1125,#1126),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),
  (0.E+000,0.285599332145,0.571198664289,0.856797996434,1.142397328578,
    1.427996660723,1.713595992867,1.999195325012,2.284794657156,
    2.570393989301,2.855993321445,3.14159265359,3.427191985734,
    3.712791317879,3.998390650023,4.283989982168,4.569589314312,
    4.855188646457,5.140787978601,5.426387310746,5.711986642891,
    5.997585975035,6.28318530718),.QUASI_UNIFORM_KNOTS.);
#1102 = CARTESIAN_POINT('',(1.554312234475E-016,-9.53995));
#1103 = CARTESIAN_POINT('',(9.519977738151E-002,-9.53995));
#1104 = CARTESIAN_POINT('',(0.285599332145,-9.53995));
#1105 = CARTESIAN_POINT('',(0.571198664289,-9.53995));
#1106 = CARTESIAN_POINT('',(0.856797996434,-9.53995));
#1107 = CARTESIAN_POINT('',(1.142397328578,-9.53995));
#1108 = CARTESIAN_POINT('',(1.427996660723,-9.53995));
#1109 = CARTESIAN_POINT('',(1.713595992867,-9.53995));
#1110 = CARTESIAN_POINT('',(1.999195325012,-9.53995));
#1111 = CARTESIAN_POINT('',(2.284794657156,-9.53995));
#1112 = CARTESIAN_POINT('',(2.570393989301,-9.53995));
#1113 = CARTESIAN_POINT('',(2.855993321445,-9.53995));
#1114 = CARTESIAN_POINT('',(3.14159265359,-9.53995));
#1115 = CARTESIAN_POINT('',(3.427191985734,-9.53995));
#1116 = CARTESIAN_POINT('',(3.712791317879,-9.53995));
#1117 = CARTESIAN_POINT('',(3.998390650023,-9.53995));
#1118 = CARTESIAN_POINT('',(4.283989982168,-9.53995));
#1119 = CARTESIAN_POINT('',(4.569589314312,-9.53995));
#1120 = CARTESIAN_POINT('',(4.855188646457,-9.53995));
#1121 = CARTESIAN_POINT('',(5.140787978601,-9.53995));
#1122 = CARTESIAN_POINT('',(5.426387310746,-9.53995));
#1123 = CARTESIAN_POINT('',(5.711986642891,-9.53995));
#1124 = CARTESIAN_POINT('',(5.997585975035,-9.53995));
#1125 = CARTESIAN_POINT('',(6.187985529798,-9.53995));
#1126 = CARTESIAN_POINT('',(6.28318530718,-9.53995));
#1127 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1128 = ORIENTED_EDGE('',*,*,#1129,.F.);
#1129 = EDGE_CURVE('',#959,#1057,#1130,.T.);
#1130 = SEAM_CURVE('',#1131,(#1135,#1142),.PCURVE_S2.);
#1131 = LINE('',#1132,#1133);
#1132 = CARTESIAN_POINT('',(-12.89811369191,-0.826297072243,-16.60362));
#1133 = VECTOR('',#1134,1.);
#1134 = DIRECTION('',(0.E+000,-2.22044604925E-016,-1.));
#1135 = PCURVE('',#1018,#1136);
#1136 = DEFINITIONAL_REPRESENTATION('',(#1137),#1141);
#1137 = LINE('',#1138,#1139);
#1138 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1139 = VECTOR('',#1140,1.);
#1140 = DIRECTION('',(0.E+000,1.));
#1141 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1142 = PCURVE('',#1018,#1143);
#1143 = DEFINITIONAL_REPRESENTATION('',(#1144),#1148);
#1144 = LINE('',#1145,#1146);
#1145 = CARTESIAN_POINT('',(6.28318530718,0.E+000));
#1146 = VECTOR('',#1147,1.);
#1147 = DIRECTION('',(0.E+000,1.));
#1148 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1149 = ORIENTED_EDGE('',*,*,#981,.T.);
#1150 = ORIENTED_EDGE('',*,*,#1129,.T.);
#1151 = ADVANCED_FACE('',(#1152),#1095,.T.);
#1152 = FACE_BOUND('',#1153,.T.);
#1153 = EDGE_LOOP('',(#1154,#1209,#1230,#1231));
#1154 = ORIENTED_EDGE('',*,*,#1155,.F.);
#1155 = EDGE_CURVE('',#1156,#1156,#1158,.T.);
#1156 = VERTEX_POINT('',#1157);
#1157 = CARTESIAN_POINT('',(-13.81821369191,-0.826297072243,-34.56367));
#1158 = SURFACE_CURVE('',#1159,(#1164,#1193),.PCURVE_S2.);
#1159 = CIRCLE('',#1160,4.0799);
#1160 = AXIS2_PLACEMENT_3D('',#1161,#1162,#1163);
#1161 = CARTESIAN_POINT('',(-17.89811369191,-0.826297072243,-34.56367));
#1162 = DIRECTION('',(0.E+000,-2.22044604925E-016,-1.));
#1163 = DIRECTION('',(1.,0.E+000,0.E+000));
#1164 = PCURVE('',#1095,#1165);
#1165 = DEFINITIONAL_REPRESENTATION('',(#1166),#1192);
#1166 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#1167,#1168,#1169,#1170,#1171,
    #1172,#1173,#1174,#1175,#1176,#1177,#1178,#1179,#1180,#1181,#1182,
    #1183,#1184,#1185,#1186,#1187,#1188,#1189,#1190,#1191),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),
  (0.E+000,0.285599332145,0.571198664289,0.856797996434,1.142397328578,
    1.427996660723,1.713595992867,1.999195325012,2.284794657156,
    2.570393989301,2.855993321445,3.14159265359,3.427191985734,
    3.712791317879,3.998390650023,4.283989982168,4.569589314312,
    4.855188646457,5.140787978601,5.426387310746,5.711986642891,
    5.997585975035,6.28318530718),.QUASI_UNIFORM_KNOTS.);
#1167 = CARTESIAN_POINT('',(1.632720936236E-016,-8.61985));
#1168 = CARTESIAN_POINT('',(9.519977738151E-002,-8.61985));
#1169 = CARTESIAN_POINT('',(0.285599332145,-8.61985));
#1170 = CARTESIAN_POINT('',(0.571198664289,-8.61985));
#1171 = CARTESIAN_POINT('',(0.856797996434,-8.61985));
#1172 = CARTESIAN_POINT('',(1.142397328578,-8.61985));
#1173 = CARTESIAN_POINT('',(1.427996660723,-8.61985));
#1174 = CARTESIAN_POINT('',(1.713595992867,-8.61985));
#1175 = CARTESIAN_POINT('',(1.999195325012,-8.61985));
#1176 = CARTESIAN_POINT('',(2.284794657156,-8.61985));
#1177 = CARTESIAN_POINT('',(2.570393989301,-8.61985));
#1178 = CARTESIAN_POINT('',(2.855993321445,-8.61985));
#1179 = CARTESIAN_POINT('',(3.14159265359,-8.61985));
#1180 = CARTESIAN_POINT('',(3.427191985734,-8.61985));
#1181 = CARTESIAN_POINT('',(3.712791317879,-8.61985));
#1182 = CARTESIAN_POINT('',(3.998390650023,-8.61985));
#1183 = CARTESIAN_POINT('',(4.283989982168,-8.61985));
#1184 = CARTESIAN_POINT('',(4.569589314312,-8.61985));
#1185 = CARTESIAN_POINT('',(4.855188646457,-8.61985));
#1186 = CARTESIAN_POINT('',(5.140787978601,-8.61985));
#1187 = CARTESIAN_POINT('',(5.426387310746,-8.61985));
#1188 = CARTESIAN_POINT('',(5.711986642891,-8.61985));
#1189 = CARTESIAN_POINT('',(5.997585975035,-8.61985));
#1190 = CARTESIAN_POINT('',(6.187985529798,-8.61985));
#1191 = CARTESIAN_POINT('',(6.28318530718,-8.61985));
#1192 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1193 = PCURVE('',#1194,#1199);
#1194 = PLANE('',#1195);
#1195 = AXIS2_PLACEMENT_3D('',#1196,#1197,#1198);
#1196 = CARTESIAN_POINT('',(-15.85816369191,-0.826297072243,-34.56367));
#1197 = DIRECTION('',(0.E+000,-2.22044604925E-016,-1.));
#1198 = DIRECTION('',(0.E+000,1.,-2.22044604925E-016));
#1199 = DEFINITIONAL_REPRESENTATION('',(#1200),#1208);
#1200 = ( BOUNDED_CURVE() B_SPLINE_CURVE(2,(#1201,#1202,#1203,#1204,
#1205,#1206,#1207),.UNSPECIFIED.,.T.,.F.) B_SPLINE_CURVE_WITH_KNOTS((1,2
    ,2,2,2,1),(-2.094395102393,0.E+000,2.094395102393,4.188790204786,
6.28318530718,8.377580409573),.UNSPECIFIED.) CURVE() 
GEOMETRIC_REPRESENTATION_ITEM() RATIONAL_B_SPLINE_CURVE((1.,0.5,1.,0.5,
1.,0.5,1.)) REPRESENTATION_ITEM('') );
#1201 = CARTESIAN_POINT('',(0.E+000,2.03995));
#1202 = CARTESIAN_POINT('',(-7.0665940898,2.03995));
#1203 = CARTESIAN_POINT('',(-3.5332970449,-4.0799));
#1204 = CARTESIAN_POINT('',(-9.99254292592E-016,-10.19975));
#1205 = CARTESIAN_POINT('',(3.5332970449,-4.0799));
#1206 = CARTESIAN_POINT('',(7.0665940898,2.03995));
#1207 = CARTESIAN_POINT('',(0.E+000,2.03995));
#1208 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1209 = ORIENTED_EDGE('',*,*,#1210,.F.);
#1210 = EDGE_CURVE('',#1057,#1156,#1211,.T.);
#1211 = SEAM_CURVE('',#1212,(#1216,#1223),.PCURVE_S2.);
#1212 = LINE('',#1213,#1214);
#1213 = CARTESIAN_POINT('',(-22.43806369191,-0.826297072243,-43.18352));
#1214 = VECTOR('',#1215,1.);
#1215 = DIRECTION('',(-0.707106781187,-2.436019915756E-016,
    -0.707106781187));
#1216 = PCURVE('',#1095,#1217);
#1217 = DEFINITIONAL_REPRESENTATION('',(#1218),#1222);
#1218 = LINE('',#1219,#1220);
#1219 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1220 = VECTOR('',#1221,1.);
#1221 = DIRECTION('',(0.E+000,1.));
#1222 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1223 = PCURVE('',#1095,#1224);
#1224 = DEFINITIONAL_REPRESENTATION('',(#1225),#1229);
#1225 = LINE('',#1226,#1227);
#1226 = CARTESIAN_POINT('',(6.28318530718,0.E+000));
#1227 = VECTOR('',#1228,1.);
#1228 = DIRECTION('',(0.E+000,1.));
#1229 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1230 = ORIENTED_EDGE('',*,*,#1056,.T.);
#1231 = ORIENTED_EDGE('',*,*,#1210,.T.);
#1232 = ADVANCED_FACE('',(#1233),#1194,.T.);
#1233 = FACE_BOUND('',#1234,.T.);
#1234 = EDGE_LOOP('',(#1235));
#1235 = ORIENTED_EDGE('',*,*,#1155,.T.);
#1236 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1239)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1237,#1238)) REPRESENTATION_CONTEXT('Cont
ext #1','3D Context with UNIT and UNCERTAINTY') );
#1237 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1238 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1239 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-006),#1237,'dis
tance_accuracy_value','Confusion accuracy');
ENDSEC;
END-ISO-10303-21;
