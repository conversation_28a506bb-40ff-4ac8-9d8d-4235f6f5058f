diff --git a/compat/cuda/ptx2c.sh b/compat/cuda/ptx2c.sh
index 48452379c2..1c486dc30e 100755
--- a/compat/cuda/ptx2c.sh
+++ b/compat/cuda/ptx2c.sh
@@ -26,9 +26,10 @@ OUT="$1"
 IN="$2"
 NAME="$(basename "$IN" | sed 's/\..*//')"
 
-printf "const char %s_ptx[] = \\" "$NAME" > "$OUT"
+printf "const char %s_ptx[] = {\\" "$NAME" > "$OUT"
 echo >> "$OUT"
-sed -e "$(printf 's/\r//g')" -e 's/["\\]/\\&/g' -e "$(printf 's/^/\t"/')" -e 's/$/\\n"/' < "$IN" >> "$OUT"
-echo ";" >> "$OUT"
+xxd -i < "$IN" >> "$OUT"
+echo "  ,0x00" >> "$OUT"
+echo "};" >> "$OUT"
 
 exit 0
diff --git a/configure b/configure
index 6190d06b0b..48fb73738e 100755
--- a/configure
+++ b/configure
@@ -1046,6 +1046,7 @@ test_nvcc(){
     tmpo_=$TMPO
     [ -x "$(command -v cygpath)" ] && tmpcu_=$(cygpath -m $tmpcu_) && tmpo_=$(cygpath -m $tmpo_)
     test_cmd $nvcc $nvccflags "$@" $NVCC_C $(nvcc_o $tmpo_) $tmpcu_
+    test_cmd xxd
 }
 
 check_nvcc() {
