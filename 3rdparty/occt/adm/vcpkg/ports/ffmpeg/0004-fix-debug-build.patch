diff --git a/configure b/configure
index 405680e..cc5bf29 100755
--- a/configure
+++ b/configure
@@ -4111,6 +4111,9 @@ for opt do
         --libfuzzer=*)
             libfuzzer_path="$optval"
         ;;
+        --debug)
+            enable debug_configure
+        ;;
         *)
             optname="${opt%%=*}"
             optname="${optname#--}"
@@ -6316,7 +6319,11 @@ fi
 
 enabled  zlib && { check_pkg_config zlib zlib "zlib.h" zlibVersion ||
                    check_lib zlib   zlib.h      zlibVersion    -lz; }
-enabled bzlib && check_lib bzlib bzlib.h BZ2_bzlibVersion    -lbz2
+if enabled debug_configure; then
+    enabled bzlib && check_lib bzlib bzlib.h BZ2_bzlibVersion    -lbz2d
+else
+    enabled bzlib && check_lib bzlib bzlib.h BZ2_bzlibVersion    -lbz2
+fi
 enabled  lzma && check_lib lzma   lzma.h lzma_version_number -llzma
 
 # On some systems dynamic loading requires no extra linker flags
@@ -6434,7 +6441,11 @@ enabled librubberband     && require_pkg_config librubberband "rubberband >= 1.8
 enabled libshine          && require_pkg_config libshine shine shine/layer3.h shine_encode_buffer
 enabled libsmbclient      && { check_pkg_config libsmbclient smbclient libsmbclient.h smbc_init ||
                                require libsmbclient libsmbclient.h smbc_init -lsmbclient; }
-enabled libsnappy         && require libsnappy snappy-c.h snappy_compress -lsnappy -lstdc++
+if enabled debug_configure; then
+    enabled libsnappy     && require libsnappy snappy-c.h snappy_compress -lsnappy -lstdc++
+else
+    enabled libsnappy     && require libsnappy snappy-c.h snappy_compress -lsnappy -lstdc++
+fi
 enabled libsoxr           && require libsoxr soxr.h soxr_create -lsoxr
 enabled libssh            && require_pkg_config libssh libssh libssh/sftp.h sftp_init
 enabled libspeex          && require_pkg_config libspeex speex speex/speex.h speex_decoder_init
