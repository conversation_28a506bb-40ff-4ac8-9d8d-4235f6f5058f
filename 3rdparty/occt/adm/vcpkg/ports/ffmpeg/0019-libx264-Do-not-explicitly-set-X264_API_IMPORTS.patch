diff --git a/configure b/configure
index 62753ef..018764e 100755
--- a/configure
+++ b/configure
@@ -6508,11 +6508,8 @@ enabled libvpx            && {
 enabled libwebp           && {
     enabled libwebp_encoder      && require_pkg_config libwebp "libwebp >= 0.2.0" webp/encode.h WebPGetEncoderVersion
     enabled libwebp_anim_encoder && check_pkg_config libwebp_anim_encoder "libwebpmux >= 0.4.0" webp/mux.h WebPAnimEncoderOptionsInit; }
-enabled libx264           && { check_pkg_config libx264 x264 "stdint.h x264.h" x264_encoder_encode ||
-                               { require libx264 "stdint.h x264.h" x264_encoder_encode "-lx264 $pthreads_extralibs $libm_extralibs -ldl" &&
-                                 warn "using libx264 without pkg-config"; } } &&
-                             require_cpp_condition libx264 x264.h "X264_BUILD >= 118" &&
-                             check_cpp_condition libx262 x264.h "X264_MPEG2"
+enabled libx264           && check_pkg_config libx264 x264 "stdint.h x264.h" x264_encoder_encode &&
+                             require_cpp_condition libx264 x264.h "X264_BUILD >= 158"
 enabled libx265           && { check_pkg_config libx265 x265 x265.h x265_api_get ||
                                { { check_lib libx265 x265.h x265_api_get "-lx265 $pthreads_extralibs $libm_extralibs -ldl -lstdc++ -lgcc_s -lgcc -lrt -lnuma" ||
                                require libx265 x265.h x265_api_get "-lx265 $pthreads_extralibs $libm_extralibs -ldl -lstdc++"; } &&
diff --git a/libavcodec/libx264.c b/libavcodec/libx264.c
index 4ddc497..0152d30 100644
--- a/libavcodec/libx264.c
+++ b/libavcodec/libx264.c
@@ -32,10 +32,6 @@
 #include "packet_internal.h"
 #include "atsc_a53.h"
 
-#if defined(_MSC_VER)
-#define X264_API_IMPORTS 1
-#endif
-
 #include <x264.h>
 #include <float.h>
 #include <math.h>
