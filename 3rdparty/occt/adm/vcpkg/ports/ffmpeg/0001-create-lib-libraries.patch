diff --git a/configure b/configure
index 89af70d..405680e 100755
--- a/configure
+++ b/configure
@@ -5480,15 +5480,17 @@ case $target_os in
         ;;
     win32|win64)
         disable symver
-        if enabled shared; then
+#        if enabled shared; then
             # Link to the import library instead of the normal static library
             # for shared libs.
             LD_LIB='%.lib'
             # Cannot build both shared and static libs with MSVC or icl.
-            disable static
-        fi
+#            disable static
+#        fi
         enabled x86_32 && check_ldflags -LARGEADDRESSAWARE
         shlibdir_default="$bindir_default"
+        LIBPREF=""
+        LIBSUF=".lib"
         SLIBPREF=""
         SLIBSUF=".dll"
         SLIBNAME_WITH_VERSION='$(SLIBPREF)$(FULLNAME)-$(LIBVERSION)$(SLIBSUF)'
