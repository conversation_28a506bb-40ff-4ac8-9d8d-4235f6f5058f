diff --git a/configure b/configure
index 26e512e..c0377b6 100755
--- a/configure
+++ b/configure
@@ -3674,6 +3674,18 @@ vpp_qsv_filter_select="qsvvpp"
 xfade_opencl_filter_deps="opencl"
 yadif_cuda_filter_deps="ffnvcodec"
 yadif_cuda_filter_deps_any="cuda_nvcc cuda_llvm"
+ametadata_filter_deps="avformat"
+metadata_filter_deps="avformat"
+headphone_filter_deps="avcodec"
+headphone_filter_select="fft"
+showspatial_filter_deps="avcodec"
+showspatial_filter_select="fft"
+superequalizer_filter_deps="avcodec"
+superequalizer_filter_select="rdft"
+surround_filter_deps="avcodec"
+surround_filter_select="rdft"
+sinc_filter_deps="avcodec"
+sinc_filter_select="rdft"
 
 # examples
 avio_list_dir_deps="avformat avutil"
