# doxygen
set (<PERSON><PERSON><PERSON><PERSON><PERSON>_MINIMUM_VERSION 1.8.4)

if (DO_ONLY_CHECK_FOR_DOXYGEN)
  message (STATUS "Info. Detecting doxygen")
  set (<PERSON>OXYGEN_SKIP_DOT ON)
  find_package (Doxygen ${DOXYGEN_MINIMUM_VERSION})

  set (CAN_DOXYGEN_BE_USED OFF)
  if (DOXYGEN_EXECUTABLE)
    set (CAN_DOXYGEN_BE_USED ON)
    message (STATUS "Info. Doxygen is found and can be used")
  endif()
else()
  set (3RDPARTY_DOT_EXECUTABLE_DESCR       "The path to the 'dot' tool producing layered drawings of directed graphs.\nThis tool used by doxygen")
  set (3RDPARTY_DOXYGEN_EXECUTABLE_DESCR   "The path to the doxygen command")

  if (NOT DEFINED 3RDPARTY_DOXYGEN_EXECUTABLE)
    set (3RDPARTY_DOXYGEN_EXECUTABLE "" CACHE FILEPATH "${3RDPARTY_DOXYGEN_EXECUTABLE_DESCR}")
  endif()

  if (NOT DEFINED 3RDPARTY_DOT_EXECUTABLE)
    set (3RDPARTY_DOT_EXECUTABLE "" CACHE FILEPATH "${3RDPARTY_DOT_EXECUTABLE_DESCR}")
  endif()

  if (NOT DEFINED 3RDPARTY_SKIP_DOT_EXECUTABLE)
    set (3RDPARTY_SKIP_DOT_EXECUTABLE ON CACHE BOOL "Skip trying to find Dot")
  endif()

  if (3RDPARTY_SKIP_DOT_EXECUTABLE)
    OCCT_CHECK_AND_UNSET (3RDPARTY_DOT_EXECUTABLE)
  endif()

  if (NOT 3RDPARTY_DOXYGEN_EXECUTABLE OR (NOT 3RDPARTY_SKIP_DOT_EXECUTABLE AND NOT 3RDPARTY_DOT_EXECUTABLE))

    set (DOXYGEN_SKIP_DOT ${3RDPARTY_SKIP_DOT_EXECUTABLE})
    find_package (Doxygen ${DOXYGEN_MINIMUM_VERSION})
    
    if (NOT 3RDPARTY_DOXYGEN_EXECUTABLE AND DOXYGEN_EXECUTABLE)
      set (3RDPARTY_DOXYGEN_EXECUTABLE "${DOXYGEN_EXECUTABLE}" CACHE FILEPATH "${3RDPARTY_DOXYGEN_EXECUTABLE_DESCR}" FORCE)
    endif()
    
    if (NOT 3RDPARTY_SKIP_DOT_EXECUTABLE AND NOT 3RDPARTY_DOT_EXECUTABLE AND DOXYGEN_DOT_EXECUTABLE)
      set (3RDPARTY_DOT_EXECUTABLE "${DOXYGEN_DOT_EXECUTABLE}" CACHE FILEPATH "${3RDPARTY_DOT_EXECUTABLE_DESCR}" FORCE)
    endif()
  endif()

  if (NOT 3RDPARTY_DOXYGEN_EXECUTABLE OR NOT EXISTS "${3RDPARTY_DOXYGEN_EXECUTABLE}")
    list (APPEND 3RDPARTY_NOT_INCLUDED 3RDPARTY_DOXYGEN_EXECUTABLE)
  endif()

  if (NOT 3RDPARTY_SKIP_DOT_EXECUTABLE)
    if (NOT 3RDPARTY_DOT_EXECUTABLE OR NOT EXISTS "${3RDPARTY_DOT_EXECUTABLE}")
      list (APPEND 3RDPARTY_NOT_INCLUDED 3RDPARTY_DOT_EXECUTABLE)
    endif()
  endif()
endif()

# unset all redundant variables
OCCT_CHECK_AND_UNSET (DOXYGEN_SKIP_DOT)
OCCT_CHECK_AND_UNSET (DOXYGEN_EXECUTABLE)
OCCT_CHECK_AND_UNSET (DOXYGEN_DOT_EXECUTABLE)
