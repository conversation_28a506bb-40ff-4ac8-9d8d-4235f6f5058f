#!/usr/bin/tclsh

# =======================================================================
# Created on: 2014-03-21
# Created by: OMY
# Copyright (c) 1996-1999 Matra Datavision
# Copyright (c) 1999-2014 OPEN CASCADE SAS
#
# This file is part of Open CASCADE Technology software library.
#
# This library is free software; you can redistribute it and/or modify it under
# the terms of the GNU Lesser General Public License version 2.1 as published
# by the Free Software Foundation, with special exception defined in the file
# OCCT_LGPL_EXCEPTION.txt. Consult the file LICENSE_LGPL_21.txt included in OCCT
# distribution for complete text of the license and disclaimer of any warranty.
#
# Alternatively, this file may be used under the terms of Open CASCADE
# commercial license or contractual agreement.
 
if { [llength $argv] < 1 } {
  puts "Command-line starter for Tcl command defined in same-named file."
  puts "Use it as follows:"
  puts "\> tclsh start.tcl command \[arguments\]"
  return
}

set cmdname [lindex $argv 0]
source [file join [file dirname [info script]] $cmdname.tcl]

eval $cmdname [lrange $argv 1 end]
