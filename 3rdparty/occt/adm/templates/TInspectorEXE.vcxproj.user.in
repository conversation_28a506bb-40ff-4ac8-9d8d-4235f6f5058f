﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
 <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|@X_COMPILER_BITNESS@'">
  <LocalDebuggerEnvironment>CASROOT=@CMAKE_SOURCE_DIR@
CSF_OCCTDataPath=@CMAKE_SOURCE_DIR@/data
QTDIR=@3RDPARTY_QT_DIR@
PATH=@3RDPARTY_DLL_DIRS_FOR_PATH@;@OpenCASCADE_BINARY_DIR@;%PATH%
 </LocalDebuggerEnvironment>
 <DebuggerFlavor>WindowsLocalDebugger</DebuggerFlavor>
 <LocalDebuggerWorkingDirectory>@CMAKE_BINARY_DIR@</LocalDebuggerWorkingDirectory>
 </PropertyGroup>
 <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|@X_COMPILER_BITNESS@'">
  <LocalDebuggerEnvironment>CASROOT=@CMAKE_SOURCE_DIR@
CSF_OCCTDataPath=@CMAKE_SOURCE_DIR@/data
QTDIR=@3RDPARTY_QT_DIR@
PATH=@3RDPARTY_DLL_DIRS_FOR_PATH@;@OpenCASCADE_BINARY_DIR@i;%PATH%
 </LocalDebuggerEnvironment>
 <DebuggerFlavor>WindowsLocalDebugger</DebuggerFlavor>
 <LocalDebuggerWorkingDirectory>@CMAKE_BINARY_DIR@</LocalDebuggerWorkingDirectory>
 </PropertyGroup>
 <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|@X_COMPILER_BITNESS@'">
  <LocalDebuggerEnvironment>CASROOT=@CMAKE_SOURCE_DIR@
CSF_OCCTDataPath=@CMAKE_SOURCE_DIR@/data
QTDIR=@3RDPARTY_QT_DIR@
PATH=@3RDPARTY_DLL_DIRS_FOR_PATH@;@OpenCASCADE_BINARY_DIR@d;%PATH%
 </LocalDebuggerEnvironment>
 <DebuggerFlavor>WindowsLocalDebugger</DebuggerFlavor>
 <LocalDebuggerWorkingDirectory>@CMAKE_BINARY_DIR@</LocalDebuggerWorkingDirectory>
 </PropertyGroup>
</Project>