echo off

set "SCRIPTROOT=%~dp0"
set "SCRIPTROOT=%SCRIPTROOT:~0,-1%"

rem ----- Reset values to defaults -----
set "VCVER=@COMPILER@"
set "ARCH=@COMPILER_BITNESS@"
set "CASDEB="

if not ["%1"]    == [""]      set "VCVER=%1"
if not ["%2"]    == [""]      set "ARCH=%2"
if /I ["%ARCH%"] == ["win32"] set "ARCH=32"
if /I ["%ARCH%"] == ["win64"] set "ARCH=64"
if /I ["%3"]     == ["debug"] set "CASDEB=d"
if /I ["%3"]     == ["d"]     set "CASDEB=d"
if /I ["%3"]     == ["i"]     set "CASDEB=i"
if /I ["%3"]     == ["relwithdeb"] set "CASDEB=i"

rem ----- Decode VCVER variable and define related ones -----
rem
rem VCFMT - "vc" followed by full version number of Visual Studio toolset
rem         (same as VCVER without optional suffix "-uwp")
rem VCLIB - name of folder containing binaries
rem         (same as VCVER except without third version in number)
rem VCPROP - name of required Visual Studion Workload (starting with VS 2017)
rem
rem Note that for VS before 2015 (vc14) always
rem VCFMT=VCLIB=VCVER and VCPROP=NativeDesktop

rem Since VS 2017, environment variables like VS100COMNTOOLS are not defined 
rem any more, we can only use vswhere.exe tool to find Visual Studio.
rem Add path to vswhere.exe
if /I not "%VCFMT%" == "gcc" (
  set "PATH=%PATH%;%ProgramFiles(x86)%\Microsoft Visual Studio\Installer"
)

rem for vc10-12, interpretation is trivial
set VCFMT=%VCVER%
set VCLIB=%VCVER:~0,4%
set VCPROP=NativeDesktop
rem vc14 and later can have optional suffix "-uwp"
if "%VCVER:~-4%" == "-uwp" (
  set VCFMT=%VCVER:~0,-4%
  set VCLIB=%VCLIB%-uwp
  set VCPROP=Universal
)
rem echo VCVER=%VCVER% VCFMT=%VCFMT% VCLIB=%VCLIB% VCPROP=%VCPROP%

rem ----- Parsing of Visual Studio platform -----
set "VisualStudioExpressName=VCExpress"

if not "%DevEnvDir%" == "" (
  rem If DevEnvDir is already defined (e.g. in custom.bat), use that value
) else if /I "%VCFMT%" == "vc9" (
  set "DevEnvDir=%VS90COMNTOOLS%..\IDE"
) else if /I "%VCFMT%" == "vc10" (
  set "DevEnvDir=%VS100COMNTOOLS%..\IDE"
) else if /I "%VCFMT%" == "vc11" (
  set "DevEnvDir=%VS110COMNTOOLS%..\IDE"
  rem Visual Studio Express starting from VS 2012 is called "for Windows Desktop"
  rem and has a new name for executable - WDExpress
  set "VisualStudioExpressName=WDExpress"
) else if /I "%VCFMT%" == "vc12" (
  set "DevEnvDir=%VS120COMNTOOLS%..\IDE"
  set "VisualStudioExpressName=WDExpress"
) else if /I "%VCFMT%" == "vc14" (
  set "DevEnvDir=%VS140COMNTOOLS%..\IDE"
) else if /I "%VCFMT%" == "vc141" (
  for /f "usebackq delims=" %%i in (`vswhere.exe -version "[15.0,15.99]" -requires Microsoft.VisualStudio.Workload.%VCPROP% -property installationPath`) do (
    set "DevEnvDir=%%i\Common7\IDE\"
  )
) else if /I "%VCFMT%" == "vc142" (
  for /f "usebackq delims=" %%i in (`vswhere.exe -version "[16.0,16.99]" -latest -requires Microsoft.VisualStudio.Workload.%VCPROP% -property installationPath`) do (
    set "DevEnvDir=%%i\Common7\IDE\"
  )  
) else if /I "%VCFMT%" == "vc143" (
  for /f "usebackq delims=" %%i in (`vswhere.exe -version "[17.0,17.99]" -latest -requires Microsoft.VisualStudio.Workload.%VCPROP% -property installationPath`) do (
    set "DevEnvDir=%%i\Common7\IDE\"
  ) 
) else if /I "%VCFMT%" == "gcc" (
  rem MinGW
) else if /I "%VCFMT%" == "clang" (
  rem clang
) else (
  echo Error: wrong VS identifier
  exit /B
)

rem ----- Parsing vcvarsall for qt samples and define PlatformToolset -----
if /I "%VCFMT%" == "vc9" (
  set "VCVARS=%VS90COMNTOOLS%..\..\VC\vcvarsall.bat"
  set "VCPlatformToolSet=v90"
) else if /I "%VCFMT%" == "vc10" (
  set "VCVARS=%VS100COMNTOOLS%..\..\VC\vcvarsall.bat"
  set "VCPlatformToolSet=v100"
) else if /I "%VCFMT%" == "vc11" (
  set "VCVARS=%VS110COMNTOOLS%..\..\VC\vcvarsall.bat"
  set "VCPlatformToolSet=v110"
) else if /I "%VCFMT%" == "vc12" (
  set "VCVARS=%VS120COMNTOOLS%..\..\VC\vcvarsall.bat"
  set "VCPlatformToolSet=v120"
) else if /I "%VCFMT%" == "vc14" (
  set "VCVARS=%VS140COMNTOOLS%..\..\VC\vcvarsall.bat"
  set "VCPlatformToolSet=v140"
) else if /I "%VCFMT%" == "vc141" (
  for /f "usebackq delims=" %%i in (`vswhere.exe -version "[15.0,15.99]" -requires Microsoft.VisualStudio.Workload.%VCPROP% -property installationPath`) do (
    set "VCVARS=%%i\VC\Auxiliary\Build\vcvarsall.bat"
  )
  set "VCPlatformToolSet=v141"
) else if /I "%VCFMT%" == "vc142" (
  for /f "usebackq delims=" %%i in (`vswhere.exe -version "[16.0,16.99]" -latest -requires Microsoft.VisualStudio.Workload.%VCPROP% -property installationPath`) do (
    set "VCVARS=%%i\VC\Auxiliary\Build\vcvarsall.bat"
  ) 
  set "VCPlatformToolSet=v142"
) else if /I "%VCFMT%" == "vc143" (
  for /f "usebackq delims=" %%i in (`vswhere.exe -version "[17.0,17.99]" -latest -requires Microsoft.VisualStudio.Workload.%VCPROP% -property installationPath`) do (
    set "VCVARS=%%i\VC\Auxiliary\Build\vcvarsall.bat"
  ) 
  set "VCPlatformToolSet=v142"  
) else if /I "%VCFMT%" == "gcc" (
  rem MinGW
) else if /I "%VCFMT%" == "clang" (
  rem clang
) else (
  echo Error: first argument ^(%VCVER%^) should specify supported version of Visual C++,
  echo one of: vc10 ^(VS 2010 SP3^), vc11 ^(VS 2012 SP3^), vc12 ^(VS 2013^) or vc14 ^(VS 2015^)
  exit
)

rem ----- For compatibility with external application using CASROOT -----
if ["%CASROOT%"] == [""] set "CASROOT=%SCRIPTROOT%"

rem ----- Define path to 3rdparty products -----
if ["%THIRDPARTY_DIR%"] == [""] set "THIRDPARTY_DIR=@3RDPARTY_DIR@"

if ["%ARCH%"] == ["32"] set VCARCH=x86
if ["%ARCH%"] == ["64"] set VCARCH=amd64

if /I ["%1"] == ["vc141"] set "VCVER=vc14"
if /I ["%1"] == ["vc142"] set "VCVER=vc14"
if /I ["%1"] == ["vc143"] set "VCVER=vc14"

if exist "%CASROOT%\custom.bat" (
  call "%CASROOT%\custom.bat" %VCVER% %ARCH% %CASDEB%
)

if not ["%QTDIR%"] == [""] (
  set "PATH=%QTDIR%/bin;%PATH%"
  set "QT_PLUGIN_PATH=%QTDIR%/plugins"
)
if not ["%TCL_DIR%"] == [""]           set "PATH=%TCL_DIR%;%PATH%"
if not ["%TK_DIR%"] == [""]            set "PATH=%TK_DIR%;%PATH%"
if not ["%FREETYPE_DIR%"] == [""]      set "PATH=%FREETYPE_DIR%;%PATH%"
if not ["%FREEIMAGE_DIR%"] == [""]     set "PATH=%FREEIMAGE_DIR%;%PATH%"
if not ["%EGL_DIR%"] == [""]           set "PATH=%EGL_DIR%;%PATH%"
if not ["%GLES2_DIR%"] == [""]         set "PATH=%GLES2_DIR%;%PATH%"
if not ["%TBB_DIR%"] == [""]           set "PATH=%TBB_DIR%;%PATH%"
if not ["%VTK_DIR%"] == [""]           set "PATH=%VTK_DIR%;%PATH%"
if not ["%FFMPEG_DIR%"] == [""]        set "PATH=%FFMPEG_DIR%;%PATH%"
if not ["%JEMALLOC_DIR%"] == [""]      set "PATH=%JEMALLOC_DIR%;%PATH%"
if not ["%OPENVR_DIR%"] == [""]        set "PATH=%OPENVR_DIR%;%PATH%"

rem ----- Set path to 3rd party and OCCT libraries -----
if not "%CSF_OCCTBinPath%" == "" (
  set "PATH=%CSF_OCCTBinPath%;%PATH%"
)

if not ["%TK_DIR%"] == ["%TCL_DIR%"] (
  if not ["%TK_DIR%"] == [""]  set "TK_LIBRARY=%TK_DIR%/../lib/tk%TK_VERSION_WITH_DOT%"
  if not ["%TCL_DIR%"] == [""] set "TCL_LIBRARY=%TCL_DIR%/../lib/tcl%TCL_VERSION_WITH_DOT%"
)

rem ----- Set envoronment variables used by OCCT -----
set  CSF_LANGUAGE=us
set  MMGT_CLEAR=1
set "CSF_SHMessage=%CSF_OCCTResourcePath%\SHMessage"
set "CSF_MDTVTexturesDirectory=%CSF_OCCTResourcePath%\Textures"
set "CSF_ShadersDirectory=%CSF_OCCTResourcePath%\Shaders"
set "CSF_XSMessage=%CSF_OCCTResourcePath%\XSMessage"
set "CSF_TObjMessage=%CSF_OCCTResourcePath%\TObj"
set "CSF_StandardDefaults=%CSF_OCCTResourcePath%\StdResource"
set "CSF_PluginDefaults=%CSF_OCCTResourcePath%\StdResource"
set "CSF_XCAFDefaults=%CSF_OCCTResourcePath%\StdResource"
set "CSF_TObjDefaults=%CSF_OCCTResourcePath%\StdResource"
set "CSF_StandardLiteDefaults=%CSF_OCCTResourcePath%\StdResource"
set "CSF_IGESDefaults=%CSF_OCCTResourcePath%\XSTEPResource"
set "CSF_STEPDefaults=%CSF_OCCTResourcePath%\XSTEPResource"
set "CSF_XmlOcafResource=%CSF_OCCTResourcePath%\XmlOcafResource"
set "CSF_MIGRATION_TYPES=%CSF_OCCTResourcePath%\StdResource\MigrationSheet.txt"

rem ----- Draw Harness special stuff -----
if exist "%CSF_OCCTResourcePath%\DrawResources" (
  set "DRAWHOME=%CSF_OCCTResourcePath%\DrawResources"
  set "CSF_DrawPluginDefaults=%CSF_OCCTResourcePath%\DrawResources"

  if exist "%CSF_OCCTResourcePath%\DrawResources\DrawDefault" (
    set "DRAWDEFAULT=%CSF_OCCTResourcePath%\DrawResources\DrawDefault"
  )
)
