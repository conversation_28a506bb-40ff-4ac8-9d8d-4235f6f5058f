#-----------------------------------------------------------------------------
#
# OpenCASCADEConfig.cmake - OpenCASCADE CMake configuration file for external projects.
#
# This file is configured by OpenCASCADE.
#

if(OpenCASCADE_ALREADY_INCLUDED)
  return()
endif()
set(OpenCASCADE_ALREADY_INCLUDED 1)

# The OpenCASCADE version number
set (OpenCASCADE_MAJOR_VERSION       "@OCC_VERSION_MAJOR@")
set (OpenCASCADE_MINOR_VERSION       "@OCC_VERSION_MINOR@")
set (OpenCASCADE_MAINTENANCE_VERSION "@OCC_VERSION_MAINTENANCE@")
set (OpenCASCADE_DEVELOPMENT_VERSION "@OCC_VERSION_DEVELOPMENT@")

# Compute the installation prefix from this OpenCASCADEConfig.cmake file 
# location, by going up one level + one level if "cmake" + one level if "lib".
# This is made to support different locations of CMake files:
# - in UNIX style: $INSTALL_DIR/lib/cmake/opencascade-<version>
# - in Windows style: $INSTALL_DIR/cmake
# - in Android style: $INSTALL_DIR/libs/$CMAKE_ANDROID_ARCH_ABI/cmake/opencascade-<version>
get_filename_component (OpenCASCADE_INSTALL_PREFIX "${CMAKE_CURRENT_LIST_FILE}" PATH)
get_filename_component (OpenCASCADE_INSTALL_PREFIX "${OpenCASCADE_INSTALL_PREFIX}" PATH)
if (OpenCASCADE_INSTALL_PREFIX MATCHES "/cmake$")
  get_filename_component (OpenCASCADE_INSTALL_PREFIX "${OpenCASCADE_INSTALL_PREFIX}" PATH)
endif()
if (OpenCASCADE_INSTALL_PREFIX MATCHES "/lib(32|64)?$")
  get_filename_component (OpenCASCADE_INSTALL_PREFIX "${OpenCASCADE_INSTALL_PREFIX}" PATH)
endif()
if (OpenCASCADE_INSTALL_PREFIX MATCHES "/libs/${CMAKE_ANDROID_ARCH_ABI}$")
  get_filename_component (OpenCASCADE_INSTALL_PREFIX "${OpenCASCADE_INSTALL_PREFIX}" PATH)
  get_filename_component (OpenCASCADE_INSTALL_PREFIX "${OpenCASCADE_INSTALL_PREFIX}" PATH)
endif()

# Set OpenCASCADE paths to headers, binaries, libraries, resources, tests, samples, data
set (OpenCASCADE_BINARY_DIR   "${OpenCASCADE_INSTALL_PREFIX}/@INSTALL_DIR_BIN@")
set (OpenCASCADE_LIBRARY_DIR  "${OpenCASCADE_INSTALL_PREFIX}/@INSTALL_DIR_LIB@")
set (OpenCASCADE_SCRIPT_DIR   "${OpenCASCADE_INSTALL_PREFIX}/@INSTALL_DIR_SCRIPT@")
set (OpenCASCADE_INCLUDE_DIR  "${OpenCASCADE_INSTALL_PREFIX}/@INSTALL_DIR_INCLUDE@")
set (OpenCASCADE_RESOURCE_DIR "${OpenCASCADE_INSTALL_PREFIX}/@INSTALL_DIR_RESOURCE@")

# The C and C++ flags added by OpenCASCADE to the cmake-configured flags.
set (OpenCASCADE_C_FLAGS      "@CMAKE_C_FLAGS@")
set (OpenCASCADE_CXX_FLAGS    "@CMAKE_CXX_FLAGS@")
@SET_OpenCASCADE_LINKER_FLAGS@

# List of available OpenCASCADE modules.
set (OpenCASCADE_MODULES @OCCT_MODULES_ENABLED@)
@SET_OpenCASCADE_MODULES_TOOLKITS@
# List of available OpenCASCADE libraries.
set (OpenCASCADE_LIBRARIES @OCCT_LIBRARIES@)

# OpenCASCADE global configuration options.
set (OpenCASCADE_COMPILER          "@COMPILER@")
set (OpenCASCADE_BUILD_WITH_DEBUG  @BUILD_WITH_DEBUG@)
set (OpenCASCADE_BUILD_SHARED_LIBS @BUILD_SHARED_LIBS@)
@SET_OpenCASCADE_BUILD_TYPE@

# Use of third-party libraries.
set (OpenCASCADE_WITH_TCL       @USE_TCL@)
set (OpenCASCADE_WITH_FREETYPE  @USE_FREETYPE@)
set (OpenCASCADE_WITH_FREEIMAGE @USE_FREEIMAGE@)
set (OpenCASCADE_WITH_TBB       @USE_TBB@)
set (OpenCASCADE_WITH_VTK       @USE_VTK@)
set (OpenCASCADE_WITH_FFMPEG    @USE_FFMPEG@)
set (OpenCASCADE_WITH_GLES2     @USE_GLES2@)
@SET_OpenCASCADE_WITH_D3D@
@SET_OpenCASCADE_WITH_GLX@

# Import OpenCASCADE compile definitions, C and C++ flags for each installed configuration.
file(GLOB CONFIG_FILES "${CMAKE_CURRENT_LIST_DIR}/OpenCASCADECompileDefinitionsAndFlags-*.cmake")
foreach(f ${CONFIG_FILES})
  include(${f})
endforeach()

if (NOT OpenCASCADE_FIND_COMPONENTS)
  set (OpenCASCADE_FIND_COMPONENTS ${OpenCASCADE_MODULES})
endif ()

# Import OpenCASCADE targets.
foreach(_comp ${OpenCASCADE_FIND_COMPONENTS})
  if (NOT ";${OpenCASCADE_MODULES};" MATCHES "${_comp}")
    set(OpenCASCADE_FOUND False)
    set(OpenCASCADE_NOTFOUND_MESSAGE "Specified unsupported component: ${_comp}")
    if (NOT OpenCASCADE_FIND_QUIETLY)
      message (ERROR ": ${OpenCASCADE_NOTFOUND_MESSAGE}")
    endif()
  else()
    include("${CMAKE_CURRENT_LIST_DIR}/OpenCASCADE${_comp}Targets.cmake")
  endif()
endforeach()
