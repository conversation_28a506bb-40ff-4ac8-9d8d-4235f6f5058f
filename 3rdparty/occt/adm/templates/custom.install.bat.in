echo off

rem CASDEB comes as third argument

if /I "%VCVER%" == "@COMPILER@" (
  if "%ARCH%" == "@COMPILER_BITNESS@" (
    rem set environment variables used by OCCT
    set CSF_FPE=@BUILD_ENABLE_FPE_SIGNAL_HANDLER@

    set "TCL_DIR=@USED_3RDPARTY_TCL_DIR@"
    set "TK_DIR=@USED_3RDPARTY_TK_DIR@"
    set "FREETYPE_DIR=@USED_3RDPARTY_FREETYPE_DIR@"
    set "FREEIMAGE_DIR=@USED_3RDPARTY_FREEIMAGE_DIR@"
    set "EGL_DIR=@USED_3RDPARTY_EGL_DIRS@"
    set "GLES2_DIR=@USED_3RDPARTY_GLES2_DIRS@"
    set "TBB_DIR=@USED_3RDPARTY_TBB_DIR@"
    set "VTK_DIR=@USED_3RDPARTY_VTK_DIR@"
    set "FFMPEG_DIR=@USED_3RDPARTY_FFMPEG_DIR@"
    set "JEMALLOC_DIR=@USED_3RDPARTY_JEMALLOC_DIR@"
    set "OPENVR_DIR=@USED_3RDPARTY_OPENVR_DIR@"

    if not "@USED_3RDPARTY_QT_DIR@" == "" (
      set "QTDIR=@USED_3RDPARTY_QT_DIR@"
    )
    set "TCL_VERSION_WITH_DOT=@3RDPARTY_TCL_LIBRARY_VERSION_WITH_DOT@"
    set "TK_VERSION_WITH_DOT=@3RDPARTY_TK_LIBRARY_VERSION_WITH_DOT@"

    set "CSF_OCCTBinPath=%CASROOT%/@INSTALL_DIR_BIN@%3"
    set "CSF_OCCTLibPath=%CASROOT%/@INSTALL_DIR_LIB@%3"

    set "CSF_OCCTIncludePath=%CASROOT%/@INSTALL_DIR_INCLUDE@"
    set "CSF_OCCTResourcePath=%CASROOT%/@INSTALL_DIR_RESOURCE@"
    set "CSF_OCCTDataPath=%CASROOT%/@INSTALL_DIR_DATA@"
    set "CSF_OCCTSamplesPath=%CASROOT%/@INSTALL_DIR_SAMPLES@"
    set "CSF_OCCTTestsPath=%CASROOT%/@INSTALL_DIR_TESTS@"
    set "CSF_OCCTDocPath=%CASROOT%/@INSTALL_DIR_DOC@"
  )
)

