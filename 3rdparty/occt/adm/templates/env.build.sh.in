#!/bin/bash
aCurrentPath="$PWD"
aScriptPath=${BASH_SOURCE%/*}; if [ -d "${aScriptPath}" ]; then cd "$aScriptPath"; fi; aScriptPath="$PWD";
cd ${aCurrentPath}

# ----- For compatibility with external application using CASROOT -----
if [ "${CASROOT}" == "" ]; then
  # Get the last directory name from the path
  lastDir=$(basename "$aScriptPath")
  # Check if last directory is exactly bin, bind, or bini
  if [ "$lastDir" = "bin" ] || [ "$lastDir" = "bind" ] || [ "$lastDir" = "bini" ]; then
    # If path contains binary folder, go one level up
    export CASROOT=$(cd "$aScriptPath/.." && pwd)
  else
    # Keep current location
    export CASROOT="${aScriptPath}"
  fi
  cd ${aCurrentPath}
fi

# ----- Define path to 3rdparty products -----
export THIRDPARTY_DIR="@3RDPARTY_DIR@"

# ----- Read script arguments -----
shopt -s nocasematch
export CASDEB="";
if [[ "$1" == "debug" ]]; then export CASDEB="d"; fi
if [[ "$1" == "d" ]]; then export CASDEB="d"; fi
if [[ "$1" == "relwithdeb" ]]; then export CASDEB="i"; fi
if [[ "$1" == "i" ]]; then export CASDEB="i"; fi
shopt -u nocasematch

# ----- Set path to 3rd party and OCCT libraries -----
anArch=`uname -m`
if [ "$anArch" != "x86_64" ] && [ "$anArch" != "ia64" ] && [ "$anArch" != "aarch64"]; then
  export ARCH="32";
else
  export ARCH="64";
fi

aSystem=`uname -s`
if [ "$aSystem" == "Darwin" ]; then
  export WOKSTATION="mac";
  export ARCH="64";
else
  export WOKSTATION="lin";
fi

# ----- Set local settings -----
if [ -e "${CASROOT}/custom.sh" ]; then
  source "${CASROOT}/custom.sh" "${CASDEB}" "${ARCH}"
fi

THRDPARTY_PATH=""
if [ "$TCL_DIR" != "" ]; then
  THRDPARTY_PATH="${TCL_DIR}:${THRDPARTY_PATH}"
fi

if [ "$TK_DIR" != "" ]; then
  THRDPARTY_PATH="${TK_DIR}:${THRDPARTY_PATH}"
fi

if [ "$FREETYPE_DIR" != "" ]; then
  THRDPARTY_PATH="${FREETYPE_DIR}:${THRDPARTY_PATH}"
fi

if [ "$FREEIMAGE_DIR" != "" ]; then
  THRDPARTY_PATH="${FREEIMAGE_DIR}:${THRDPARTY_PATH}"
fi

if [ "$TBB_DIR" != "" ]; then
  THRDPARTY_PATH="${TBB_DIR}:${THRDPARTY_PATH}"
fi

if [ "$VTK_DIR" != "" ]; then
  THRDPARTY_PATH="${VTK_DIR}:${THRDPARTY_PATH}"
fi

if [ "$FFMPEG_DIR" != "" ]; then
  THRDPARTY_PATH="${FFMPEG_DIR}:${THRDPARTY_PATH}"
fi

if [ "$JEMALLOC_DIR" != "" ]; then
  THRDPARTY_PATH="${JEMALLOC_DIR}:${THRDPARTY_PATH}"
fi

if [ "$QTDIR" != "" ]; then
  THRDPARTY_PATH="${QTDIR}/lib:${THRDPARTY_PATH}"
fi

if [ "$TK_DIR" != "$TCL_DIR" ]; then
  if [ "$TK_DIR" != "" ]; then
    export TK_LIBRARY="${TK_DIR}/../lib/tk${TK_VERSION_WITH_DOT}"
  fi
  if [ "$TCL_DIR" != "" ]; then
    export TCL_LIBRARY="${TCL_DIR}/../lib/tcl${TCL_VERSION_WITH_DOT}"
  fi
fi

if [ "$LD_LIBRARY_PATH" != "" ]; then
  export LD_LIBRARY_PATH="${THRDPARTY_PATH}:${LD_LIBRARY_PATH}"
else
  export LD_LIBRARY_PATH="${THRDPARTY_PATH}"
fi

if [ "$CSF_OCCTBinPath" != "" ]; then
  export PATH="${CSF_OCCTBinPath}:${PATH}"
fi

if [ "$CSF_OCCTLibPath" != "" ]; then
  if [ "$LD_LIBRARY_PATH" != "" ]; then
    export LD_LIBRARY_PATH="${CSF_OCCTLibPath}:${LD_LIBRARY_PATH}"
  else
    export LD_LIBRARY_PATH="${CSF_OCCTLibPath}"
  fi
fi

if [ "$WOKSTATION" == "mac" ]; then
  if [ "$DYLD_LIBRARY_PATH" != "" ]; then
    export DYLD_LIBRARY_PATH="${LD_LIBRARY_PATH}:${DYLD_LIBRARY_PATH}"
  else
    export DYLD_LIBRARY_PATH="${LD_LIBRARY_PATH}"
  fi
fi

# ----- Set envoronment variables used by OCCT -----
export CSF_LANGUAGE=us
export MMGT_CLEAR=1
export CSF_SHMessage="${CSF_OCCTResourcePath}/SHMessage"
export CSF_MDTVTexturesDirectory="${CSF_OCCTResourcePath}/Textures"
export CSF_ShadersDirectory="${CSF_OCCTResourcePath}/Shaders"
export CSF_XSMessage="${CSF_OCCTResourcePath}/XSMessage"
export CSF_TObjMessage="${CSF_OCCTResourcePath}/TObj"
export CSF_StandardDefaults="${CSF_OCCTResourcePath}/StdResource"
export CSF_PluginDefaults="${CSF_OCCTResourcePath}/StdResource"
export CSF_XCAFDefaults="${CSF_OCCTResourcePath}/StdResource"
export CSF_TObjDefaults="${CSF_OCCTResourcePath}/StdResource"
export CSF_StandardLiteDefaults="${CSF_OCCTResourcePath}/StdResource"
export CSF_IGESDefaults="${CSF_OCCTResourcePath}/XSTEPResource"
export CSF_STEPDefaults="${CSF_OCCTResourcePath}/XSTEPResource"
export CSF_XmlOcafResource="${CSF_OCCTResourcePath}/XmlOcafResource"
export CSF_MIGRATION_TYPES="${CSF_OCCTResourcePath}/StdResource/MigrationSheet.txt"

# ----- Draw Harness special stuff -----
if [ -e "${CSF_OCCTResourcePath}/DrawResources" ]; then
  export DRAWHOME="${CSF_OCCTResourcePath}/DrawResources"
  export CSF_DrawPluginDefaults="${CSF_OCCTResourcePath}/DrawResources"

  if [ -e "${CSF_OCCTResourcePath}/DrawResources/DrawDefault" ]; then
    export DRAWDEFAULT="${CSF_OCCTResourcePath}/DrawResources/DrawDefault"
  fi
fi
