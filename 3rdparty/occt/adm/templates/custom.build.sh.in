#!/bin/bash

if [ "$1" == "@BIN_LETTER@" ]; then
  if [ "$2" == "@COMPILER_BITNESS@" ]; then
    # set environment variables used by OCCT
    export CSF_FPE=@BUILD_ENABLE_FPE_SIGNAL_HANDLER@

    export TCL_DIR="@3RDPARTY_TCL_LIBRARY_DIR@"
    export TK_DIR="@3RDPARTY_TK_LIBRARY_DIR@"
    export FREETYPE_DIR="@3RDPARTY_FREETYPE_LIBRARY_DIR@"
    export FREEIMAGE_DIR="@3RDPARTY_FREEIMAGE_LIBRARY_DIRS@"
    export TBB_DIR="@3RDPARTY_TBB_LIBRARY_DIR@"
    export VTK_DIR="@3RDPARTY_VTK_LIBRARY_DIR@"
    export FFMPEG_DIR="@3RDPARTY_FFMPEG_LIBRARY_DIR@"
    export JEMALLOC_DIR="@3RD<PERSON>RTY_JEMALLOC_LIBRARY_DIR@"

    if [ "x@3RDPARTY_QT_DIR" != "x" ]; then
      export QTDIR="@3RDPARTY_QT_DIR@"
    fi

    export TCL_VERSION_WITH_DOT="@3RDPARTY_TCL_LIBRARY_VERSION_WITH_DOT@"
    export TK_VERSION_WITH_DOT="@3RDPARTY_TK_LIBRARY_VERSION_WITH_DOT@"

    export CSF_OCCTBinPath="@CMAKE_RUNTIME_OUTPUT_DIRECTORY@"
    export CSF_OCCTLibPath="@CMAKE_ARCHIVE_OUTPUT_DIRECTORY@"
    export CSF_OCCTIncludePath="@CMAKE_BINARY_DIR@/@INSTALL_DIR_INCLUDE@"
    export CSF_OCCTResourcePath="@CMAKE_SOURCE_DIR@/src"
    export CSF_OCCTDataPath="@CMAKE_SOURCE_DIR@/data"
    export CSF_OCCTSamplesPath="@CMAKE_SOURCE_DIR@/samples"
    export CSF_OCCTTestsPath="@CMAKE_SOURCE_DIR@/tests"
    export CSF_OCCTDocPath="@CMAKE_SOURCE_DIR@/doc"

    # for compatibility with external application using CASROOT
    export CASROOT="@CMAKE_SOURCE_DIR@"
  fi
fi

