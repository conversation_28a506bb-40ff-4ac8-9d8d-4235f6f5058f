echo off

if /I "%VCVER%" == "@COMPILER@" (
  if "%ARCH%" == "@COMPILER_BITNESS@" (
    rem set environment variables used by OCCT
    set CSF_FPE=@BUILD_ENABLE_FPE_SIGNAL_HANDLER@

    set "TCL_DIR=@3RDPARTY_TCL_DLL_DIR@"
    set "TK_DIR=@3RDPARTY_TK_DLL_DIR@"
    set "FREETYPE_DIR=@3RDPARTY_FREETYPE_DLL_DIR@"
    set "FREEIMAGE_DIR=@3RDPARTY_FREEIMAGE_DLL_DIRS@"
    set "EGL_DIR=@3RDPARTY_EGL_DLL_DIRS@"
    set "GLES2_DIR=@3RDPARTY_GLES2_DLL_DIRS@"
    set "TBB_DIR=@3RDPARTY_TBB_DLL_DIR@"
    set "VTK_DIR=@3RDPARTY_VTK_DLL_DIR@"
    set "FFMPEG_DIR=@3RDPARTY_FFMPEG_DLL_DIR@"
    set "JEMALLOC_DIR=@3RDPARTY_JEMALLOC_LIBRARY_DIR@"
    set "OPENVR_DIR=@3RDPARTY_OPENVR_DLL_DIRS@"

    if not "@3RDPARTY_QT_DIR@" == "" (
      set "QTDIR=@3RDPARTY_QT_DIR@"
    )
    set "TCL_VERSION_WITH_DOT=@3RDPARTY_TCL_LIBRARY_VERSION_WITH_DOT@"
    set "TK_VERSION_WITH_DOT=@3RDPARTY_TK_LIBRARY_VERSION_WITH_DOT@"

    rem CSF_OCCTBinPath and CSF_OCCTLibPath are defined differently for 
    rem multiple and single configuration builds
    set "CSF_OCCTBinPath=@CMAKE_RUNTIME_OUTPUT_DIRECTORY@"
    if ["@CMAKE_RUNTIME_OUTPUT_DIRECTORY@"] == [""] (
      set "CSF_OCCTBinPath=@CMAKE_BINARY_DIR@/win%ARCH%/%VCVER%/bin%3"
    )
    set "CSF_OCCTLibPath=@CMAKE_ARCHIVE_OUTPUT_DIRECTORY@"
    if ["@CMAKE_ARCHIVE_OUTPUT_DIRECTORY@"] == [""] (
      set "CSF_OCCTLibPath=@CMAKE_BINARY_DIR@/win%ARCH%/%VCVER%/lib%3"
    )

    set "CSF_OCCTIncludePath=@CMAKE_BINARY_DIR@/inc"
    set "CSF_OCCTResourcePath=@CMAKE_SOURCE_DIR@/src"
    set "CSF_OCCTDataPath=@CMAKE_SOURCE_DIR@/data"
    set "CSF_OCCTSamplesPath=@CMAKE_SOURCE_DIR@/samples"
    set "CSF_OCCTTestsPath=@CMAKE_SOURCE_DIR@/tests"
    set "CSF_OCCTDocPath=@CMAKE_SOURCE_DIR@/doc"

    rem for compatibility with external application using CASROOT
    set "CASROOT=@CMAKE_SOURCE_DIR@"
  ) 
)
