#!/bin/bash

if [ "$1" == "@BIN_LETTER@" ]; then
  if [ "$2" == "@COMPILER_BITNESS@" ]; then
    # set environment variables used by OCCT
    export CSF_FPE=@BUILD_ENABLE_FPE_SIGNAL_HANDLER@

    export TCL_DIR="@USED_3RDPARTY_TCL_DIR@"
    export TK_DIR="@USED_3RDPARTY_TK_DIR@"
    export FREETYPE_DIR="@USED_3RDPARTY_FREETYPE_DIR@"
    export FREEIMAGE_DIR="@USED_3RDPARTY_FREEIMAGE_DIRS@"
    export TBB_DIR="@USED_3RDPARTY_TBB_DIR@"
    export VTK_DIR="@USED_3RDPARTY_VTK_DIR@"
    export FFMPEG_DIR="@USED_3RDPARTY_FFMPEG_DIR@"
    export JEMALLOC_DIR="@USED_3RDPARTY_JEMALLOC_DIR@"

    if [ "x@USED_3RDPARTY_QT_DIR@" != "x" ]; then
      export QTDIR="@USED_3RDPARTY_QT_DIR@"
    fi

    export TCL_VERSION_WITH_DOT="@3RDPARTY_TCL_LIBRARY_VERSION_WITH_DOT@"
    export TK_VERSION_WITH_DOT="@3RDPARTY_TK_LIBRARY_VERSION_WITH_DOT@"

    export CSF_OCCTBinPath="${CASROOT}/@INSTALL_DIR_BIN@"
    export CSF_OCCTLibPath="${CASROOT}/@INSTALL_DIR_LIB@"
    export CSF_OCCTIncludePath="${CASROOT}/@INSTALL_DIR_INCLUDE@"
    export CSF_OCCTResourcePath="${CASROOT}/@INSTALL_DIR_RESOURCE@"
    export CSF_OCCTDataPath="${CASROOT}/@INSTALL_DIR_DATA@"
    export CSF_OCCTSamplesPath="${CASROOT}/@INSTALL_DIR_SAMPLES@"
    export CSF_OCCTTestsPath="${CASROOT}/@INSTALL_DIR_TESTS@"
    export CSF_OCCTDocPath="${CASROOT}/@INSTALL_DIR_DOC@"
  fi
fi

