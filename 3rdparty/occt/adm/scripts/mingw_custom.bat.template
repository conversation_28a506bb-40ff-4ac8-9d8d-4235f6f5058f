rem Environment configuration template for mingw_build.bat (to be renamed as mingw_custom.bat)
set "aCmakeBin=%ProgramW6432%\CMake\bin"
set "aFreeType=%aCasSrc%/../3rdparty/freetype-2.6.3-mingw-64"
set "aTclTk=%aCasSrc%/../3rdparty/tcltk-8.6.4-mingw-64"
set "aFreeImage=%aCasSrc%/../3rdparty/freeimage-3.17-0-mingw-64"
set "aRapidJson=%aCasSrc%/../3rdparty/rapidjson-1.1.0"
set "aDraco=%aCasSrc%/../3rdparty/draco-1.4-1-mingw-64"

set "aMingwVars=c:\mingw-8.3.0-msys2\mingwvars.bat"

rem Uncomment to customize building steps
rem set "aBuildRoot=work"
rem set "toCMake=1"
rem set "toClean=1"
rem set "toMake=1"
rem set "toInstall=1"
rem set "toPack=1"
rem set "toDebug=0"

rem set "BUILD_ModelingData=ON"
rem set "BUILD_ModelingAlgorithms=ON"
rem set "BUILD_Visualization=ON"
rem set "BUILD_ApplicationFramework=ON"
rem set "BUILD_DataExchange=ON"
rem set "BUILD_DETools=OFF"
rem set "BUILD_Draw=ON"

rem set "USE_RAPIDJSON=ON"
rem set "USE_DRACO=ON"
rem set "USE_FREEIMAGE=ON"
