# environment configuration template for ios_build.sh (to be renamed as ios_custom_env.sh)
export PATH=/Applications/CMake.app/Contents/bin:$PATH
export aFreeType="$aSrcRoot/../3rdparty/freetype-2.10.4-ios"
export aFreeImage="$aSrcRoot/../3rdparty/freeimage-3.18-ios"
export aRapidJson="$aSrcRoot/../3rdparty/rapidjson-1.1.0"
export aDraco="$aSrcRoot/../3rdparty/draco-1.4.1-ios"

# Uncomment to customize building steps
#export isStatic=0
#export toCMake=1
#export toClean=1
#export toMake=1
#export toInstall=1
#export toPack=0
#export toPackFat=1
#export toDebug=0

#export BUILD_ModelingData=ON
#export BUILD_ModelingAlgorithms=ON
#export BUILD_Visualization=ON
#export BUILD_ApplicationFramework=ON
#export BUILD_DataExchange=ON
#export BUILD_DETools=OFF

#export USE_RAPIDJSON=ON
#export USE_DRACO=ON
#export USE_FREEIMAGE=ON

#export IPHONEOS_DEPLOYMENT_TARGET=8.0
#export anAbiList="iPhoneOS|arm64 iPhoneSimulator|arm64 iPhoneSimulator|x86_64"
#export anAbiList="iPhoneOS|arm64"
