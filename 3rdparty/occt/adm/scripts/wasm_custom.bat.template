rem Environment configuration template for wasm_build.bat (to be renamed as wasm_custom.bat)
set "EMSDK_ROOT=%aCasSrc%\..\emsdk"
set "aFreeType=%aCasSrc%\..\3rdparty\freetype-2.7.1-wasm32"
rem set "aRapidJson=%aCasSrc%\..\3rdparty\rapidjson-1.1.0"
rem set "aDraco=%aCasSrc%\..\3rdparty\draco-1.4.1-wasm32"
rem set "aCmakeBin=%ProgramW6432%\CMake\bin"

rem Uncomment to customize building steps
rem set "aBuildRoot=work"
rem set "toCMake=1"
rem set "toClean=1"
rem set "toMake=1"
rem set "toInstall=1"
rem set "toPack=1"
rem set "toDebug=1"
rem set "toBuildSample=1"
rem Source map base (should point to server where C++ sources will be copied)
rem enables -g4 debug building option for WebGL sample and allows navigating C++ source code within JavaScript debugger.
rem set "sourceMapBase=http://localhost:9090/"

rem set "BUILD_ModelingData=ON"
rem set "BUILD_ModelingAlgorithms=ON"
rem set "BUILD_Visualization=ON"
rem set "BUILD_ApplicationFramework=ON"
rem set "BUILD_DataExchange=ON"
rem set "BUILD_DETools=OFF"

rem set "USE_RAPIDJSON=ON"
rem set "USE_DRACO=ON"
rem set "USE_PTHREADS=ON"
