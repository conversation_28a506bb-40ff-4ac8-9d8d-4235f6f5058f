rem Environment configuration template for android_build.bat (to be renamed as android_custom.bat)

rem Paths to 3rd-party tools and libraries
rem call c:\TDM-GCC-64\mingwvars.bat
rem set "PATH=c:\CMake\bin;%PATH%"
rem set "anNdkPath=c:/android-ndk-r12"
rem set "aFreeType=c:/freetype-2.7.1-android"
rem set "aRapidJson=c:/rapidjson-1.1.0"
rem set "aDraco=c:/draco-1.4.1-android"

rem Uncomment to customize building steps
rem set "aBuildRoot=%~dp0..\..\work"
rem set "toCMake=1"
rem set "toClean=0"
rem set "toMake=1"
rem set "toInstall=1"
rem set "toPack=1"
rem set "isStatic=0"

rem Minimal Android platform and CPU architectures
rem set "anNdkApiLevel=21"
rem set "anNdkAbiList=arm64-v8a x86_64"

rem OCCT Modules to build
rem set "BUILD_ModelingData=ON"
rem set "BUILD_ModelingAlgorithms=ON"
rem set "BUILD_Visualization=ON"
rem set "BUILD_ApplicationFramework=ON"
rem set "BUILD_DataExchange=ON"
rem set "BUILD_MODULE_DETools=OFF"

rem Optional 3rd-party libraries to enable
rem set "USE_RAPIDJSON=ON"
rem set "USE_DRACO=ON"
