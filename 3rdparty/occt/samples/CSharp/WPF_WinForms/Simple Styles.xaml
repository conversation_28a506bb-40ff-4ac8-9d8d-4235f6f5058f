﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:d="http://schemas.microsoft.com/expression/interactivedesigner/2006" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="d">
	
	<!-- SimpleStyles.XAML defines a set of control styles which are simplified starting points for creating your own controls -->
	
	<!-- Brushes : These are used to define the color for background, foreground, selection, enabled etc of all controls
	If you want to change the color of a control you can just change the brush; if you want to add a new shape or change arrangement then also edit the template -->
	
	<!-- NormalBrush is used as the Background for SimpleButton, SimpleRepeatButton -->
	<LinearGradientBrush x:Key="NormalBrush" EndPoint="0,1" StartPoint="0,0">
		<GradientStop Color="#EEE" Offset="0.0"/>
		<GradientStop Color="#CCC" Offset="1.0"/>
	</LinearGradientBrush>
	<LinearGradientBrush x:Key="NormalBorderBrush" EndPoint="0,1" StartPoint="0,0">
		<GradientStop Color="#CCC" Offset="0.0"/>
		<GradientStop Color="#444" Offset="1.0"/>
	</LinearGradientBrush>
	
	<!-- LightBrush is used for content areas such as Menu, Tab Control background -->
	<LinearGradientBrush x:Key="LightBrush" EndPoint="0,1" StartPoint="0,0">
		<GradientStop Color="#FFF" Offset="0.0"/>
		<GradientStop Color="#EEE" Offset="1.0"/>
	</LinearGradientBrush>
	
	<!-- MouseOverBrush is used for MouseOver in Button, Radio Button, CheckBox -->
	<LinearGradientBrush x:Key="MouseOverBrush" EndPoint="0,1" StartPoint="0,0">
		<GradientStop Color="#FFF" Offset="0.0"/>
		<GradientStop Color="#AAA" Offset="1.0"/>
	</LinearGradientBrush>
	
	<!-- PressedBrush is used for Pressed in Button, Radio Button, CheckBox -->
	<LinearGradientBrush x:Key="PressedBrush" EndPoint="0,1" StartPoint="0,0">
		<GradientStop Color="#BBB" Offset="0.0"/>
		<GradientStop Color="#EEE" Offset="0.1"/>
		<GradientStop Color="#EEE" Offset="0.9"/>
		<GradientStop Color="#FFF" Offset="1.0"/>
	</LinearGradientBrush>
	<LinearGradientBrush x:Key="PressedBorderBrush" EndPoint="0,1" StartPoint="0,0">
		<GradientStop Color="#444" Offset="0.0"/>
		<GradientStop Color="#888" Offset="1.0"/>
	</LinearGradientBrush>

	<!-- SelectedBackgroundBrush is used for the Selected item in ListBoxItem, ComboBoxItem-->
	<SolidColorBrush x:Key="SelectedBackgroundBrush" Color="#DDD"/>	

	<!-- Disabled Brushes are used for the Disabled look of each control -->
	<SolidColorBrush x:Key="DisabledForegroundBrush" Color="#888"/>
	<SolidColorBrush x:Key="DisabledBackgroundBrush" Color="#EEE"/>
	<SolidColorBrush x:Key="DisabledBorderBrush" Color="#AAA"/>

	<!-- Used for background of ScrollViewer, TreeView, ListBox, Expander, TextBox, Tab Control -->
	<SolidColorBrush x:Key="WindowBackgroundBrush" Color="#FFF"/>
	
	<!-- DefaultedBorderBrush is used to show KeyBoardFocus -->
	<LinearGradientBrush x:Key="DefaultedBorderBrush" EndPoint="0,1" StartPoint="0,0">
		<GradientStop Color="#777" Offset="0.0"/>
		<GradientStop Color="#000" Offset="1.0"/>
	</LinearGradientBrush>

	<SolidColorBrush x:Key="SolidBorderBrush" Color="#888"/>
	<SolidColorBrush x:Key="LightBorderBrush" Color="#AAA"/>
	<SolidColorBrush x:Key="LightColorBrush" Color="#DDD"/>
	
	<!-- Used for Checkmark, Radio button, TreeViewItem, Expander ToggleButton glyphs -->
	<SolidColorBrush x:Key="GlyphBrush" Color="#444"/>
	
	
	<!-- Style and Template pairs are used to define each control Part -->
	<!-- The Style provides default values on the control; the Template gives the elements for each control -->
	
	<!-- SimpleButtonFocusVisual is used to show keyboard focus around a SimpleButton control -->
	<Style x:Key="SimpleButtonFocusVisual">
		<Setter Property="Control.Template">
			<Setter.Value>
				<ControlTemplate>
					<Border>
						<Rectangle Margin="2" Stroke="#60000000" StrokeThickness="1" StrokeDashArray="1 2"/>
					</Border>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>

    <Style TargetType="{x:Type Image}" x:Key="toolbarImageStyle">
        <Style.Triggers>
            <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType={x:Type Button}, AncestorLevel=1}, Path=IsEnabled}" Value="False">
                <Setter Property="Opacity" Value="0.50"></Setter>
            </DataTrigger>
        </Style.Triggers>
    </Style>

    <Style TargetType="MenuItem" x:Key="popupItem">
        <Setter Property="CommandTarget"
                    Value="{Binding Path=PlacementTarget, RelativeSource={RelativeSource AncestorType=ContextMenu} }"/>
    </Style>

    <!-- Simple Button - This control sets brushes on each state. Note that these brushes must be listed above since they are static resources -->
	<Style x:Key="SimpleButton" TargetType="{x:Type Button}" BasedOn="{x:Null}">
		<Setter Property="FocusVisualStyle" Value="{DynamicResource SimpleButtonFocusVisual}"/>
		<Setter Property="Background" Value="{DynamicResource NormalBrush}"/>
		<Setter Property="BorderBrush" Value="{DynamicResource NormalBorderBrush}"/>
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type Button}">
					
					<!-- We use Grid as a root because it is easy to add more elements to customize the button -->
					<Grid x:Name="Grid">
						<Border x:Name="Border" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Padding="{TemplateBinding Padding}"/>
						
						<!-- Content Presenter is where the text content etc is placed by the control -->
						<!-- The bindings are useful so that the control can be parameterized without editing the template -->
						<ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" Margin="{TemplateBinding Padding}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" RecognizesAccessKey="True"/>
					</Grid>
					
					<!--Each state sets a brush on the Border in the template -->
					<ControlTemplate.Triggers>
						<Trigger Property="IsKeyboardFocused" Value="true">
							<Setter Property="BorderBrush" Value="{DynamicResource DefaultedBorderBrush}" TargetName="Border"/>
						</Trigger>
						<Trigger Property="IsMouseOver" Value="true">
							<Setter Property="Background" Value="{DynamicResource MouseOverBrush}" TargetName="Border"/>
						</Trigger>
						<Trigger Property="IsPressed" Value="true">
							<Setter Property="Background" Value="{DynamicResource PressedBrush}" TargetName="Border"/>
							<Setter Property="BorderBrush" Value="{DynamicResource PressedBorderBrush}" TargetName="Border"/>
						</Trigger>
						<Trigger Property="IsEnabled" Value="true"/>
						<Trigger Property="IsEnabled" Value="false">
							<Setter Property="Background" Value="{DynamicResource DisabledBackgroundBrush}" TargetName="Border"/>
							<Setter Property="BorderBrush" Value="{DynamicResource DisabledBorderBrush}" TargetName="Border"/>
							<Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}"/>
						</Trigger>
					</ControlTemplate.Triggers>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<Style x:Key="RadioButtonFocusVisual">
		<Setter Property="Control.Template">
			<Setter.Value>
				<ControlTemplate>
					<Border>
						<Rectangle Margin="15,0,0,0" Stroke="#60000000" StrokeThickness="1" StrokeDashArray="1 2"/>
					</Border>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<Style x:Key="CheckBoxFocusVisual">
		<Setter Property="Control.Template">
			<Setter.Value>
				<ControlTemplate>
					<Border>
						<Rectangle Margin="15,0,0,0" Stroke="#60000000" StrokeThickness="1" StrokeDashArray="1 2"/>
					</Border>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<!-- Simple CheckBox -->
	<Style x:Key="SimpleCheckBox" TargetType="{x:Type CheckBox}">
		<Setter Property="SnapsToDevicePixels" Value="true"/>
		<Setter Property="FocusVisualStyle" Value="{DynamicResource CheckBoxFocusVisual}"/>
		<Setter Property="Background" Value="{DynamicResource NormalBrush}"/>
		<Setter Property="BorderBrush" Value="{DynamicResource NormalBorderBrush}"/>
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type CheckBox}">
					
					<!-- BulletDecorator is used to provide baseline alignment between the checkmark and the Content -->
					<BulletDecorator Background="Transparent">
						<BulletDecorator.Bullet>
							<Grid Width="13" Height="13">
								<Border x:Name="Border" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}"/>
								<Path x:Name="CheckMark" Stroke="{DynamicResource GlyphBrush}" StrokeThickness="2" SnapsToDevicePixels="False" Data="M 0 0 L 13 13 M 0 13 L 13 0"/>
							</Grid>
						</BulletDecorator.Bullet>
						<ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" Margin="{TemplateBinding Padding}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" RecognizesAccessKey="True"/>
					</BulletDecorator>
					
					<!-- This uses Visibility to hide and show the CheckMark on IsChecked -->
					<ControlTemplate.Triggers>
						<Trigger Property="IsChecked" Value="false">
							<Setter Property="Visibility" Value="Collapsed" TargetName="CheckMark"/>
						</Trigger>
						<Trigger Property="IsMouseOver" Value="true">
							<Setter Property="Background" Value="{DynamicResource MouseOverBrush}" TargetName="Border"/>
						</Trigger>
						<Trigger Property="IsPressed" Value="true">
							<Setter Property="Background" Value="{DynamicResource PressedBrush}" TargetName="Border"/>
							<Setter Property="BorderBrush" Value="{DynamicResource PressedBorderBrush}" TargetName="Border"/>
						</Trigger>
						<Trigger Property="IsEnabled" Value="false">
							<Setter Property="Background" Value="{DynamicResource DisabledBackgroundBrush}" TargetName="Border"/>
							<Setter Property="BorderBrush" Value="{DynamicResource DisabledBorderBrush}" TargetName="Border"/>
						</Trigger>
					</ControlTemplate.Triggers>
					
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<!-- Simple Radio Button -->
	<Style x:Key="SimpleRadioButton" TargetType="{x:Type RadioButton}">
		<Setter Property="SnapsToDevicePixels" Value="true"/>
		<Setter Property="FocusVisualStyle" Value="{DynamicResource RadioButtonFocusVisual}"/>
		<Setter Property="Background" Value="{DynamicResource NormalBrush}"/>
		<Setter Property="BorderBrush" Value="{DynamicResource NormalBorderBrush}"/>
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type RadioButton}">
				
					<!-- BulletDecorator is used to provide baseline alignment between the checkmark and the Content -->
					<BulletDecorator Background="Transparent">
						<BulletDecorator.Bullet>
							<Grid Width="13" Height="13">
								<Ellipse x:Name="Ellipse_Border" Fill="{TemplateBinding Background}" Stroke="{TemplateBinding BorderBrush}" StrokeThickness="1"/>
								<Ellipse Margin="4" x:Name="CheckMark" Fill="{DynamicResource GlyphBrush}"/>
							</Grid>
						</BulletDecorator.Bullet>
						<ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" Margin="{TemplateBinding Padding}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" RecognizesAccessKey="True"/>
					</BulletDecorator>
					
					<ControlTemplate.Triggers>
						<Trigger Property="IsChecked" Value="false">
							<Setter Property="Visibility" Value="Collapsed" TargetName="CheckMark"/>
						</Trigger>
						<Trigger Property="IsMouseOver" Value="true">
							<Setter Property="Fill" Value="{DynamicResource MouseOverBrush}" TargetName="Ellipse_Border"/>
						</Trigger>
						<Trigger Property="IsPressed" Value="true">
							<Setter Property="Fill" Value="{DynamicResource PressedBrush}" TargetName="Ellipse_Border"/>
							<Setter Property="Stroke" Value="{DynamicResource GlyphBrush}" TargetName="Ellipse_Border"/>
						</Trigger>
						<Trigger Property="IsEnabled" Value="false">
							<Setter Property="Fill" Value="{DynamicResource DisabledBackgroundBrush}" TargetName="Ellipse_Border"/>
							<Setter Property="Stroke" Value="#40000000" TargetName="Ellipse_Border"/>
							<Setter Property="Foreground" Value="#80000000"/>
						</Trigger>
						
					</ControlTemplate.Triggers>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<!-- Simple Repeat Button - This is used by Simple ScrollBar for the up and down buttons -->
	<Style x:Key="SimpleRepeatButton" d:IsControlPart="True" TargetType="{x:Type RepeatButton}" BasedOn="{x:Null}">
		<Setter Property="Background" Value="{DynamicResource NormalBrush}"/>
		<Setter Property="BorderBrush" Value="{DynamicResource NormalBorderBrush}"/>
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type RepeatButton}">
					<Grid>
						<Border x:Name="Border" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}"/>
						<ContentPresenter HorizontalAlignment="Center" x:Name="ContentPresenter" VerticalAlignment="Center" Content="{TemplateBinding Content}" ContentTemplate="{TemplateBinding ContentTemplate}" ContentTemplateSelector="{TemplateBinding ContentTemplateSelector}"/>
					</Grid>
					<ControlTemplate.Triggers>
						<Trigger Property="IsKeyboardFocused" Value="true">
							<Setter Property="BorderBrush" Value="{DynamicResource DefaultedBorderBrush}" TargetName="Border"/>
						</Trigger>
						<Trigger Property="IsMouseOver" Value="true">
							<Setter Property="Background" Value="{DynamicResource MouseOverBrush}" TargetName="Border"/>
						</Trigger>
						<Trigger Property="IsPressed" Value="true">
							<Setter Property="Background" Value="{DynamicResource PressedBrush}" TargetName="Border"/>
							<Setter Property="BorderBrush" Value="{DynamicResource PressedBorderBrush}" TargetName="Border"/>
						</Trigger>
						<Trigger Property="IsEnabled" Value="false">
							<Setter Property="Background" Value="{DynamicResource DisabledBackgroundBrush}" TargetName="Border"/>
							<Setter Property="BorderBrush" Value="{DynamicResource DisabledBorderBrush}" TargetName="Border"/>
							<Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}"/>
						</Trigger>
					</ControlTemplate.Triggers>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<!-- Simple Thumb - The Thumb is the draggable part of the Scrollbar -->
	<Style x:Key="SimpleThumbStyle" d:IsControlPart="True" TargetType="{x:Type Thumb}" BasedOn="{x:Null}">
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type Thumb}">
					<Grid Margin="0,0,0,0" x:Name="Grid">
						<Rectangle HorizontalAlignment="Stretch" x:Name="Rectangle" VerticalAlignment="Stretch" Width="Auto" Height="Auto" RadiusX="2" RadiusY="2" Fill="{DynamicResource NormalBrush}" Stroke="{DynamicResource NormalBorderBrush}"/>
					</Grid>
					<ControlTemplate.Triggers>
						<Trigger Property="IsFocused" Value="True"/>
						<Trigger Property="IsMouseOver" Value="True"/>
						<Trigger Property="IsEnabled" Value="False"/>
						<Trigger Property="IsDragging" Value="True"/>
					</ControlTemplate.Triggers>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<!-- Simple ScrollRepeatButton Style - This RepeatButton is used above and below the Thumb in the Scrollbar. They are set to transparent si that they do not show over the scrollbar -->
	<Style x:Key="SimpleScrollRepeatButtonStyle" d:IsControlPart="True" TargetType="{x:Type RepeatButton}">
		<Setter Property="Background" Value="Transparent"/>
		<Setter Property="BorderBrush" Value="Transparent"/>
		<Setter Property="IsTabStop" Value="false"/>
		<Setter Property="Focusable" Value="false"/>
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type RepeatButton}">
					<Grid>
						<Rectangle Fill="{TemplateBinding Background}" Stroke="{TemplateBinding BorderBrush}" StrokeThickness="{TemplateBinding BorderThickness}"/>
					</Grid>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<!-- Simple ScrollBar  This makes use of SimpleThumb, SimpleRepeatButton and SimpleScrollRepeatButton -->
	
	<Style x:Key="SimpleScrollBar" TargetType="{x:Type ScrollBar}">
		<Setter Property="Stylus.IsFlicksEnabled" Value="false"/>
		<Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"/>
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type ScrollBar}">
					<Grid x:Name="GridRoot" Width="{DynamicResource {x:Static SystemParameters.VerticalScrollBarWidthKey}}" Background="{TemplateBinding Background}">
						<Grid.RowDefinitions>
							<RowDefinition MaxHeight="18"/>
							<RowDefinition Height="0.00001*"/>
							<RowDefinition MaxHeight="18"/>
						</Grid.RowDefinitions>
						
						<RepeatButton x:Name="DecreaseRepeat" Style="{DynamicResource SimpleRepeatButton}" Command="ScrollBar.LineUpCommand">
							<Grid>
								<Path x:Name="DecreaseArrow" Stroke="{TemplateBinding Foreground}" StrokeThickness="1" Data="M 0 4 L 8 4 L 4 0 Z"/>
							</Grid>
						</RepeatButton>
						
						<!-- Track is a special layout container which sizes the thumb and the repeat button which do jump scrolling either side of it -->
						<Track Grid.Row="1" x:Name="PART_Track" Orientation="Vertical" IsDirectionReversed="true">
							<Track.Thumb>
								<Thumb Style="{DynamicResource SimpleThumbStyle}"/>
							</Track.Thumb>
							<Track.IncreaseRepeatButton>
								<RepeatButton x:Name="PageUp" Style="{DynamicResource SimpleScrollRepeatButtonStyle}" Command="ScrollBar.PageDownCommand"/>
							</Track.IncreaseRepeatButton>
							<Track.DecreaseRepeatButton>
								<RepeatButton x:Name="PageDown" Style="{DynamicResource SimpleScrollRepeatButtonStyle}" Command="ScrollBar.PageUpCommand"/>
							</Track.DecreaseRepeatButton>
						</Track>
						
						<RepeatButton Grid.Row="2" x:Name="IncreaseRepeat" Style="{DynamicResource SimpleRepeatButton}" Command="ScrollBar.LineDownCommand">
							<Grid>
								<Path x:Name="IncreaseArrow" Stroke="{TemplateBinding Foreground}" StrokeThickness="1" Data="M 0 0 L 4 4 L 8 0 Z"/>
							</Grid>
						</RepeatButton>
					</Grid>
					
					<!-- This uses a single template for ScrollBar and rotate it to be Horizontal
					It also changes the commands so that the it does Left and Right instead of Up and Down Commands -->
					<ControlTemplate.Triggers>
						<Trigger Property="Orientation" Value="Horizontal">

							<!-- Rotate the ScrollBar from Vertical to Horizontal -->
							<Setter Property="LayoutTransform" TargetName="GridRoot">
								<Setter.Value>
									<RotateTransform Angle="-90"/>
								</Setter.Value>
							</Setter>
							
							<!-- Track is bound to Orientation internally, so we need to rotate it back to Vertical -->
							<Setter TargetName="PART_Track" Property="Orientation" Value="Vertical"/>
					
							<!-- Change the commands to do Horizontal commands -->
							<Setter Property="Command" Value="ScrollBar.LineLeftCommand" TargetName="DecreaseRepeat"/>
							<Setter Property="Command" Value="ScrollBar.LineRightCommand" TargetName="IncreaseRepeat"/>
							<Setter Property="Command" Value="ScrollBar.PageLeftCommand" TargetName="PageDown"/>
							<Setter Property="Command" Value="ScrollBar.PageRightCommand" TargetName="PageUp"/>
						</Trigger>
					</ControlTemplate.Triggers>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<!-- Simple ScrollViewer 
	ScrollViewer is a Grid control which has a ContentPresenter and a Horizontal and Vertical ScrollBar 
	It is used by ListBox, MenuItem, ComboBox, and TreeView -->
	<Style x:Key="SimpleScrollViewer" TargetType="{x:Type ScrollViewer}" BasedOn="{x:Null}">
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type ScrollViewer}">
					<Grid Background="{TemplateBinding Background}">
						<Grid.ColumnDefinitions>
							<ColumnDefinition Width="*"/>
							<ColumnDefinition Width="Auto"/>
						</Grid.ColumnDefinitions>
						<Grid.RowDefinitions>
							<RowDefinition Height="*"/>
							<RowDefinition Height="Auto"/>
						</Grid.RowDefinitions>
						<ScrollContentPresenter Grid.Column="0" Grid.Row="0" Margin="{TemplateBinding Padding}" Content="{TemplateBinding Content}" ContentTemplate="{TemplateBinding ContentTemplate}" CanContentScroll="{TemplateBinding CanContentScroll}"/>
						
						<!-- The visibility of the ScrollBars is controlled by the implementation of the control -->
						<ScrollBar Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}" Grid.Column="0" Grid.Row="1" x:Name="PART_HorizontalScrollBar" Style="{DynamicResource SimpleScrollBar}" Orientation="Horizontal" Value="{Binding Path=HorizontalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}" ViewportSize="{TemplateBinding ViewportWidth}" Minimum="0" Maximum="{TemplateBinding ScrollableWidth}" />
						<ScrollBar Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}" Grid.Column="1" Grid.Row="0" x:Name="PART_VerticalScrollBar" Style="{DynamicResource SimpleScrollBar}" Orientation="Vertical" Value="{Binding Path=VerticalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}" ViewportSize="{TemplateBinding ViewportHeight}" Minimum="0" Maximum="{TemplateBinding ScrollableHeight}" />
					</Grid>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<!-- Simple ListBox - This uses SimpleScrollViewer to allow items to be scrolled and SimpleListBoxItem to define the look of each item -->
	<Style x:Key="SimpleListBox" TargetType="{x:Type ListBox}">
		<Setter Property="SnapsToDevicePixels" Value="true"/>
		<Setter Property="Background" Value="{DynamicResource WindowBackgroundBrush}"/>
		<Setter Property="BorderBrush" Value="{DynamicResource SolidBorderBrush}"/>
		<Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Auto"/>
		<Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto"/>
		<Setter Property="ScrollViewer.CanContentScroll" Value="True"/>
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type ListBox}">
					<Grid>
						<Border x:Name="Border" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}"/>
						<ScrollViewer Margin="1" Style="{DynamicResource SimpleScrollViewer}" Focusable="false" Background="{TemplateBinding Background}">
							
							<!-- The StackPanel is used to display the children by setting IsItemsHost to be Trus -->
							<StackPanel Margin="2" IsItemsHost="true"/>
							
						</ScrollViewer>
					</Grid>
					<ControlTemplate.Triggers>
						<Trigger Property="IsEnabled" Value="false">
							<Setter Property="Background" Value="{DynamicResource DisabledBackgroundBrush}" TargetName="Border"/>
							<Setter Property="BorderBrush" Value="{DynamicResource DisabledBorderBrush}" TargetName="Border"/>
						</Trigger>
						<Trigger Property="IsGrouping" Value="true">
							<Setter Property="ScrollViewer.CanContentScroll" Value="false"/>
						</Trigger>
					</ControlTemplate.Triggers>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<!-- Simple ListBoxItem - This is used for each Item in a ListBox. The item's content is placed in the ContentPresenter -->
	
	<Style x:Key="SimpleListBoxItem" d:IsControlPart="True" TargetType="{x:Type ListBoxItem}">
		<Setter Property="SnapsToDevicePixels" Value="true"/>
		<Setter Property="OverridesDefaultStyle" Value="true"/>
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type ListBoxItem}">
					<Grid SnapsToDevicePixels="true">
						<Border x:Name="Border" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}"/>
						<ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
					</Grid>
					<ControlTemplate.Triggers>
						
						<!-- Change IsSelected SelectedBackgroundBrush to set the selection color for the items -->
						<Trigger Property="IsSelected" Value="true">
							<Setter Property="Background" Value="{DynamicResource SelectedBackgroundBrush}" TargetName="Border"/>
						</Trigger>
						
						<Trigger Property="IsEnabled" Value="false">
							<Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}"/>
						</Trigger>
					</ControlTemplate.Triggers>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<!-- Simple Expander ToggleButton - This Button is used by the Expander control. When it is toggled it switches visibility on the Up_Arrow and Down_Arrow -->
	<ControlTemplate x:Key="ExpanderToggleButton" TargetType="{x:Type ToggleButton}">
		<Grid>
			<Rectangle Margin="0,0,0,0" x:Name="Rectangle" Fill="Transparent" Stroke="{DynamicResource NormalBorderBrush}"/>
			<Path HorizontalAlignment="Center" x:Name="Up_Arrow" VerticalAlignment="Center" Fill="{DynamicResource GlyphBrush}" Data="M 0 0 L 4 4 L 8 0 Z"/>
			<Path Visibility="Collapsed" HorizontalAlignment="Center" x:Name="Down_Arrow" VerticalAlignment="Center" Fill="{DynamicResource GlyphBrush}" Data="M 0 4 L 4 0 L 8 4 Z"/>
		</Grid>
		<ControlTemplate.Triggers>
			<Trigger Property="IsMouseOver" Value="true">
				<Setter Property="Fill" Value="{DynamicResource MouseOverBrush}" TargetName="Rectangle"/>
			</Trigger>
			<Trigger Property="IsPressed" Value="true">
				<Setter Property="Fill" Value="{DynamicResource PressedBrush}" TargetName="Rectangle"/>
			</Trigger>
			<Trigger Property="IsChecked" Value="true">
				<Setter Property="Visibility" Value="Visible" TargetName="Down_Arrow"/>
				<Setter Property="Visibility" Value="Collapsed" TargetName="Up_Arrow"/>
			</Trigger>
			<Trigger Property="IsEnabled" Value="False">
				<Setter Property="Fill" Value="{DynamicResource DisabledBackgroundBrush}" TargetName="Rectangle"/>
				<Setter Property="Stroke" Value="{DynamicResource DisabledBorderBrush}" TargetName="Rectangle"/>
				<Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}"/>
				<Setter Property="Fill" Value="{DynamicResource DisabledForegroundBrush}" TargetName="Up_Arrow"/>
			</Trigger>
		</ControlTemplate.Triggers>
	</ControlTemplate>
	
	<!-- Simple Expander 
	 This uses the Simpler ExpanderToggleButton. It sets Visibility on the ContentPresenter to expand
	 Limitations : The Simple Expander only expands down -->
	<Style x:Key="SimpleExpander" TargetType="{x:Type Expander}">
		<Setter Property="Background" Value="{DynamicResource LightBrush}"/>
		<Setter Property="BorderBrush" Value="{DynamicResource NormalBorderBrush}"/>
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type Expander}">
					<Grid>
						<Grid.RowDefinitions>
							<RowDefinition Height="Auto"/>
							<RowDefinition Height="*" x:Name="ContentRow"/>
						</Grid.RowDefinitions>
						<Border Grid.Row="0" x:Name="Border" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="2,2,0,0">
							<Grid>
								<Grid.ColumnDefinitions>
									<ColumnDefinition Width="20"/>
									<ColumnDefinition Width="*"/>
								</Grid.ColumnDefinitions>
								<ToggleButton Template="{DynamicResource ExpanderToggleButton}" Background="{DynamicResource NormalBrush}" IsChecked="{Binding Path=IsExpanded, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" OverridesDefaultStyle="True"/>
								<ContentPresenter Grid.Column="1" Margin="4" RecognizesAccessKey="True" ContentSource="Header"/>
							</Grid>
						</Border>
						<Border Visibility="Collapsed" Grid.Row="1" x:Name="ExpandSite" Background="{DynamicResource WindowBackgroundBrush}" BorderBrush="{DynamicResource SolidBorderBrush}" BorderThickness="1,0,1,1" CornerRadius="0,0,2,2">
							<ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" Margin="{TemplateBinding Padding}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" Focusable="false"/>
						</Border>
					</Grid>
					<ControlTemplate.Triggers>
						<Trigger Property="IsExpanded" Value="True">
							<Setter Property="Visibility" Value="Visible" TargetName="ExpandSite"/>
						</Trigger>
						<Trigger Property="IsEnabled" Value="False">
							<Setter Property="Background" Value="{DynamicResource DisabledBackgroundBrush}" TargetName="Border"/>
							<Setter Property="BorderBrush" Value="{DynamicResource DisabledBorderBrush}" TargetName="Border"/>
							<Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}"/>
						</Trigger>
					</ControlTemplate.Triggers>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<!-- Simple ComboBox Toggle Button - This is used in ComboBox to expand and collapse the ComboBox Popup-->
	<ControlTemplate x:Key="ComboBoxToggleButton" TargetType="{x:Type ToggleButton}">
		<Grid>
			<Grid.ColumnDefinitions>
				<ColumnDefinition/>
				<ColumnDefinition Width="20"/>
			</Grid.ColumnDefinitions>
			<Rectangle Grid.ColumnSpan="2" HorizontalAlignment="Stretch" x:Name="Rectangle" VerticalAlignment="Stretch" Width="Auto" Height="Auto" RadiusX="5" RadiusY="5" Fill="{DynamicResource NormalBrush}" Stroke="{DynamicResource NormalBorderBrush}"/>
			<Rectangle Grid.Column="0" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Width="Auto" Height="Auto" RadiusX="5" RadiusY="5" Fill="{DynamicResource WindowBackgroundBrush}" Stroke="{DynamicResource NormalBorderBrush}"/>
			<Path Grid.Column="1" HorizontalAlignment="Center" x:Name="Arrow" VerticalAlignment="Center" Fill="{DynamicResource GlyphBrush}" Data="M 0 0 L 4 4 L 8 0 Z"/>
		</Grid>
		<ControlTemplate.Triggers>
			<Trigger Property="IsMouseOver" Value="true">
				<Setter Property="Fill" Value="{DynamicResource MouseOverBrush}" TargetName="Rectangle"/>
			</Trigger>
			<Trigger Property="IsChecked" Value="true">
				<Setter Property="Fill" Value="{DynamicResource PressedBrush}" TargetName="Rectangle"/>
			</Trigger>
			<Trigger Property="IsEnabled" Value="False">
				<Setter Property="Fill" Value="{DynamicResource DisabledBackgroundBrush}" TargetName="Rectangle"/>
				<Setter Property="Stroke" Value="{DynamicResource DisabledBorderBrush}" TargetName="Rectangle"/>
				<Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}"/>
				<Setter Property="Fill" Value="{DynamicResource DisabledForegroundBrush}" TargetName="Arrow"/>
			</Trigger>
		</ControlTemplate.Triggers>
	</ControlTemplate>
	
	<!-- This is the area which contains the selected item in the ComboBox -->
	
	<ControlTemplate x:Key="ComboBoxTextBox" TargetType="{x:Type TextBox}">
		<!-- This must be named as PART_ContentHost -->
		<Border x:Name="PART_ContentHost" Focusable="False" Background="{TemplateBinding Background}"/>
	</ControlTemplate>
	
	<!-- Simple ComboBox 
	This uses the ComboBoxToggleButton to expand and collapse a Popup control
	SimpleScrollViewer to allow items to be scrolled and SimpleComboBoxItem to define the look of each item 
	The Popup shows a list of items in a StackPanel-->
	
	<Style x:Key="SimpleComboBox" TargetType="{x:Type ComboBox}">
		<Setter Property="SnapsToDevicePixels" Value="true"/>
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type ComboBox}">
					<Grid>
						<!-- The ToggleButton is databound to the ComboBox itself to toggle IsDropDownOpen -->
						<ToggleButton Grid.Column="2" Template="{DynamicResource ComboBoxToggleButton}" x:Name="ToggleButton" Focusable="false" IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" ClickMode="Press"/>
						<ContentPresenter HorizontalAlignment="Left" Margin="3,3,23,3" x:Name="ContentSite" VerticalAlignment="Center" Content="{TemplateBinding SelectionBoxItem}" ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}" ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}" IsHitTestVisible="False"/>
						
						<!-- The TextBox must be named PART_EditableTextBox or ComboBox will not recognize it -->
						<TextBox Visibility="Hidden" Template="{DynamicResource ComboBoxTextBox}" HorizontalAlignment="Left" Margin="3,3,23,3" x:Name="PART_EditableTextBox" Style="{x:Null}" VerticalAlignment="Center" Focusable="True" Background="Transparent" IsReadOnly="{TemplateBinding IsReadOnly}"/>
						
						<!-- The Popup shows the list of items in the ComboBox. IsOpen is databound to IsDropDownOpen which is toggled via the ComboBoxToggleButton -->
						<Popup IsOpen="{TemplateBinding IsDropDownOpen}" Placement="Bottom" x:Name="Popup" Focusable="False" AllowsTransparency="True" PopupAnimation="Slide">
							<Grid MaxHeight="{TemplateBinding MaxDropDownHeight}" MinWidth="{TemplateBinding ActualWidth}" x:Name="DropDown" SnapsToDevicePixels="True">
								<Border x:Name="DropDownBorder" Background="{DynamicResource WindowBackgroundBrush}" BorderBrush="{DynamicResource SolidBorderBrush}" BorderThickness="1"/>
								<ScrollViewer Margin="4,6,4,6" Style="{DynamicResource SimpleScrollViewer}" SnapsToDevicePixels="True" HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">
							
									<!-- The StackPanel is used to display the children by setting IsItemsHost to be True -->
									<StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Contained"/>
								
								</ScrollViewer>
							</Grid>
						</Popup>
					</Grid>
					<ControlTemplate.Triggers>						
						<!-- This forces the DropDown to have a minimum size if it is empty -->
						<Trigger Property="HasItems" Value="false">
							<Setter Property="MinHeight" Value="95" TargetName="DropDownBorder"/>
						</Trigger>
						<Trigger Property="IsEnabled" Value="false">
							<Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}"/>
						</Trigger>
						<Trigger Property="IsGrouping" Value="true">
							<Setter Property="ScrollViewer.CanContentScroll" Value="false"/>
						</Trigger>
						<Trigger Property="AllowsTransparency" SourceName="Popup" Value="true">
							<Setter Property="CornerRadius" Value="4" TargetName="DropDownBorder"/>
							<Setter Property="Margin" Value="0,2,0,0" TargetName="DropDownBorder"/>
						</Trigger>
						<Trigger Property="IsEditable" Value="true">
							<Setter Property="IsTabStop" Value="false"/>
							<Setter Property="Visibility" Value="Visible" TargetName="PART_EditableTextBox"/>
							<Setter Property="Visibility" Value="Hidden" TargetName="ContentSite"/>
						</Trigger>
					</ControlTemplate.Triggers>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<!-- Simple ComboBoxItem - This is used for each item inside of the ComboBox. You can change the selected color of each item below-->
	<Style x:Key="SimpleComboBoxItem" d:IsControlPart="True" TargetType="{x:Type ComboBoxItem}">
		<Setter Property="SnapsToDevicePixels" Value="true"/>
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type ComboBoxItem}">
					<Grid SnapsToDevicePixels="true">
						<Border x:Name="Border" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}"/>
						<ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
					</Grid>
					<ControlTemplate.Triggers>
					
						<!-- Change IsHighlighted SelectedBackgroundBrush to set the selection color for the items -->
						<Trigger Property="IsHighlighted" Value="true">
							<Setter Property="Background" Value="{DynamicResource SelectedBackgroundBrush}" TargetName="Border"/>
						</Trigger>
						
						<Trigger Property="IsEnabled" Value="false">
							<Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}"/>
						</Trigger>
					</ControlTemplate.Triggers>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<!-- Simple SimpleProgressBar
	 The template uses two Border controls to show the Track and Progress 
	 Limitations : It only supports a horizontal orientated ProgressBar -->
	<Style x:Key="SimpleProgressBar" TargetType="{x:Type ProgressBar}">
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type ProgressBar}">
					<Grid>
					
						<!-- This Border is the track. It must be named PART_Track -->
						<Border x:Name="PART_Track" Background="{DynamicResource PressedBrush}" BorderBrush="{DynamicResource SolidBorderBrush}" BorderThickness="1" CornerRadius="2"/>
						
						<!-- This Border shows progress. It must be named PART_Indicator for the control to function -->
						<Border HorizontalAlignment="Left" x:Name="PART_Indicator" Background="{DynamicResource MouseOverBrush}" BorderBrush="{DynamicResource NormalBorderBrush}" BorderThickness="1" CornerRadius="2"/>
					
					</Grid>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<!-- Simple TextBox -->
	<Style x:Key="SimpleTextBox" TargetType="{x:Type TextBox}">
		<Setter Property="KeyboardNavigation.TabNavigation" Value="None"/>
		<Setter Property="FocusVisualStyle" Value="{x:Null}"/>
		<Setter Property="AllowDrop" Value="true"/>
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type TextBox}">
					<Grid>
						<Border x:Name="Border" Background="{DynamicResource WindowBackgroundBrush}" BorderBrush="{DynamicResource SolidBorderBrush}" BorderThickness="1" Padding="2" CornerRadius="2">
							
							<!-- The implementation places the Content into the ScrollViewer. It must be named PART_ContentHost for the control to function -->
							<ScrollViewer Margin="0" x:Name="PART_ContentHost" Style="{DynamicResource SimpleScrollViewer}" Background="{TemplateBinding Background}"/>
						
						</Border>
					</Grid>
					<ControlTemplate.Triggers>
						<Trigger Property="IsEnabled" Value="False">
							<Setter Property="Background" Value="{DynamicResource DisabledBackgroundBrush}" TargetName="Border"/>
							<Setter Property="BorderBrush" Value="{DynamicResource DisabledBackgroundBrush}" TargetName="Border"/>
							<Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}"/>
						</Trigger>
					</ControlTemplate.Triggers>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<!-- Simple Label - This template is just a ContentPresenter that shows the content of the Label -->
	<Style x:Key="SimpleLabel" TargetType="{x:Type Label}">
		<Setter Property="HorizontalContentAlignment" Value="Left"/>
		<Setter Property="VerticalContentAlignment" Value="Top"/>
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type Label}">
					<Grid>
						<ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" RecognizesAccessKey="True"/>
					</Grid>
					<ControlTemplate.Triggers>
						<Trigger Property="IsEnabled" Value="false"/>
					</ControlTemplate.Triggers>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<!-- Simple Menu - This template uses a StackPanel to layout MenuItems --> 
	<Style x:Key="SimpleMenu" TargetType="{x:Type Menu}">
		<Setter Property="Background" Value="{DynamicResource LightBrush}"/>
		<Setter Property="BorderBrush" Value="{DynamicResource NormalBorderBrush}"/>
		<Setter Property="SnapsToDevicePixels" Value="True"/>
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type Menu}">
					<Grid>
						<Border Margin="1" x:Name="Border" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}"/>
						<StackPanel Background="{TemplateBinding Background}" IsItemsHost="True" ClipToBounds="True" Orientation="Horizontal"/>
					</Grid>
					<ControlTemplate.Triggers>
						<Trigger Property="IsEnabled" Value="False">
							<Setter Property="Background" Value="{DynamicResource DisabledBackgroundBrush}" TargetName="Border"/>
							<Setter Property="BorderBrush" Value="{DynamicResource DisabledBorderBrush}" TargetName="Border"/>
						</Trigger>
					</ControlTemplate.Triggers>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<!-- This BitmapEffect is used by the Simple MenuItem -->
	<DropShadowBitmapEffect x:Key="PopupDropShadow" ShadowDepth="1.5" Softness="0.15"/>
	
	<!-- Simple MenuItem - The template uses triggers to provide four different arrangements of menu item which are set via the Role property --> 
	<Style x:Key="SimpleMenuItem" TargetType="{x:Type MenuItem}">
		<Setter Property="HorizontalContentAlignment" Value="{Binding Path=HorizontalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}"/>
		<Setter Property="VerticalContentAlignment" Value="{Binding Path=VerticalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}"/>
		<Setter Property="Background" Value="Transparent"/>
		<Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.MenuTextBrushKey}}"/>
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type MenuItem}">
					<Border x:Name="Border" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
						<Grid>
							
							<!-- The Grid is used to hold together columns for an Icon, Content, Glyph checkmark and Arrow to show the next level
							 Size sharing is used in Grid so that the Icon, Content, Arrow for each MenuItem align together -->
							<Grid.ColumnDefinitions>
								<ColumnDefinition MinWidth="17" Width="Auto" SharedSizeGroup="MenuItemIconColumnGroup"/>
								<ColumnDefinition Width="*"/>
								<ColumnDefinition Width="Auto" SharedSizeGroup="MenuItemIGTColumnGroup"/>
								<ColumnDefinition Width="14"/>
							</Grid.ColumnDefinitions>
							
							<!-- ContentPresenter to show an Icon if needed -->
							<ContentPresenter Margin="4,0,6,0" x:Name="Icon" VerticalAlignment="Center" ContentSource="Icon"/>
							
							<!-- Glyph is a checkmark if needed for a checkable menu -->
							<Grid Visibility="Hidden" Margin="4,0,6,0" x:Name="GlyphPanel" VerticalAlignment="Center">
								<Path x:Name="GlyphPanelpath" VerticalAlignment="Center" Fill="{TemplateBinding Foreground}" Data="M0,2 L0,4.8 L2.5,7.4 L7.1,2.8 L7.1,0 L2.5,4.6 z" FlowDirection="LeftToRight"/>
							</Grid>
							
							<!-- Content for the menu text etc -->
							<ContentPresenter Grid.Column="1" Margin="{TemplateBinding Padding}" x:Name="HeaderHost" RecognizesAccessKey="True" ContentSource="Header"/>
							
							<!-- Arrow drawn path which points to the next level of the menu -->
							<Grid Grid.Column="3" Margin="4,0,6,0" x:Name="ArrowPanel" VerticalAlignment="Center">
								<Path x:Name="ArrowPanelPath" VerticalAlignment="Center" Fill="{TemplateBinding Foreground}" Data="M0,0 L0,8 L4,4 z"/>
							</Grid>
							
							<!-- The Popup is the body of the menu which expands down or across depending on the level of the item -->
							<Popup IsOpen="{Binding Path=IsSubmenuOpen, RelativeSource={RelativeSource TemplatedParent}}" Placement="Right" x:Name="SubMenuPopup" Focusable="false" AllowsTransparency="true" PopupAnimation="{DynamicResource {x:Static SystemParameters.MenuPopupAnimationKey}}" VerticalOffset="-3">
								<Grid x:Name="SubMenu">
									<Border x:Name="SubMenuBorder" Background="{DynamicResource WindowBackgroundBrush}" BorderBrush="{DynamicResource SolidBorderBrush}" BorderThickness="1"/>
									
									<!-- StackPanel holds children of the menu. This is set bu IsItemsHost=True -->
									<StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Cycle"/>
								</Grid>
							</Popup>
							
						</Grid>
					</Border>
					
					<!-- These triggers re-configure the four arrangements of MenuItem to show different levels of menu via Role -->
					<ControlTemplate.Triggers>
					
						<!-- Role = TopLevelHeader : this is the root menu item in a menu; the Popup expands down -->
						<Trigger Property="Role" Value="TopLevelHeader">
							<Setter Property="Margin" Value="0,1,0,1"/>
							<Setter Property="Padding" Value="6,3,6,3"/>
							<Setter Property="Grid.IsSharedSizeScope" Value="true"/>
							<Setter Property="Placement" Value="Bottom" TargetName="SubMenuPopup"/>
							<Setter Property="Visibility" Value="Collapsed" TargetName="ArrowPanel"/>
						</Trigger>
						
						<!-- Role = TopLevelItem :  this is a child menu item from the top level without any child items-->
						<Trigger Property="Role" Value="TopLevelItem">
							<Setter Property="Margin" Value="0,1,0,1"/>
							<Setter Property="Padding" Value="6,3,6,3"/>
							<Setter Property="Visibility" Value="Collapsed" TargetName="ArrowPanel"/>
						</Trigger>
						
						<!-- Role = SubMenuHeader : this is a child menu item which does not have children -->
						<Trigger Property="Role" Value="SubmenuHeader">
							<Setter Property="DockPanel.Dock" Value="Top"/>
							<Setter Property="Padding" Value="0,2,0,2"/>
							<Setter Property="Grid.IsSharedSizeScope" Value="true"/>
						</Trigger>
						
						<!-- Role = SubMenuItem : this is a child menu item which has children-->
						<Trigger Property="Role" Value="SubmenuItem">
							<Setter Property="DockPanel.Dock" Value="Top"/>
							<Setter Property="Padding" Value="0,2,0,2"/>
							<Setter Property="Visibility" Value="Collapsed" TargetName="ArrowPanel"/>
						</Trigger>
						<Trigger Property="IsSuspendingPopupAnimation" Value="true">
							<Setter Property="PopupAnimation" Value="None" TargetName="SubMenuPopup"/>
						</Trigger>
						
						<!-- If no Icon is present the we collapse the Icon Content -->
						<Trigger Property="Icon" Value="{x:Null}">
							<Setter Property="Visibility" Value="Collapsed" TargetName="Icon"/>
						</Trigger>
						
						<!-- The GlyphPanel contains the CheckMark -->
						<Trigger Property="IsChecked" Value="true">
							<Setter Property="Visibility" Value="Visible" TargetName="GlyphPanel"/>
							<Setter Property="Visibility" Value="Collapsed" TargetName="Icon"/>
						</Trigger>
						
						<Trigger Property="AllowsTransparency" SourceName="SubMenuPopup" Value="true">
							<Setter Property="Margin" Value="0,0,3,3" TargetName="SubMenu"/>
							<Setter Property="SnapsToDevicePixels" Value="true" TargetName="SubMenu"/>
							<Setter Property="BitmapEffect" Value="{DynamicResource PopupDropShadow}" TargetName="SubMenuBorder"/>
						</Trigger>
						
						<!-- Using the system colors for the Menu Highlight and IsEnabled-->
						<Trigger Property="IsHighlighted" Value="true">
							<Setter Property="Background" Value="{DynamicResource {x:Static SystemColors.HighlightBrushKey}}" TargetName="Border"/>
							<Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.HighlightTextBrushKey}}"/>
						</Trigger>
						<Trigger Property="IsEnabled" Value="false">
							<Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.GrayTextBrushKey}}"/>
						</Trigger>
					</ControlTemplate.Triggers>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<!-- Simple Separator - This template is used for a Separator in a menu --> 
	<Style x:Key="SimpleSeparator" TargetType="{x:Type Separator}">
		<Setter Property="Height" Value="1"/>
		<Setter Property="Margin" Value="0,2,0,2"/>
		<Setter Property="Focusable" Value="false"/>
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type Separator}">
					<Border BorderBrush="{DynamicResource SolidBorderBrush}" BorderThickness="1"/>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>

	<!-- Simple TabControl 
	This template uses Simple TabItem for each Tab. The TabItems are placed in the TabPanel
	Limitations : The Simple TabControl only allow the Tabs to be shown at the top of the Tab control. You can re-position the TabPanel to change this-->
	
	<Style x:Key="SimpleTabControl" TargetType="{x:Type TabControl}">
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type TabControl}">
					<Grid KeyboardNavigation.TabNavigation="Local">
						<Grid.RowDefinitions>
							<RowDefinition Height="Auto"/>
							<RowDefinition Height="*"/>
						</Grid.RowDefinitions>
						
						<!-- TabPanel is a layout container which allows the TabItems to wrap and re-order when selected
						The implementation knows to use this control because it is marked IsItemsHost = True -->
						<TabPanel Grid.Row="0" Margin="0,0,4,-1" x:Name="HeaderPanel" Background="Transparent" IsItemsHost="True" Panel.ZIndex="1" KeyboardNavigation.TabIndex="1"/>
						
						<Border Grid.Row="1" x:Name="Border" Background="{DynamicResource WindowBackgroundBrush}" BorderBrush="{DynamicResource SolidBorderBrush}" BorderThickness="1" CornerRadius="2" KeyboardNavigation.DirectionalNavigation="Contained" KeyboardNavigation.TabNavigation="Local" KeyboardNavigation.TabIndex="2">
							
							<!-- The implementation switches the content. This control must be named PART_SelectedContentHost -->
							<ContentPresenter Margin="4" x:Name="PART_SelectedContentHost" ContentSource="SelectedContent"/>
							
						</Border>
					</Grid>
					
					<ControlTemplate.Triggers>
						<Trigger Property="IsEnabled" Value="False">
							<Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}"/>
							<Setter Property="BorderBrush" Value="{DynamicResource DisabledBorderBrush}" TargetName="Border"/>
						</Trigger>
					</ControlTemplate.Triggers>
				
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<!--Simple TabItem -->
	<Style x:Key="SimpleTabItem" d:IsControlPart="True" TargetType="{x:Type TabItem}">
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type TabItem}">
					<Grid>
						<Border Margin="0,0,-4,0" x:Name="Border" Background="{DynamicResource LightBrush}" BorderBrush="{DynamicResource SolidBorderBrush}" BorderThickness="1,1,1,1" CornerRadius="2,12,0,0">
							<ContentPresenter HorizontalAlignment="Center" Margin="12,2,12,2" x:Name="ContentSite" VerticalAlignment="Center" RecognizesAccessKey="True" ContentSource="Header"/>
						</Border>
					</Grid>
					<ControlTemplate.Triggers>
						<Trigger Property="IsSelected" Value="True">
							<Setter Property="Panel.ZIndex" Value="100"/>
							<Setter Property="Background" Value="{DynamicResource WindowBackgroundBrush}" TargetName="Border"/>
							<Setter Property="BorderThickness" Value="1,1,1,0" TargetName="Border"/>
						</Trigger>
						<Trigger Property="IsEnabled" Value="False">
							<Setter Property="Background" Value="{DynamicResource DisabledBackgroundBrush}" TargetName="Border"/>
							<Setter Property="BorderBrush" Value="{DynamicResource DisabledBorderBrush}" TargetName="Border"/>
							<Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}"/>
						</Trigger>
					</ControlTemplate.Triggers>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<!--Simple Simple SliderThumb - The Thumb is the draggable part of a Slider-->
	<Style x:Key="SimpleSliderThumb" d:IsControlPart="True" TargetType="{x:Type Thumb}">
		<Setter Property="SnapsToDevicePixels" Value="true"/>
		<Setter Property="Height" Value="14"/>
		<Setter Property="Width" Value="14"/>
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type Thumb}">
					<Grid>
						<Ellipse x:Name="Ellipse" Fill="{DynamicResource NormalBrush}" Stroke="{DynamicResource NormalBorderBrush}" StrokeThickness="1"/>
					</Grid>
					<ControlTemplate.Triggers>
						<Trigger Property="IsMouseOver" Value="True">
							<Setter Property="Fill" Value="{DynamicResource MouseOverBrush}" TargetName="Ellipse"/>
						</Trigger>
						<Trigger Property="IsEnabled" Value="false">
							<Setter Property="Fill" Value="{DynamicResource DisabledBackgroundBrush}" TargetName="Ellipse"/>
						</Trigger>
					</ControlTemplate.Triggers>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<!--Simple Simple Slider
	Similar to ScrollBar this template uses Track to layout the draggable Thumb which has an up and down repeat button
	It uses Simple SliderThumb and SimpleScrollRepeatButtonStyle for the page up and down repeat buttons -->
	<Style x:Key="SimpleSlider" TargetType="{x:Type Slider}">
		<Setter Property="Background" Value="{DynamicResource LightBrush}"/>
		<Setter Property="BorderBrush" Value="{DynamicResource NormalBorderBrush}"/>
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type Slider}">
					<Grid x:Name="GridRoot">
						<Grid.RowDefinitions>
							<RowDefinition Height="Auto"/>
							<RowDefinition Height="Auto" MinHeight="{TemplateBinding MinHeight}"/>
							<RowDefinition Height="Auto"/>
						</Grid.RowDefinitions>
						
						<!-- TickBar shows the ticks for Slider -->
						<TickBar Visibility="Collapsed" x:Name="TopTick" Height="4" SnapsToDevicePixels="True" Placement="Top" Fill="{DynamicResource GlyphBrush}"/>
						<Border Grid.Row="1" Margin="0" x:Name="Border" Height="4" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="2"/>
						
						<!-- The Track lays out the repeat buttons and thumb -->
						<Track Grid.Row="1" x:Name="PART_Track">
							<Track.Thumb>
								<Thumb Style="{DynamicResource SimpleSliderThumb}"/>
							</Track.Thumb>
							<Track.IncreaseRepeatButton>
								<RepeatButton Style="{DynamicResource SimpleScrollRepeatButtonStyle}" Command="Slider.IncreaseLarge"/>
							</Track.IncreaseRepeatButton>
							<Track.DecreaseRepeatButton>
								<RepeatButton Style="{DynamicResource SimpleScrollRepeatButtonStyle}" Command="Slider.DecreaseLarge"/>
							</Track.DecreaseRepeatButton>
						</Track>
						
						<TickBar Visibility="Collapsed" Grid.Row="2" x:Name="BottomTick" Height="4" SnapsToDevicePixels="True" Placement="Bottom" Fill="{TemplateBinding Foreground}"/>
					</Grid>
					<ControlTemplate.Triggers>
						<Trigger Property="TickPlacement" Value="TopLeft">
							<Setter Property="Visibility" Value="Visible" TargetName="TopTick"/>
						</Trigger>
						<Trigger Property="TickPlacement" Value="BottomRight">
							<Setter Property="Visibility" Value="Visible" TargetName="BottomTick"/>
						</Trigger>
						<Trigger Property="TickPlacement" Value="Both">
							<Setter Property="Visibility" Value="Visible" TargetName="TopTick"/>
							<Setter Property="Visibility" Value="Visible" TargetName="BottomTick"/>
						</Trigger>
						<Trigger Property="IsEnabled" Value="false">
							<Setter Property="Background" Value="{DynamicResource DisabledBackgroundBrush}" TargetName="Border"/>
							<Setter Property="BorderBrush" Value="{DynamicResource DisabledBorderBrush}" TargetName="Border"/>
						</Trigger>
						
						<!-- Use a rotation to create a Vertical Slider form the default Horizontal -->
						<Trigger Property="Orientation" Value="Vertical">
							<Setter Property="LayoutTransform" TargetName="GridRoot">
								<Setter.Value>
									<RotateTransform Angle="-90"/>
								</Setter.Value>
							</Setter>
							<!-- Track rotates itself based on orientation so need to force it back -->
							<Setter TargetName="PART_Track" Property="Orientation" Value="Horizontal"/>
						</Trigger>
						
					</ControlTemplate.Triggers>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<!--Simple Tree View - This lays out TreeViewItems within a ScrollViewer -->
	<Style x:Key="SimpleTreeView" TargetType="{x:Type TreeView}">
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type TreeView}">
					<Grid>
						<Border x:Name="Border" Background="{DynamicResource WindowBackgroundBrush}" BorderBrush="{DynamicResource SolidBorderBrush}" BorderThickness="1" CornerRadius="1">
							<ScrollViewer Style="{DynamicResource SimpleScrollViewer}" Focusable="False" Background="{TemplateBinding Background}" Padding="4" HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="False">
								<ItemsPresenter/>
							</ScrollViewer>
						</Border>
					</Grid>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<!--Simple TreeViewItem ToggleButton - + and - button to expand and collapse a TreeViewItem -->
	<Style x:Key="SimpleTreeViewItemToggleButton" d:IsControlPart="True" TargetType="{x:Type ToggleButton}">
		<Setter Property="Focusable" Value="False"/>
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type ToggleButton}">
					<Grid Width="15" Height="13" Background="Transparent">
						<Path Visibility="Collapsed" HorizontalAlignment="Left" Margin="1,1,1,1" x:Name="IsExpandedPath" VerticalAlignment="Center" Fill="{DynamicResource GlyphBrush}" Data="M 0 4 L 8 4 L 4 8 Z"/>
						<Path HorizontalAlignment="Left" Margin="1,1,1,1" x:Name="ExpandPath" VerticalAlignment="Center" Fill="{DynamicResource GlyphBrush}" Data="M 4 0 L 8 4 L 4 8 Z"/>
					</Grid>
					<ControlTemplate.Triggers>
						<Trigger Property="IsChecked" Value="True">
							<Setter Property="Visibility" Value="Visible" TargetName="IsExpandedPath"/>
							<Setter Property="Visibility" Value="Collapsed" TargetName="ExpandPath"/>
						</Trigger>
					</ControlTemplate.Triggers>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
	
	<!--Simple TreeViewItem - The TreeViewItem template has a header which shows the Item and a Body which is an ItemsHost control which expands to show child items-->
	<Style x:Key="SimpleTreeViewItem" d:IsControlPart="True" TargetType="{x:Type TreeViewItem}">
		<Setter Property="Background" Value="Transparent"/>
		<Setter Property="HorizontalContentAlignment" Value="{Binding Path=HorizontalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}"/>
		<Setter Property="VerticalContentAlignment" Value="{Binding Path=VerticalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}"/>
		<Setter Property="Padding" Value="1,0,0,0"/>
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="{x:Type TreeViewItem}">
					<Grid>
						<Grid.ColumnDefinitions>
							<ColumnDefinition MinWidth="19" Width="Auto"/>
							<ColumnDefinition Width="Auto"/>
							<ColumnDefinition Width="*"/>
						</Grid.ColumnDefinitions>
						<Grid.RowDefinitions>
							<RowDefinition Height="Auto"/>
							<RowDefinition/>
						</Grid.RowDefinitions>
						<ToggleButton x:Name="Expander" Style="{DynamicResource SimpleTreeViewItemToggleButton}" IsChecked="{Binding Path=IsExpanded, RelativeSource={RelativeSource TemplatedParent}}" ClickMode="Press"/>
						<Border Grid.Column="1" x:Name="Selection_Border" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Padding="{TemplateBinding Padding}">
							<ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" x:Name="PART_Header" ContentSource="Header"/>
						</Border>
						<ItemsPresenter Grid.Column="1" Grid.ColumnSpan="2" Grid.Row="1" x:Name="ItemsHost"/>
					</Grid>
					<ControlTemplate.Triggers>
						<Trigger Property="IsExpanded" Value="false">
							<Setter Property="Visibility" Value="Collapsed" TargetName="ItemsHost"/>
						</Trigger>
						<Trigger Property="HasItems" Value="false">
							<Setter Property="Visibility" Value="Hidden" TargetName="Expander"/>
						</Trigger>
						<Trigger Property="IsSelected" Value="true">
							<Setter Property="Background" Value="{DynamicResource {x:Static SystemColors.HighlightBrushKey}}" TargetName="Selection_Border"/>
							<Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.HighlightTextBrushKey}}"/>
						</Trigger>
						<MultiTrigger>
							<MultiTrigger.Conditions>
								<Condition Property="IsSelected" Value="true"/>
								<Condition Property="IsSelectionActive" Value="false"/>
							</MultiTrigger.Conditions>
							<Setter Property="Background" Value="{DynamicResource {x:Static SystemColors.ControlBrushKey}}" TargetName="Selection_Border"/>
							<Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"/>
						</MultiTrigger>
						<Trigger Property="IsEnabled" Value="false">
							<Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.GrayTextBrushKey}}"/>
						</Trigger>
					</ControlTemplate.Triggers>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
</ResourceDictionary>
