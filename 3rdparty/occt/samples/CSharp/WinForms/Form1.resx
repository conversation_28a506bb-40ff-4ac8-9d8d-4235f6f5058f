<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 1.3
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">1.3</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1">this is my long string</data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        [base64 mime encoded serialized .NET Framework object]
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        [base64 mime encoded string representing a byte array form of the .NET Framework object]
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used forserialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>1.3</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="mainMenu1.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="mainMenu1.Location" type="System.Drawing.Point, System.Drawing, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </data>
  <data name="mainMenu1.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="File.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="File.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="FileNew.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="FileNew.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="FileExport.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="FileExport.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ImportBRep.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ImportBRep.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ImportIges.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ImportIges.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ImportStep.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ImportStep.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="FileImport.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="FileImport.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ExportBRep.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ExportBRep.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ExportIges.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ExportIges.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ExportStep.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ExportStep.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ExportStl.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ExportStl.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ExportVrml.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ExportVrml.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="menuItem3.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="menuItem3.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ExportImage.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ExportImage.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="FileClose.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="FileClose.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="menuItem2.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="menuItem2.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="menuItem1.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="menuItem1.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="View.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="View.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ViewToolbar.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ViewToolbar.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ViewStatusBar.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ViewStatusBar.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="Window.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="Window.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="menuItem4.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="menuItem4.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="WindowCascade.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="WindowCascade.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="WindowTile.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="WindowTile.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="Help.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="Help.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="HelpAbout.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="HelpAbout.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="toolBarTool.Locked" type="System.Boolean, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </data>
  <data name="toolBarTool.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="toolBarTool.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="New.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="New.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="About.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="About.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="wireframe.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="wireframe.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="shading.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="shading.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="color.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="color.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="material.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="material.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="transparency.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="transparency.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="delete.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="delete.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="imageList1.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="imageList1.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="imageList1.Location" type="System.Drawing.Point, System.Drawing, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>141, 18</value>
  </data>
  <data name="imageList1.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFpTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj0xLjAuNTAw
        MC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZT
        eXN0ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMA
        AAB6GAAAAk1TRnQBSQFMAgEBGAEAAR0BAAEEAQABEAEAARABAAT/AQkBAAj/AUIBTQE2AQQGAAE2AQQC
        AAEoAwABQAMAAYADAAEBAQABCAYAASAYAAGAAgABgAMAAoABAAGAAwABgAEAAYABAAKAAgADwAEAAcAB
        3AHAAQAB8AHKAaYBAAEzBQABMwEAATMBAAEzAQACMwIAAxYBAAMcAQADIgEAAykBAANVAQADTQEAA0IB
        AAM5AQABgAF8Af8BAAJQAf8BAAGTAQAB1gEAAf8B7AHMAQABxgHWAe8BAAHWAucBAAGQAakBrQIAAf8B
        MwMAAWYDAAGZAwABzAIAATMDAAIzAgABMwFmAgABMwGZAgABMwHMAgABMwH/AgABZgMAAWYBMwIAAmYC
        AAFmAZkCAAFmAcwCAAFmAf8CAAGZAwABmQEzAgABmQFmAgACmQIAAZkBzAIAAZkB/wIAAcwDAAHMATMC
        AAHMAWYCAAHMAZkCAALMAgABzAH/AgAB/wFmAgAB/wGZAgAB/wHMAQABMwH/AgAB/wEAATMBAAEzAQAB
        ZgEAATMBAAGZAQABMwEAAcwBAAEzAQAB/wEAAf8BMwIAAzMBAAIzAWYBAAIzAZkBAAIzAcwBAAIzAf8B
        AAEzAWYCAAEzAWYBMwEAATMCZgEAATMBZgGZAQABMwFmAcwBAAEzAWYB/wEAATMBmQIAATMBmQEzAQAB
        MwGZAWYBAAEzApkBAAEzAZkBzAEAATMBmQH/AQABMwHMAgABMwHMATMBAAEzAcwBZgEAATMBzAGZAQAB
        MwLMAQABMwHMAf8BAAEzAf8BMwEAATMB/wFmAQABMwH/AZkBAAEzAf8BzAEAATMC/wEAAWYDAAFmAQAB
        MwEAAWYBAAFmAQABZgEAAZkBAAFmAQABzAEAAWYBAAH/AQABZgEzAgABZgIzAQABZgEzAWYBAAFmATMB
        mQEAAWYBMwHMAQABZgEzAf8BAAJmAgACZgEzAQADZgEAAmYBmQEAAmYBzAEAAWYBmQIAAWYBmQEzAQAB
        ZgGZAWYBAAFmApkBAAFmAZkBzAEAAWYBmQH/AQABZgHMAgABZgHMATMBAAFmAcwBmQEAAWYCzAEAAWYB
        zAH/AQABZgH/AgABZgH/ATMBAAFmAf8BmQEAAWYB/wHMAQABzAEAAf8BAAH/AQABzAEAApkCAAGZATMB
        mQEAAZkBAAGZAQABmQEAAcwBAAGZAwABmQIzAQABmQEAAWYBAAGZATMBzAEAAZkBAAH/AQABmQFmAgAB
        mQFmATMBAAGZATMBZgEAAZkBZgGZAQABmQFmAcwBAAGZATMB/wEAApkBMwEAApkBZgEAA5kBAAKZAcwB
        AAKZAf8BAAGZAcwCAAGZAcwBMwEAAWYBzAFmAQABmQHMAZkBAAGZAswBAAGZAcwB/wEAAZkB/wIAAZkB
        /wEzAQABmQHMAWYBAAGZAf8BmQEAAZkB/wHMAQABmQL/AQABzAMAAZkBAAEzAQABzAEAAWYBAAHMAQAB
        mQEAAcwBAAHMAQABmQEzAgABzAIzAQABzAEzAWYBAAHMATMBmQEAAcwBMwHMAQABzAEzAf8BAAHMAWYC
        AAHMAWYBMwEAAZkCZgEAAcwBZgGZAQABzAFmAcwBAAGZAWYB/wEAAcwBmQIAAcwBmQEzAQABzAGZAWYB
        AAHMApkBAAHMAZkBzAEAAcwBmQH/AQACzAIAAswBMwEAAswBZgEAAswBmQEAA8wBAALMAf8BAAHMAf8C
        AAHMAf8BMwEAAZkB/wFmAQABzAH/AZkBAAHMAf8BzAEAAcwC/wEAAcwBAAEzAQAB/wEAAWYBAAH/AQAB
        mQEAAcwBMwIAAf8CMwEAAf8BMwFmAQAB/wEzAZkBAAH/ATMBzAEAAf8BMwH/AQAB/wFmAgAB/wFmATMB
        AAHMAmYBAAH/AWYBmQEAAf8BZgHMAQABzAFmAf8BAAH/AZkCAAH/AZkBMwEAAf8BmQFmAQAB/wKZAQAB
        /wGZAcwBAAH/AZkB/wEAAf8BzAIAAf8BzAEzAQAB/wHMAWYBAAH/AcwBmQEAAf8CzAEAAf8BzAH/AQAC
        /wEzAQABzAH/AWYBAAL/AZkBAAL/AcwBAAJmAf8BAAFmAf8BZgEAAWYC/wEAAf8CZgEAAf8BZgH/AQAC
        /wFmAQABIQEAAaUBAANfAQADdwEAA4YBAAOWAQADywEAA7IBAAPXAQAD3QEAA+MBAAPqAQAD8QEAA/gB
        AAHwAfsB/wEAAaQCoAEAA4ADAAH/AgAB/wMAAv8BAAH/AwAB/wEAAf8BAAL/AgAD//8A/wD/AP8A/wD/
        AP8A/wAPAAH7PgAD+wHsPQAB+wIAAewIAAP7NAAC7AYAAvsBAAL7DQAB7CMABOwGAAL7AQAC+w4AAewb
        AAL7AwAC7AIAAewBAAHsBQAC+wEAAvsPAAXsAQAC7BUAA/sB7AQAAuwFAAL7AQAC+w8AAeweAAHsAvsD
        AAPsBQAD+xAAAeweAAHsAwAB+wEAAewBAAHsGAAB7B4AAewEAAHsAgAB7BgAAewfAAHsAwAB7AMAA/sw
        AAP7AgAC7AEAAewBAALsAwAB+xUAAewdAAPsAgAC7BsAAewcAAHsAQAD7DwAAuw/AAHsQgAB+ywAA/sQ
        AAH7CwABAQnsAwAJ7AEBCwAC+w4AAfsBAAH7AQAB+wkAAgEHAALsAgAB7AH+BwACAQgABPsKAAoBCAAB
        AQEAAQEGAAHsAQAB7AEAAewBAAH+BgABAQEAAQEFAAL7DgAB7AEBAwAD+wEAAgEHAAEBAgABAQX+AewC
        /gLsAgAG/gEAAv4BAQQAAfsPAAHsAQABAQMAAfsCAAHsAQABAQUAAfsBAQIAAQEFAAHsAgAC7AIAAf4F
        AAEBAgABAQQAAfsCAAH+DAAB7AIACgEFAAEBAfsBAAEBBQAB7AIAAuwCAAH+BQABAQIAAfsDAAP7AQAB
        +wH+CwAB7AIAAf4FAAHsAgAB7AIAA/sBAQL7AQEFAAHsAgAC7AIAAf4FAAEBAQAB+wEBBAAB+wIAAvsF
        /gEAAv4DAAHsAgAB/gUAAewCAAHsBQABAQH7AQABAQUAAewCAALsAgAB/gUAAQEG+wYAAf4KAAHsAgAB
        /gUAAewCAAHsBAAB+wEBAgABAQUAAewCAALsAgAB/gUAAQEBAAH7AQEJAAH+CgAB7AIAAf4FAAHsAgAB
        7AUAAQEC7AEBBuwCAArsAQECAAH7CQAB/goAAewCAAH+BQAB7AIAAewGAAEBAQABAQYAAewBAAHsAQAB
        7AEAAf4GAAEBAQABAQkAAf4KAArsAgAB7AcAAgEHAALsAgAB7AH+BwACARUAAewBAAH+BgAB7AEAAewI
        AAEBCewDAAnsAQEJAAH+DAAB7AH+BwAC7CgAAf4NAArsOQAB+xkACuwGAArsCwAD+wgAAfsJAQYAAewB
        /gcAAuwFAAHsAf4HAALsCQAB+wEAAfsBAAH7BwABAQH7BwABAQHsBQAB7AEAAf4GAAHsAQAB7AQAAewB
        AAH+BgAB7AEAAewKAAH7CQABAQEAAfsGAAEBAQAB7AQAAewCAAoBAwAB7AIABv4B7AL+AewHAALsAfsB
        AAPsBQABAQIAAfsBAAH7A/4BAQL+AewDAAHsAgABAQUAAewCAAEBAwAB7AIAAf4FAAHsAgAB7AQAAfsB
        AAHsAgAB+wMAAewB+wQAAQEDAAL7AwABAQIAAewDAAHsAgABAQUAAewCAAEBAwAB7AIAAf4FAAHsAgAB
        7AMAAfsCAAHsBgAB7AEAAfsDAAEBAgAD+wMAAQECAAHsAwAB7AIAAQEFAAHsAgABAQMAAewCAAH+BQAB
        7AIAAewCAAb7BAAF+wIAAQECAAH+BQABAQIAAewDAAHsAgABAQUAAewCAAEBAwAB7AIAAf4FAAHsAgAB
        7AMAAfsCAAHsBgAB7AEAAfsDAAEBAgAB/gUAAQECAAHsAwAB7AIAAQEBAAT7AewCAAEBAwAB7AIAAf4F
        AAHsAgAB7AQAAfsBAAHsAgAB+wMAAewB+wQAAQECAAH+BQABAQIAAewDAAPsAQEB7AL7A+wCAAEBAwAK
        AQIAAewGAAPsAfsBAAPsBQAKAQIAAewEAAHsAQABAQEAAfsBAAH7AgAB7AEAAQEEAAEBAQAB/gYAAQEB
        AAHsCQAB+woAAewBAAH+BgAB7AEAAewFAAHsAQEBAAH7AgAB+wIAAewBAQUAAQEB/gIAAfsEAAEBAewH
        AAH7AQAB+wEAAfsJAAHsAf4HAALsBgAKAQYAAgED+wUBCAAD+wsACuwNAAH7CQAB+wEAAfsBAAH7DQAB
        +yQAAfsKAAH7NQAB+wkAAfsIAAP7CgAD+w4AAfshAAL7DAAC+wIAAewDAAfsA/sB7BYAAfsIAAH7AewB
        AwoAAfsBAAH7DgAB+xcAA/sJAAEDCQAB+xEAAewWAAH7AQAB+wEAAfskAAHsDwAB+wgAAfsPAAXsBQAB
        7AYABOwCAAHsBwAF7AIAA/sHAAH7DQAB7AYAAewEAAHsBAAB7AUAAewBAAHsBQAB7AYAAewCAAH7AwAB
        +wkAAfsIAAHsBwAB7AMAAewEAAHsBgAC7AUAAewHAAHsBAAB+wsAAfsGAALsBwAC7AIAAewDAALsBgAC
        7AQAAuwHAAEDAewCAAX7BQAF+wUAAewJAAHsAgAB7AMAAewIAAHsBAAB7AQAA/sCAAHsAwAB+wsAAfsG
        AAHsCQAB7AIAAewDAAHsCAAB7AQAAewCAAL7BQAB7AQAAfsEAAH7BAAB+wcAAuwHAALsAgAB7AMAAuwG
        AALsBQAB7AcAAuwJAAH7CwAB+wEAAewHAAHsAwAB7AQAAewGAALsBQAB7AcAAewKAAH7CAAB+wEAAfsD
        AAHsBQAB7AH7AQAB+wEAAfsFAAHsBAAB7AEAAewBAAP7AgAB7AUAAewJAAH7AQAB+wEAAfsGAAL7BQAF
        7AIABfsE7AEABuwIAAXsCwAD+wcAA/sKAAP7AQAB+yUAAfsfAAb4CAAG+A0AAgcC7AUADQoIAAH4BvsB
        +AYAAfgGoAH4CAACCwLvAQcD7AYAAx4BAANrAQACNwFtCAABrgFYBvsBWAGuBAABrgGZBqABmQGuBQAC
        CwKSAU8CBwHvAuwGAAMBAQADBgEAAvsB7AgAAVgBcwb7AXMBWAQAAewBHAagARwB7AUAAW0B7QEHAU8B
        AgJPAQcC7AFtAQsEAAMBAQADBgEAAvsB7AcAAT8B+wFYBnMBRgH7BAAB7AGZBhwBmQHsBQAB7AEHAU8B
        7QECAe0BAgEHA+wFAAMLAQADDQEAAikBQwcAAfkBLAH7BkYB+QH7BAAB7AGgBpkBoAHsBAABbwHvAQcB
        AgEHAXIBBwFyAQcD7AUAAwIBAAMEAQADBgMABGIC+QEsATIG+QH7BAAB7AigAewEAAHsAQcBcgECAXIB
        BwJyAQcD7AUAA/oBAAP8AQAD/gMAB/kB+wb5AfsEAAHsCKAB7AQAAewBBwFyAQIBcgEHAgIBBwPsARQB
        AQMAA/oBAAP8AQAD/gMABGIC+QEsATIG+QH7BAAB7AigAewEAAHsAgcDcgE1AXIBeQGSA+wEAAMtAQAD
        hQEAA4EHAAH5ASwB+wZGAfkB+wQAAewBoAaZAaAB7AMAAUYBkgMHAk8BWAEHAVgBBwGSAuwEAAMeAQAD
        KQEAA2EHAAE/AfsBWAZzAUYB+wQAAewBmQYcAZkB7AMAAewCBwLvAeoC7AQHAewB7QQAA/kBAAP7AQAD
        /QgAAVgBcwb7AXMBWAQAAewBHAagARwB7AMAAewCBwLsAQAB7AHtAgcCmQHsAe8BCwMAA/kBAAP7AQAD
        /QgAAa4BWAb7AVgBrgQAAa4BmQagAZkBrgMAAewB7wEHAuwBAAHsAgcBmQJYAm4EAAMgAQADNwEAA6UJ
        AAH4BvsB+AYAAfgGoAH4BAACbgIHAewBDgMHAfsCbhsABvgIAAb4BwAC7AEHBew6AATsLQAG+woABuwp
        AAFYBqcBWAgAAewG+wHsGwAC+woAAVgBhgYAAYYBWAYAAa4BWAb7AVgBrgcACf8KAAL7CgAB+wFYBgAB
        WAH7BgAB7Aj7AewHAAn/FgAB+wGuBlEBrgH7BgAB7Aj7AewHAAn/CgAB+wsAAfsBAAauAQAB+wYAAewI
        +wHsBwAJ/woAAfsLAAH7CAAB+wYAAewI+wHsBwAJ/woAAfsLAAH7CAAB+wYAAewI+wHsBwAJ/woAAvsK
        AAH7CAAB+wYAAewI+wHsBwAJ/wsAAvsJAAH7AQAGrgEAAfsGAAHsAfsGWAH7AewHAAn/BwAB+wQAAvsI
        AAH7Aa4GUQGuAfsGAAHsAVgGcwFYAewHAAb/CgAB+wUAAfsIAAH7AVgGAAFYAfsGAAHsAXMG+wFzAewH
        AAb/AQAB/wgAAvsDAAL7CAABWAGGBgABhgFYBgABrgFYBvsBWAGuBwAG/wsABfsKAAFYBqcBWAgAAewG
        +wHsKQAG+woABuxFAAFCAU0BPgcAAT4DAAEoAwABQAMAAYADAAEBAQABAQYAAQQWAAP//wACAAH9B/8B
        +AF/Bv8B/QG/AccB/wHAAR8BwAEfAf8BnwGTAf8CzwHfAc8B/gEfAZMB/wLXAd8B1wE5AY8BkwH/AdgB
        AwHfAdsBwwHPAZMB/wLbAd8B2wHxAscB+wLbAd8B2wH3AVcB/wHBAtsB3wHbAfcBtwH/AZsC2wHfAdsB
        +wGoAf8BrwHAARsBwAEbARkBTgH/AbUC6wHvAesBwgE/Af8BuQLzAfcB8wHBAv8BEQH4AQMB+AEDAecC
        /wG/BP8B9wf/Af4F/wHxAf8B/gH/AeABBwEAAT8B+QH/AfoBvwHnAfMBPwGfAeEB/wGAAR8B6wH1AV8B
        rwGfAf8BnAFPAewBAAFgAQcBoAEPAa4B1wHNAfYBbwG3AacB5wGwAQMB5QH2AW8BtwEDAesBtwHbAQEB
        9gFvAacBoAEBAbcB2wHlAfYBbwGAAu0BtwHbAc0B9gFvAacC7QG3AdsB4AEGAQABNwLtAbcB2wH1AfoB
        rwHXAu0BgAEbAfkB/AHPAecB4AENAdcB6wH8AQAB4AEHAvUB5wHzBP8C+QHwAQME/wH8AQEB/gP/AYAB
        HwGAAR8B/AF/AYABHwGfAc8BnwHPAfoBvwGfAc8BrwHXAa8B1wH+Af8BrwHXAbABAwGwAQMB8AGPAbQB
        AwG3AdsBtwHbAdYB5wG5AdsBtwHbAbcB2wG3AesBsQHbAbcB2wG3AdsBAwHBAbcB2wG3AdsBtwHbAbcB
        6wG3AdsBtAEbAbcB2wHWAecBtwHbAYABGwGAARsB8AGPAYABGwHVAWsB1wHrAf4B/wHXAesB5QGzAeYB
        8wH6Ab8B5wHzAfABAwHwAQMB/AF/AfABAwH/Ae8B+gG/Af4E/wH3Af4G/wH7Af4B/wEfAfgB/wH9BP8B
        PwH8AdwBAAF/Af8B/gH/AR8B+gHPAf0BPwH/AfwBfwHPAfcB5wH9AZ8B/wH6Ab8B5wH/AfMB/QHPAf0B
        /gH/AfIBDwG5AQ0B5AEYAf4B/wH5AfcBvAH1AfMB7QHfAfcC+wG9AfkC9wG/AfsB8wH5AbkB+QHnAfMB
        BwHBAfcB/QG7Af0B7wEbAb8B+wH3Af0BuwH9AewB+wHeAfcB8wH5AbkB+QH3AfMB/gH/AesB+wG9AfkC
        9wH+Af8BXQHyAb4B9QEbAe8B+gG/AT4BDAIBAfwBHwH8AX8BHwH4Ab8D/wH+A/8B/gEHAfgBHwH8AQcB
        gAEDAfwBAwHwAQ8B8AEHAYABAwH4AQEB4AEHAcABBwGAAQMB+AEBAeABBwHAAQMBgAEDAfABAQHgAQcB
        wAEDAYABAwHwAQEB4AEHAYABAwGAAQMBAAEBAeABBwGAAQMBgAEDAQABAQHgAQcBgAEBAYABAwEAAQEB
        4AEHAYABAQGAAQMB8AEBAeABBwEAAQEBgAEDAfABAQHgAQcBAAEBAYABAwH4AQEB4AEHAQABAQGAAQMB
        +AEBAeABBwEAAQMBgAEDAfwBAwHwAQ8BAAEPAYABAwH+AQcB+AEfAcABPwb/AfAF/wH4AR8B+AEfAv8B
        /gE/AfABDwHwAQ8B4AEDAfwBHwLnAeABBwHgAQMB/AEfAucB4AEHAeABAwH+AX8B4AEHAeABBwHgAQMB
        /AE/AegBFwHgAQcB4AEDAfwBPwHvAfcB4AEHAeABAwH8AT8B7wH3AeABBwHgAQMB/AEfAe8B9wHgAQcB
        4AEDAfIBDwHoARcB4AEHAeABAwHhAQcB4AEHAeABBwHgAQMB4QGHAucB4AEHAeABBwHgAQcC5wHgAQcB
        4AEPAfABDwHwAQ8B8AEPAeABHwH4AR8B+AEfAfgBHwj/Cw==
</value>
  </data>
  <data name="myStatusBar.Locked" type="System.Boolean, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </data>
  <data name="myStatusBar.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="myStatusBar.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="toolBarView.Locked" type="System.Boolean, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </data>
  <data name="toolBarView.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="toolBarView.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ZoomAll.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ZoomAll.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ZoomWin.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ZoomWin.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ZoomProg.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ZoomProg.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="Pan.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="Pan.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="PanGlo.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="PanGlo.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="Front.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="Front.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="Back.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="Back.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="TOP.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="TOP.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="BOTTOM.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="BOTTOM.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="LEFT.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="LEFT.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="RIGHT.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="RIGHT.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="Axo.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="Axo.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="Rot.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="Rot.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="Reset.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="Reset.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="HlrOn.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="HlrOn.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="HlrOff.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="HlrOff.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="toolBarButton1.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="toolBarButton1.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="$this.Locked" type="System.Boolean, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </data>
  <data name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>(Default)</value>
  </data>
  <data name="$this.TrayLargeIcon" type="System.Boolean, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </data>
  <data name="$this.Localizable" type="System.Boolean, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </data>
  <data name="$this.GridSize" type="System.Drawing.Size, System.Drawing, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>8, 8</value>
  </data>
  <data name="$this.DrawGrid" type="System.Boolean, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </data>
  <data name="$this.Name">
    <value>Form1</value>
  </data>
  <data name="$this.TrayHeight" type="System.Int32, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>80</value>
  </data>
  <data name="$this.SnapToGrid" type="System.Boolean, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </data>
  <data name="$this.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAMDAAAAAAAACoDgAAFgAAACgAAAAwAAAAYAAAAAEACAAAAAAAgAoAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAACAAACAAAAAgIAAgAAAAIAAgACAgAAAwMDAAMDcwADwyqYABAQEAAgICAAMDAwAERERABYW
        FgAcHBwAIiIiACkpKQBVVVUATU1NAEJCQgA5OTkAgHz/AFBQ/wCTANYA/+zMAMbW7wDW5+cAkKmtAAAA
        MwAAAGYAAACZAAAAzAAAMwAAADMzAAAzZgAAM5kAADPMAAAz/wAAZgAAAGYzAABmZgAAZpkAAGbMAABm
        /wAAmQAAAJkzAACZZgAAmZkAAJnMAACZ/wAAzAAAAMwzAADMZgAAzJkAAMzMAADM/wAA/2YAAP+ZAAD/
        zAAzAAAAMwAzADMAZgAzAJkAMwDMADMA/wAzMwAAMzMzADMzZgAzM5kAMzPMADMz/wAzZgAAM2YzADNm
        ZgAzZpkAM2bMADNm/wAzmQAAM5kzADOZZgAzmZkAM5nMADOZ/wAzzAAAM8wzADPMZgAzzJkAM8zMADPM
        /wAz/zMAM/9mADP/mQAz/8wAM///AGYAAABmADMAZgBmAGYAmQBmAMwAZgD/AGYzAABmMzMAZjNmAGYz
        mQBmM8wAZjP/AGZmAABmZjMAZmZmAGZmmQBmZswAZpkAAGaZMwBmmWYAZpmZAGaZzABmmf8AZswAAGbM
        MwBmzJkAZszMAGbM/wBm/wAAZv8zAGb/mQBm/8wAzAD/AP8AzACZmQAAmTOZAJkAmQCZAMwAmQAAAJkz
        MwCZAGYAmTPMAJkA/wCZZgAAmWYzAJkzZgCZZpkAmWbMAJkz/wCZmTMAmZlmAJmZmQCZmcwAmZn/AJnM
        AACZzDMAZsxmAJnMmQCZzMwAmcz/AJn/AACZ/zMAmcxmAJn/mQCZ/8wAmf//AMwAAACZADMAzABmAMwA
        mQDMAMwAmTMAAMwzMwDMM2YAzDOZAMwzzADMM/8AzGYAAMxmMwCZZmYAzGaZAMxmzACZZv8AzJkAAMyZ
        MwDMmWYAzJmZAMyZzADMmf8AzMwAAMzMMwDMzGYAzMyZAMzMzADMzP8AzP8AAMz/MwCZ/2YAzP+ZAMz/
        zADM//8AzAAzAP8AZgD/AJkAzDMAAP8zMwD/M2YA/zOZAP8zzAD/M/8A/2YAAP9mMwDMZmYA/2aZAP9m
        zADMZv8A/5kAAP+ZMwD/mWYA/5mZAP+ZzAD/mf8A/8wAAP/MMwD/zGYA/8yZAP/MzAD/zP8A//8zAMz/
        ZgD//5kA///MAGZm/wBm/2YAZv//AP9mZgD/Zv8A//9mACEApQBfX18Ad3d3AIaGhgCWlpYAy8vLALKy
        sgDX19cA3d3dAOPj4wDq6uoA8fHxAPj4+ADw+/8ApKCgAICAgAAAAP8AAP8AAAD//wD/AAAA/wD/AP//
        AAD///8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABMT
        ExMTExMTEyIiQiI8HTyCAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALwDODg4Nzc4MQMdAAAA
        AADDAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOw4ODgyOCQAAAAdAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAODIyMSIAHewAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAA7DI3MSIdCwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAADEyMQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAs4JAALAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALwxJAAhAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4IgC8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAJAxJB0LAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        ALwLCx0dIh0dCwATAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACx0dAB0dIiIiIiId
        CxMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAvAsdAB0dIiQkAyQkIiIiHSIAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAALAAAAIgMxMVhZmZqZAyQiHR0AAAAAkAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAvAsdISIDMThZWVmgvaCZWVIkIh0AAB0dIh0hCwALCwALtgAAAAAAAAAAAAAAAOwAAB0iMTEx
        ODhYWZrDw8Ofn1hSJCIAHQAdACIAHSIkAwsdOAAAAADeExMTCwsLIiIkMTEyODg3MllZmprDw8PDwllS
        MSQdHQAAAB0AIiQkAAAAvDi8AAAxAzExMTExMTI4Nzg4ODc4ODhZXpnDw8PDml1YNwMkIh0AAB0hIiQA
        AAAAACIAAAA4ODc4Nzg4ODg4ODg4ODg4ODhZWV6avb2gmllZODcxJCIAAAAdIgMLAAAAACIAAACGE0+G
        T4bs7Oy8vLy8vAAxODg4ODg4NzIyMSQdHQAAvADsCwAiJDExAAAAwjjsAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAALMSQAAAAAAAAAAAAAAAAAEyI4MQsiOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALMSQAAAAA
        AAAAAAAAAAAAAAC8CwsAEwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC8AAsAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA////////AAD///////8AAP///////wAA////////
        AAD///////8AAP///////wAA////////AAD///////8AAP///////wAA////////AAD///////8AAP//
        /////wAA//8AAH//AAD//wAAf/8AAP//4AP//wAA///4B///AAD///gP//8AAP///B///wAA///8H///
        AAD///wf//8AAP///h///wAA///8H///AAD//+AD//8AAP//wAD//wAA//4AAD//AAD//AAAB/8AAP/w
        AAAAAwAA/8AAAAABAADAAAAAADgAAMAAAAAAPAAAwAAAAAA8AADAAAAAIDgAAP///j/+AQAA///+P/+D
        AAD///4///8AAP///////wAA////////AAD///////8AAP///////wAA////////AAD///////8AAP//
        /////wAA////////AAD///////8AAP///////wAA////////AAD///////8AAP///////wAA
</value>
  </data>
</root>