<?xml version="1.0"?>
<configuration>
  <appSettings>
    <!--   User application and configured property settings go here.-->
    <!--   Example: <add key="settingName" value="settingValue"/> -->
    <add key="New.Visible" value="True"/>
    <add key="About.Visible" value="True"/>
    <add key="delete.Enabled" value="False"/>
    <add key="delete.Visible" value="False"/>
    <add key="transparency.Enabled" value="False"/>
    <add key="transparency.Visible" value="False"/>
    <add key="marerial.Enabled" value="False"/>
    <add key="marerial.Visible" value="True"/>
    <add key="color.Enabled" value="False"/>
    <add key="color.Visible" value="False"/>
    <add key="shading.Enabled" value="False"/>
    <add key="shading.Pushed" value="False"/>
    <add key="shading.Visible" value="False"/>
    <add key="wireframe.Enabled" value="False"/>
    <add key="wireframe.Pushed" value="False"/>
    <add key="wireframe.Visible" value="False"/>
    <add key="material.Enabled" value="False"/>
    <add key="material.Visible" value="False"/>
    <add key="ClientSettingsProvider.ServiceUri" value=""/>
  </appSettings>
  <system.web>
    <membership defaultProvider="ClientAuthenticationMembershipProvider">
      <providers>
        <add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri=""/>
      </providers>
    </membership>
    <roleManager defaultProvider="ClientRoleProvider" enabled="true">
      <providers>
        <add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400"/>
      </providers>
    </roleManager>
  </system.web>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8"/></startup></configuration>
