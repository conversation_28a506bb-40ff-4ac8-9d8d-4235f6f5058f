<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 1.3
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">1.3</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1">this is my long string</data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        [base64 mime encoded serialized .NET Framework object]
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        [base64 mime encoded string representing a byte array form of the .NET Framework object]
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used forserialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>1.3</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="imageList1.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="imageList1.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="imageList1.Location" type="System.Drawing.Point, System.Drawing, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </data>
  <data name="imageList1.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFpTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj0xLjAuNTAw
        MC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZT
        eXN0ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMA
        AADoEgAAAk1TRnQBSQFMAgEBEAEAARMBAAEEAQABEAEAARABAAT/AQkBEAj/AUIBTQE2AQQGAAE2AQQC
        AAEoAwABQAMAAVADAAEBAQABCAYAARQYAAGAAgABgAMAAoABAAGAAwABgAEAAYABAAKAAgADwAEAAcAB
        3AHAAQAB8AHKAaYBAAEzBQABMwEAATMBAAEzAQACMwIAAxYBAAMcAQADIgEAAykBAANVAQADTQEAA0IB
        AAM5AQABgAF8Af8BAAJQAf8BAAGTAQAB1gEAAf8B7AHMAQABxgHWAe8BAAHWAucBAAGQAakBrQIAAf8B
        MwMAAWYDAAGZAwABzAIAATMDAAIzAgABMwFmAgABMwGZAgABMwHMAgABMwH/AgABZgMAAWYBMwIAAmYC
        AAFmAZkCAAFmAcwCAAFmAf8CAAGZAwABmQEzAgABmQFmAgACmQIAAZkBzAIAAZkB/wIAAcwDAAHMATMC
        AAHMAWYCAAHMAZkCAALMAgABzAH/AgAB/wFmAgAB/wGZAgAB/wHMAQABMwH/AgAB/wEAATMBAAEzAQAB
        ZgEAATMBAAGZAQABMwEAAcwBAAEzAQAB/wEAAf8BMwIAAzMBAAIzAWYBAAIzAZkBAAIzAcwBAAIzAf8B
        AAEzAWYCAAEzAWYBMwEAATMCZgEAATMBZgGZAQABMwFmAcwBAAEzAWYB/wEAATMBmQIAATMBmQEzAQAB
        MwGZAWYBAAEzApkBAAEzAZkBzAEAATMBmQH/AQABMwHMAgABMwHMATMBAAEzAcwBZgEAATMBzAGZAQAB
        MwLMAQABMwHMAf8BAAEzAf8BMwEAATMB/wFmAQABMwH/AZkBAAEzAf8BzAEAATMC/wEAAWYDAAFmAQAB
        MwEAAWYBAAFmAQABZgEAAZkBAAFmAQABzAEAAWYBAAH/AQABZgEzAgABZgIzAQABZgEzAWYBAAFmATMB
        mQEAAWYBMwHMAQABZgEzAf8BAAJmAgACZgEzAQADZgEAAmYBmQEAAmYBzAEAAWYBmQIAAWYBmQEzAQAB
        ZgGZAWYBAAFmApkBAAFmAZkBzAEAAWYBmQH/AQABZgHMAgABZgHMATMBAAFmAcwBmQEAAWYCzAEAAWYB
        zAH/AQABZgH/AgABZgH/ATMBAAFmAf8BmQEAAWYB/wHMAQABzAEAAf8BAAH/AQABzAEAApkCAAGZATMB
        mQEAAZkBAAGZAQABmQEAAcwBAAGZAwABmQIzAQABmQEAAWYBAAGZATMBzAEAAZkBAAH/AQABmQFmAgAB
        mQFmATMBAAGZATMBZgEAAZkBZgGZAQABmQFmAcwBAAGZATMB/wEAApkBMwEAApkBZgEAA5kBAAKZAcwB
        AAKZAf8BAAGZAcwCAAGZAcwBMwEAAWYBzAFmAQABmQHMAZkBAAGZAswBAAGZAcwB/wEAAZkB/wIAAZkB
        /wEzAQABmQHMAWYBAAGZAf8BmQEAAZkB/wHMAQABmQL/AQABzAMAAZkBAAEzAQABzAEAAWYBAAHMAQAB
        mQEAAcwBAAHMAQABmQEzAgABzAIzAQABzAEzAWYBAAHMATMBmQEAAcwBMwHMAQABzAEzAf8BAAHMAWYC
        AAHMAWYBMwEAAZkCZgEAAcwBZgGZAQABzAFmAcwBAAGZAWYB/wEAAcwBmQIAAcwBmQEzAQABzAGZAWYB
        AAHMApkBAAHMAZkBzAEAAcwBmQH/AQACzAIAAswBMwEAAswBZgEAAswBmQEAA8wBAALMAf8BAAHMAf8C
        AAHMAf8BMwEAAZkB/wFmAQABzAH/AZkBAAHMAf8BzAEAAcwC/wEAAcwBAAEzAQAB/wEAAWYBAAH/AQAB
        mQEAAcwBMwIAAf8CMwEAAf8BMwFmAQAB/wEzAZkBAAH/ATMBzAEAAf8BMwH/AQAB/wFmAgAB/wFmATMB
        AAHMAmYBAAH/AWYBmQEAAf8BZgHMAQABzAFmAf8BAAH/AZkCAAH/AZkBMwEAAf8BmQFmAQAB/wKZAQAB
        /wGZAcwBAAH/AZkB/wEAAf8BzAIAAf8BzAEzAQAB/wHMAWYBAAH/AcwBmQEAAf8CzAEAAf8BzAH/AQAC
        /wEzAQABzAH/AWYBAAL/AZkBAAL/AcwBAAJmAf8BAAFmAf8BZgEAAWYC/wEAAf8CZgEAAf8BZgH/AQAC
        /wFmAQABIQEAAaUBAANfAQADdwEAA4YBAAOWAQADywEAA7IBAAPXAQAD3QEAA+MBAAPqAQAD8QEAA/gB
        AAHwAfsB/wEAAaQCoAEAA4ADAAH/AgAB/wMAAv8BAAH/AwAB/wEAAf8BAAL/AgAD//8A/wD/AP8ACwAB
        +z4AA/sB7D0AAfsCAAHsCAAD+zQAAuwGAAL7AQAC+w0AAewjAATsBgAC+wEAAvsOAAHsGwAC+wMAAuwC
        AAHsAQAB7AUAAvsBAAL7DwAF7AEAAuwVAAP7AewEAALsBQAC+wEAAvsPAAHsHgAB7AL7AwAD7AUAA/sQ
        AAHsHgAB7AMAAfsBAAHsAQAB7BgAAeweAAHsBAAB7AIAAewYAAHsHwAB7AMAAewDAAP7MAAD+wIAAuwB
        AAHsAQAC7AMAAfsVAAHsHQAD7AIAAuwbAAHsHAAB7AEAA+w8AALsPwAB7EIAAfs/AAH7CwABAQnsAwAJ
        7AEBCgAC7AGGDgAB+wEAAfsBAAH7CQACAQcAAuwCAAHsAf4HAAIBCQABhgH7AewCAAEKAQQGAAoBCAAB
        AQEAAQEGAAHsAQAB7AEAAewBAAH+BgABAQEAAQEGAAHsA/sB7AEKAQABhQEAAYUFAAHsAQEDAAP7AQAC
        AQcAAQECAAEBBf4B7AL+AuwCAAb+AQAC/gEBBAABhgFYAa4BhQEEAQoBhQEAAYUCXwGFBAAB7AEAAQED
        AAH7AgAB7AEAAQEFAAH7AQECAAEBBQAB7AIAAuwCAAH+BQABAQIAAQEEAAGGAewBrgEAAYoDAAGFAV8B
        AAEGAQQDAAHsAgAKAQUAAQEB+wEAAQEFAAHsAgAC7AIAAf4FAAEBAgAB+wQAAYYB7AIAAX0BrAIAAYUB
        awHZAccBCgMAAewCAAH+BQAB7AIAAewCAAP7AQEC+wEBBQAB7AIAAuwCAAH+BQABAQEAAfsBAQQAAvsB
        hgEAAfsBngHHAQABuAFfAgABCgMAAewCAAH+BQAB7AIAAewFAAEBAfsBAAEBBQAB7AIAAuwCAAH+BQAB
        AQb7AQABhgGuBAABsgG4AQoBXwGhAQABCgMAAewCAAH+BQAB7AIAAewEAAH7AQECAAEBBQAB7AIAAuwC
        AAH+BQABAQEAAfsBAQgAAaECXwEGAQABhQFfAQABCgMAAewCAAH+BQAB7AIAAewFAAEBAuwBAQbsAgAK
        7AEBAgAB+wgAAQoBhQGhAf4CAAGFAgoDAAHsAgAB/gUAAewCAAHsBgABAQEAAQEGAAHsAQAB7AEAAewB
        AAH+BgABAQEAAQEIAAEKAgAB/gQAAQoDAArsAgAB7AcAAgEHAALsAgAB7AH+BwACAQgAAYUBXwGhAf4B
        hQFfAQoGAAHsAQAB/gYAAewBAAHsCAABAQnsAwAJ7AEBCQABhQFfAQYBXwGFCAAB7AH+BwAC7CkAAoUL
        AArsOQAB+xkACuwGAArsCwAD+wgAAfsJAQYAAewB/gcAAuwFAAHsAf4HAALsCQAB+wEAAfsBAAH7BwAB
        AQH7BwABAQHsBQAB7AEAAf4GAAHsAQAB7AQAAewBAAH+BgAB7AEAAewKAAH7CQABAQEAAfsGAAEBAQAB
        7AQAAewCAAoBAwAB7AIABv4B7AL+AewHAALsAfsBAAPsBQABAQIAAfsBAAH7A/4BAQL+AewDAAHsAgAB
        AQUAAewCAAEBAwAB7AIAAf4FAAHsAgAB7AQAAfsBAAHsAgAB+wMAAewB+wQAAQEDAAL7AwABAQIAAewD
        AAHsAgABAQUAAewCAAEBAwAB7AIAAf4FAAHsAgAB7AMAAfsCAAHsBgAB7AEAAfsDAAEBAgAD+wMAAQEC
        AAHsAwAB7AIAAQEFAAHsAgABAQMAAewCAAH+BQAB7AIAAewCAAb7BAAF+wIAAQECAAH+BQABAQIAAewD
        AAHsAgABAQUAAewCAAEBAwAB7AIAAf4FAAHsAgAB7AMAAfsCAAHsBgAB7AEAAfsDAAEBAgAB/gUAAQEC
        AAHsAwAB7AIAAQEBAAT7AewCAAEBAwAB7AIAAf4FAAHsAgAB7AQAAfsBAAHsAgAB+wMAAewB+wQAAQEC
        AAH+BQABAQIAAewDAAPsAQEB7AL7A+wCAAEBAwAKAQIAAewGAAPsAfsBAAPsBQAKAQIAAewEAAHsAQAB
        AQEAAfsBAAH7AgAB7AEAAQEEAAEBAQAB/gYAAQEBAAHsCQAB+woAAewBAAH+BgAB7AEAAewFAAHsAQEB
        AAH7AgAB+wIAAewBAQUAAQEB/gIAAfsEAAEBAewHAAH7AQAB+wEAAfsJAAHsAf4HAALsBgAKAQYAAgED
        +wUBCAAD+wsACuwNAAH7CQAB+wEAAfsBAAH7DQAB+yQAAfsKAAH7NQAB+wkAAfsIAAP7CgAD+w4AAfsh
        AAL7DAAC+wIAAewDAAfsA/sB7BYAAfsIAAH7AewBAwoAAfsBAAH7DgAB+xcAA/sJAAEDCQAB+xEAAewW
        AAH7AQAB+wEAAfskAAHsDwAB+wgAAfsPAAXsBQAB7AYABOwCAAHsBwAF7AIAA/sHAAH7DQAB7AYAAewE
        AAHsBAAB7AUAAewBAAHsBQAB7AYAAewCAAH7AwAB+wkAAfsIAAHsBwAB7AMAAewEAAHsBgAC7AUAAewH
        AAHsBAAB+wsAAfsGAALsBwAC7AIAAewDAALsBgAC7AQAAuwHAAEDAewCAAX7BQAF+wUAAewJAAHsAgAB
        7AMAAewIAAHsBAAB7AQAA/sCAAHsAwAB+wsAAfsGAAHsCQAB7AIAAewDAAHsCAAB7AQAAewCAAL7BQAB
        7AQAAfsEAAH7BAAB+wcAAuwHAALsAgAB7AMAAuwGAALsBQAB7AcAAuwJAAH7CwAB+wEAAewHAAHsAwAB
        7AQAAewGAALsBQAB7AcAAewKAAH7CAAB+wEAAfsDAAHsBQAB7AH7AQAB+wEAAfsFAAHsBAAB7AEAAewB
        AAP7AgAB7AUAAewJAAH7AQAB+wEAAfsGAAL7BQAF7AIABfsE7AEABuwIAAXsCwAD+wcAA/sKAAP7AQAB
        +yUAAfsIAAFCAU0BPgcAAT4DAAEoAwABQAMAAVADAAEBAQABAQUAAYABAhYAA/+BAAH9B/8B+AF/Bv8B
        /QG/AccB/wHAAR8BwAEfAf8BnwGTAf8CzwHfAc8B/gEfAZMB/wLXAd8B1wE5AY8BkwH/AdgBAwHfAdsB
        wwHPAZMB/wLbAd8B2wHxAscB+wLbAd8B2wH3AVcB/wHBAtsB3wHbAfcBtwH/AZsC2wHfAdsB+wGoAf8B
        rwHAARsBwAEbARkBTgH/AbUC6wHvAesBwgE/Af8BuQLzAfcB8wHBAv8BEQH4AQMB+AEDAecC/wG/BP8B
        9wf/Af4H/wH+Af8B4AEHAQABPwHxAf8B+gG/AecB8wE/AZ8B8QGfAYABHwHrAfUBXwGvAcABDwGcAU8B
        7AEAAWABBwGAAYcBrgHXAc0B9gFvAbcBgwGTAbABAwHlAfYBbwG3AZEBgwG3AdsBAQH2AW8BpwGAAZsB
        twHbAeUB9gFvAYABlgELAbcB2wHNAfYBbwGnAfABSwG3AdsB4AEGAQABNwHwAWMBtwHbAfUB+gGvAdcB
        +wFzAYABGwH5AfwBzwHnAfgBDwHXAesB/AEAAeABBwH8AR8B5wHzBP8B/gF/AfABAwb/Af4D/wGAAR8B
        gAEfAfwBfwGAAR8BnwHPAZ8BzwH6Ab8BnwHPAa8B1wGvAdcB/gH/Aa8B1wGwAQMBsAEDAfABjwG0AQMB
        twHbAbcB2wHWAecBuQHbAbcB2wG3AdsBtwHrAbEB2wG3AdsBtwHbAQMBwQG3AdsBtwHbAbcB2wG3AesB
        twHbAbQBGwG3AdsB1gHnAbcB2wGAARsBgAEbAfABjwGAARsB1QFrAdcB6wH+Af8B1wHrAeUBswHmAfMB
        +gG/AecB8wHwAQMB8AEDAfwBfwHwAQMB/wHvAfoBvwH+BP8B9wH+Bv8B+wH+Af8BHwH4Af8B/QT/AT8B
        /AHcAQABfwH/Af4B/wEfAfoBzwH9AT8B/wH8AX8BzwH3AecB/QGfAf8B+gG/AecB/wHzAf0BzwH9Af4B
        /wHyAQ8BuQENAeQBGAH+Af8B+QH3AbwB9QHzAe0B3wH3AvsBvQH5AvcBvwH7AfMB+QG5AfkB5wHzAQcB
        wQH3Af0BuwH9Ae8BGwG/AfsB9wH9AbsB/QHsAfsB3gH3AfMB+QG5AfkB9wHzAf4B/wHrAfsBvQH5AvcB
        /gH/AV0B8gG+AfUBGwHvAfoBvwE+AQwCAQH8AR8B/AF/AR8B+AG/A/8B/gH/FgAL
</value>
  </data>
  <data name="myPopup.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="myPopup.Location" type="System.Drawing.Point, System.Drawing, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>122, 17</value>
  </data>
  <data name="myPopup.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="menuItem1.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="menuItem1.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="myPopupObject.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="myPopupObject.Location" type="System.Drawing.Point, System.Drawing, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>220, 17</value>
  </data>
  <data name="myPopupObject.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContextWireframe.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContextWireframe.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContextShading.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContextShading.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContextColor.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContextColor.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContextMaterial.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContextMaterial.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContMatBrass.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContMatBrass.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContMenBronze.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContMenBronze.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContMenCopper.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContMenCopper.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContMenGold.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContMenGold.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContMenPewt.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContMenPewt.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContMenPlaster.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContMenPlaster.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContMenPlastic.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContMenPlastic.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContMenSilver.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContMenSilver.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContMenTranc.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContMenTranc.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContMenDelete.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="ContMenDelete.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="$this.Locked" type="System.Boolean, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </data>
  <data name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>(Default)</value>
  </data>
  <data name="$this.TrayLargeIcon" type="System.Boolean, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </data>
  <data name="$this.Name">
    <value>Form2</value>
  </data>
  <data name="$this.Localizable" type="System.Boolean, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </data>
  <data name="$this.GridSize" type="System.Drawing.Size, System.Drawing, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>8, 8</value>
  </data>
  <data name="$this.DrawGrid" type="System.Boolean, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </data>
  <data name="$this.TrayHeight" type="System.Int32, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>80</value>
  </data>
  <data name="$this.SnapToGrid" type="System.Boolean, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </data>
  <data name="$this.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAIAICAQAAAAAADoAgAAJgAAABAQEAAAAAAAKAEAAA4DAAAoAAAAIAAAAEAAAAABAAQAAAAAAIAC
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAgAAAAICAAIAAAACAAIAAgIAAAICAgADAwMAAAAD/AAD/
        AAAA//8A/wAAAP8A/wD//wAA////AAiIiIAAiIgAAAAAAAAAAAAAAACIgAAIiAAAAAAAAAAAD///AA//
        8AiIAAAAAAAAAA////////8ACIgAAAAAAAAP//////////AIgAAAAAAAD///////////AIgAAAAAAA//
        //////////AIAAAAAAAP////////////CIAAAAAAD///+IiIiIiIiACIiIiAAA/////////////wAAAA
        iAAP////////////////8AiAD/////////////////8AgA//////////////////8IAP////////////
        //////CID//////////////////wCA////iIiIiIiIiIiIiIiAgP//////////////////8ID///////
        ////////////CA///////////////////wgP//////////////////8ID///////////////////CA//
        /////////////////wgP///4iIiIiIiIiIiIiIgID///////////////////CA////mZ//mf+Zn/mZ//
        /wgP///5/5+f+fn///n///8ID///+f+fn/n5///5////CA////n/n5/5+f//+f///wgP///5mf/5n/mZ
        /5n///8ID///////////////////CA///////////////////wgAAAAAAAAAAAAAAAAAAAAAgcP//wAA
        //8AAD//AAAP/wAAB/8AAAP/AAAD/wAAAf8AAAAHAAAAAwAAAAEAAAABAAAAAQAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAEoAAAAEAAAACAAAAABAAQAAAAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAgAAAAICAAIAA
        AACAAIAAgIAAAMDAwACAgIAAAAD/AAD/AAAA//8A/wAAAP8A/wD//wAA////AAd3B3AAAAAAAAdwAHcA
        AAAP8A//AHAAAA//////AAZgD/d3d3cHd2AP/////wAHcA////////AAD/d3d3d3dwcP////////Bw//
        //////8HD5n/+f/5nwcPn5+fn5//Bw+fn5+fn/8HD5n/+f/5nwcP////////BwAAAAAAAAAAif8AAAA/
        AAAAHwAAABkAAAABAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAA==
</value>
  </data>
</root>