# This workflow validates the build on the latest Ubuntu version (24.04) using multiple configurations.
# It is triggered on pushes to the master branch.
# The workflow includes steps to install dependencies, configure, build, and clean up the project for different configurations.

name: Ubuntu build validation

on:
  push:
    branches:
      - 'master'

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number }}
  cancel-in-progress: true

jobs:
  main_job:
    name: Latest ubuntu validation
    runs-on: ubuntu-24.04

    strategy:
      matrix:
        config:
          - {
              name: "G<PERSON>",
              cc: "gcc",
              cxx: "g++",
              compiler_flags: ""
            }
          - {
              name: "Clang",
              cc: "clang",
              cxx: "clang++",
              compiler_flags: "-D CMAKE_CXX_FLAGS=\"-Werror -Wall -Wextra\" -D CMAKE_C_FLAGS=\"-Werror -Wall -Wextra\""
            }
        build_type: [Debug, Release]

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Install dependencies
      run: sudo apt-get update && sudo apt-get install -y ninja-build tcl-dev tk-dev cmake clang gcc g++ make libbtbb-dev libx11-dev libglu1-mesa-dev tcllib tcl-thread tcl libvtk9-dev libopenvr-dev libdraco-dev libfreeimage-dev libegl1-mesa-dev libgles2-mesa-dev libfreetype-dev libjemalloc-dev

    - name: Install rapidjson
      run: |
        wget https://github.com/Tencent/rapidjson/archive/858451e5b7d1c56cf8f6d58f88cf958351837e53.zip -O rapidjson.zip
        unzip rapidjson.zip

    - name: Configure basic
      run: |
        mkdir -p build
        cd build
        cmake -G "Ninja" \
              -D CMAKE_C_COMPILER=${{ matrix.config.cc }} \
              -D CMAKE_CXX_COMPILER=${{ matrix.config.cxx }} \
              -D CMAKE_BUILD_TYPE=${{ matrix.build_type }} \
              ${{ matrix.config.compiler_flags }} ..

    - name: Build basic
      run: |
        cd build
        cmake --build . --config ${{ matrix.build_type }} -- -j 4

    - name: Clear up after build
      run: |
        rm -rf build

    - name: Configure full shared
      run: |
        mkdir -p build
        cd build
        cmake -G "Ninja" \
              -D CMAKE_C_COMPILER=${{ matrix.config.cc }} \
              -D CMAKE_CXX_COMPILER=${{ matrix.config.cxx }} \
              -D BUILD_USE_PCH=OFF \
              -D BUILD_INCLUDE_SYMLINK=ON \
              -D BUILD_OPT_PROFILE=Production \
              -D BUILD_LIBRARY_TYPE=Shared \
              -D USE_TK=ON \
              -D CMAKE_BUILD_TYPE=${{ matrix.build_type }} \
              -D USE_MMGR_TYPE=JEMALLOC \
              -D INSTALL_DIR=${{ github.workspace }}/install-${{ matrix.build_type }} \
              -D 3RDPARTY_RAPIDJSON_DIR=${{ github.workspace }}/rapidjson-858451e5b7d1c56cf8f6d58f88cf958351837e53 \
              -D USE_FREETYPE=ON \
              -D USE_DRACO=ON \
              -D USE_FFMPEG=OFF \
              -D USE_FREEIMAGE=ON \
              -D USE_GLES2=ON \
              -D USE_OPENVR=ON \
              -D USE_VTK=ON \
              -D USE_TBB=ON \
              -D USE_RAPIDJSON=ON \
              -D USE_OPENGL=ON \
              ${{ matrix.config.compiler_flags }} ..

    - name: Build full shared
      run: |
        cd build
        cmake --build . --target install --config ${{ matrix.build_type }} -- -j 4
    
    - name: Clear up after build
      run: |
        rm -rf build
        rm -rf ${{ github.workspace }}/install-${{ matrix.build_type }}

    - name: Configure full static
      run: |
        mkdir -p build
        cd build
        cmake -G "Ninja" \
              -D CMAKE_C_COMPILER=${{ matrix.config.cc }} \
              -D CMAKE_CXX_COMPILER=${{ matrix.config.cxx }} \
              -D BUILD_USE_PCH=OFF \
              -D BUILD_INCLUDE_SYMLINK=ON \
              -D BUILD_OPT_PROFILE=Default \
              -D BUILD_LIBRARY_TYPE=Static \
              -D USE_TK=ON \
              -D CMAKE_BUILD_TYPE=${{ matrix.build_type }} \
              -D USE_MMGR_TYPE=JEMALLOC \
              -D INSTALL_DIR=${{ github.workspace }}/install-${{ matrix.build_type }} \
              -D 3RDPARTY_RAPIDJSON_DIR=${{ github.workspace }}/rapidjson-858451e5b7d1c56cf8f6d58f88cf958351837e53 \
              -D USE_FREETYPE=ON \
              -D USE_DRACO=ON \
              -D USE_FFMPEG=OFF \
              -D USE_FREEIMAGE=ON \
              -D USE_GLES2=ON \
              -D USE_OPENVR=ON \
              -D USE_VTK=OFF \
              -D USE_TBB=ON \
              -D USE_RAPIDJSON=ON \
              -D USE_OPENGL=ON \
              ${{ matrix.config.compiler_flags }} ..

    - name: Build full static
      run: |
        cd build
        cmake --build . --target install --config ${{ matrix.build_type }} -- -j 4

    - name: Clear up after build
      run: |
        rm -rf build
        rm -rf ${{ github.workspace }}/install-${{ matrix.build_type }}

    - name: Configure full with DEBUG define
      run: |
        mkdir -p build
        cd build
        cmake -G "Ninja" \
              -D CMAKE_C_COMPILER=${{ matrix.config.cc }} \
              -D CMAKE_CXX_COMPILER=${{ matrix.config.cxx }} \
              -D BUILD_WITH_DEBUG=ON \
              -D BUILD_USE_PCH=OFF \
              -D BUILD_INCLUDE_SYMLINK=ON \
              -D BUILD_OPT_PROFILE=Production \
              -D BUILD_LIBRARY_TYPE=Shared \
              -D USE_TK=ON \
              -D CMAKE_BUILD_TYPE=${{ matrix.build_type }} \
              -D INSTALL_DIR=${{ github.workspace }}/install-${{ matrix.build_type }} \
              -D 3RDPARTY_RAPIDJSON_DIR=${{ github.workspace }}/rapidjson-858451e5b7d1c56cf8f6d58f88cf958351837e53 \
              -D USE_FREETYPE=ON \
              -D USE_DRACO=ON \
              -D USE_FFMPEG=OFF \
              -D USE_FREEIMAGE=ON \
              -D USE_GLES2=ON \
              -D USE_OPENVR=ON \
              -D USE_VTK=ON \
              -D USE_TBB=ON \
              -D USE_RAPIDJSON=ON \
              -D USE_OPENGL=ON ..

    - name: Build full with DEBUG define
      run: |
        cd build
        cmake --build . --target install --config ${{ matrix.build_type }} -- -j 4
      
    - name: Clear up after build
      run: |
        rm -rf build
        rm -rf ${{ github.workspace }}/install-${{ matrix.build_type }}
