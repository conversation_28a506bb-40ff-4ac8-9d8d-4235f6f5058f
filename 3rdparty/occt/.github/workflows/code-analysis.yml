# This workflow performs code analysis using both CodeQL and Microsoft C++ Code Analysis.
# It is triggered on pushes to the 'master' branch and publishes warnings into the security GitHub tab.
# The workflow includes two jobs: one for CodeQL analysis on Ubuntu and another for MSVC Code Analysis on Windows.

name: Code Analysis

on:
  push:
    branches:
      - 'master'

permissions:
  contents: read
  security-events: write
  packages: read

env:
  # Path to the CMake build directory.
  build: '${{ github.workspace }}/build'
  config: 'Debug'

jobs:
  codeql-analyze:
    name: CodeQL Analyze (C/C++)
    runs-on: ubuntu-latest

    steps:
    # Step: Checkout the repository
    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    # Step: Install necessary dependencies for building the project
    - name: Install dependencies
      run: sudo apt-get update && sudo apt-get install -y tcl-dev tk-dev cmake gcc g++ make libbtbb-dev libx11-dev libglu1-mesa-dev

    # Step: Initialize CodeQL for scanning
    - name: Initialize CodeQL
      uses: github/codeql-action/init@v3.26.5
      with:
        languages: c-cpp
        build-mode: manual

    # Step: Build the project using CMake and Make
    - name: Build project
      shell: bash
      run: |
        mkdir -p build
        cd build
        cmake -G "Unix Makefiles" \
              -D CMAKE_C_COMPILER=gcc \
              -D CMAKE_CXX_COMPILER=g++ \
              -D USE_FREETYPE=OFF \
              -D CMAKE_BUILD_TYPE=Release ..
        make -j$(nproc)

    # Step: Perform CodeQL Analysis
    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v3.26.5
      with:
        category: "/language:c-cpp"

  msvc-analyze:
    name: Microsoft C++ Code Analysis
    runs-on: windows-latest

    steps:
    # Step: Checkout the repository
    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    # Step: Install necessary dependencies using Chocolatey
    - name: Install dependencies
      run: |
          choco install cmake --installargs 'ADD_CMAKE_TO_PATH=System' -y
          choco install magicsplat-tcl-tk -y

    # Step: Configure the project using CMake
    - name: Configure CMake
      run: |
        mkdir build
        cd build
        cmake -D USE_FREETYPE=OFF -DCMAKE_BUILD_TYPE=${{ env.config }} ..

    # Step: Run MSVC Code Analysis
    - name: Run MSVC Code Analysis
      uses: microsoft/msvc-code-analysis-action@v0.1.1
      id: run-analysis
      with:
        cmakeBuildDirectory: ${{ env.build }}
        buildConfiguration: ${{ env.config }}
        ruleset: NativeRecommendedRules.ruleset

    # Step: Upload SARIF file to GitHub Code Scanning Alerts
    - name: Upload SARIF to GitHub
      uses: github/codeql-action/upload-sarif@v3.26.5
      with:
        sarif_file: ${{ steps.run-analysis.outputs.sarif }}
