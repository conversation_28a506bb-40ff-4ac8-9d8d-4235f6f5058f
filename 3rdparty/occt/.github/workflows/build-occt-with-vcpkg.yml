# This workflow builds OCCT using vcpkg on multiple platforms (Windows, macOS, Linux).
# It builds in both Debug and Release modes.
# All dependencies except the compiler are installed using vcpkg.
# The workflow includes steps to clone vcpkg, install dependencies, configure and build.

name: Build OCCT with vcpkg

on:
  push:
    branches:
      - 'master'

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number }}
  cancel-in-progress: true

env:
  USERNAME: Open-Cascade-SAS
  VCPKG_EXE: ${{ github.workspace }}/vcpkg/vcpkg
  FEED_URL: https://nuget.pkg.github.com/Open-Cascade-SAS/index.json
  VCPKG_BINARY_SOURCES: "clear;nuget,https://nuget.pkg.github.com/Open-Cascade-SAS/index.json,readwrite"

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-24.04, ubuntu-22.04, windows-2022, windows-2019, macos-15, macos-14, macos-13]
        build_type: [Debug, Release]

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Install required packages (Linux)
      if: runner.os == 'Linux'
      run: |
        sudo apt-get update
        sudo apt-get install -y build-essential ninja-build curl zip unzip tar nasm autoconf mono-complete
        sudo apt-get install -y libxi-dev libgl1-mesa-dev libglu1-mesa-dev mesa-common-dev libxrandr-dev libxxf86vm-dev

    - name: Install required packages (macOS)
      if: runner.os == 'macOS'
      run: |
        brew update || true
        brew install cmake ninja nasm autoconf mono || true
        # temporary workaround for missing tcl-tk
        brew install tcl-tk || true
        # Force link any conflicting packages
        brew link --overwrite python@3.12 || true
        brew link --overwrite python@3.13 || true

    - name: Install required packages (Windows)
      if: runner.os == 'Windows'
      uses: ilammy/msvc-dev-cmd@v1.13.0
      with:
        arch: x64

    - name: Set up vcpkg (Unix)
      if: runner.os != 'Windows'
      run: |
        git clone https://github.com/microsoft/vcpkg.git
        ./vcpkg/bootstrap-vcpkg.sh
      shell: bash

    - name: Set up vcpkg (Windows)
      if: runner.os == 'Windows'
      run: |
        git clone https://github.com/microsoft/vcpkg.git
        .\vcpkg\bootstrap-vcpkg.bat
      shell: cmd

    - name: Add NuGet sources
      if: runner.os == 'Windows'
      run: |
        .$(${{ env.VCPKG_EXE }} fetch nuget) `
          sources add `
          -Source "${{ env.FEED_URL }}" `
          -StorePasswordInClearText `
          -Name GitHubPackages `
          -UserName "${{ env.USERNAME }}" `
          -Password "${{ secrets.GITHUB_TOKEN }}"
        .$(${{ env.VCPKG_EXE }} fetch nuget) `
          setapikey "${{ secrets.GITHUB_TOKEN }}" `
          -Source "${{ env.FEED_URL }}"
      shell: pwsh

    - name: Add NuGet sources
      if: runner.os != 'Windows'
      run: |
        mono `${{ env.VCPKG_EXE }} fetch nuget | tail -n 1` \
          sources add \
          -Source "${{ env.FEED_URL }}" \
          -StorePasswordInClearText \
          -Name GitHubPackages \
          -UserName "${{ env.USERNAME }}" \
          -Password "${{ secrets.GITHUB_TOKEN }}"
        mono `${{ env.VCPKG_EXE }} fetch nuget | tail -n 1` \
          setapikey "${{ secrets.GITHUB_TOKEN }}" \
          -Source "${{ env.FEED_URL }}"
      shell: bash

    - name: Configure OCCT ${{ matrix.build_type }} (Unix)
      if: runner.os != 'Windows'
      run: |
        mkdir build-${{ matrix.build_type }}
        cd build-${{ matrix.build_type }}
        cmake -G Ninja \
              -DCMAKE_TOOLCHAIN_FILE=${{ github.workspace }}/vcpkg/scripts/buildsystems/vcpkg.cmake \
              -DCMAKE_BUILD_TYPE=${{ matrix.build_type }} \
              -DBUILD_USE_VCPKG=ON \
              -DUSE_MMGR_TYPE=NATIVE \
              -DUSE_FREETYPE=ON \
              -DUSE_TK=OFF \
              -DBUILD_USE_PCH=ON \
              -DBUILD_INCLUDE_SYMLINK=ON \
              -DBUILD_LIBRARY_TYPE="Static" \
              -DINSTALL_DIR=${{ github.workspace }}/install-${{ matrix.build_type }} \
              -DUSE_DRACO=ON \
              -DUSE_FFMPEG=ON \
              -DUSE_FREEIMAGE=ON \
              -DUSE_GLES2=OFF \
              -DUSE_VTK=ON \
              -DUSE_TBB=ON \
              -DUSE_RAPIDJSON=ON \
              -DUSE_OPENGL=ON \
              -DBUILD_MODULE_Draw=OFF \
              -DVCPKG_INSTALL_OPTIONS=--clean-buildtrees-after-build \
              ${{ runner.os != 'macOS' && '-DUSE_OPENVR=ON' || '' }} ..
      shell: bash

    - name: Configure OCCT ${{ matrix.build_type }} (Windows)
      if: runner.os == 'Windows'
      run: |
        mkdir build-${{ matrix.build_type }}
        cd build-${{ matrix.build_type }}
        cmake -DCMAKE_TOOLCHAIN_FILE=${{ github.workspace }}/vcpkg/scripts/buildsystems/vcpkg.cmake ^
              -DCMAKE_BUILD_TYPE=${{ matrix.build_type }} ^
              -DBUILD_USE_VCPKG=ON ^
              -DUSE_MMGR_TYPE=JEMALLOC ^
              -DUSE_FREETYPE=ON ^
              -DUSE_TK=OFF ^
              -DBUILD_USE_PCH=ON ^
              -DBUILD_INCLUDE_SYMLINK=ON ^
              -DINSTALL_DIR=${{ github.workspace }}/install-${{ matrix.build_type }} ^
              -DUSE_DRACO=ON ^
              -DUSE_FFMPEG=OFF ^
              -DUSE_FREEIMAGE=ON ^
              -DUSE_GLES2=ON ^
              -DUSE_OPENVR=ON ^
              -DUSE_VTK=ON ^
              -DUSE_TBB=ON ^
              -DUSE_RAPIDJSON=ON ^
              -DVCPKG_INSTALL_OPTIONS=--clean-buildtrees-after-build ^
              -DUSE_OPENGL=ON ..
      shell: cmd

    - name: Build OCCT ${{ matrix.build_type }} (Unix)
      if: runner.os != 'Windows'
      run: |
        cd build-${{ matrix.build_type }}
        cmake --build . --target install --config ${{ matrix.build_type }}
      shell: bash

    - name: Build OCCT ${{ matrix.build_type }} (Windows)
      if: runner.os == 'Windows'
      run: |
        cd build-${{ matrix.build_type }}
        cmake --build . --target install --config ${{ matrix.build_type }}
      shell: cmd
