# This workflow builds and tests OCCT on multiple platforms (Windows, macOS, Linux with Clang, and Linux with GCC).
# It is triggered on pull requests to any branch.
# The workflow includes steps to prepare and build the project on each platform, run tests, and upload the results.
# Concurrency is set to ensure that only one instance of the workflow runs per pull request at a time.

name: Build and Test OCCT on Multiple Platforms

on:
  pull_request:
    branches:
      - '**'
  push:
    branches:
      - 'master'

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number }}
  cancel-in-progress: true

jobs:
  clang-format:
    name: Check code formatting
    runs-on: windows-2022
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4.1.7
      with:
        fetch-depth: 0
    
    - name: Run clang-format check
      uses: ./.github/actions/clang-format-check
      with:
        base-ref: ${{ github.event.pull_request.base.ref || 'master' }}

  documentation:
    name: Build Documentation
    runs-on: windows-2022

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4.2.1

    - name: Build documentation
      uses: ./.github/actions/build-docs

  prepare-and-build-windows-x64:
    name: Prepare and Build on Windows with MSVC (x64)
    runs-on: windows-2022

    steps:

    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Download and extract 3rdparty dependencies
      run: |
          Invoke-WebRequest -Uri https://github.com/Open-Cascade-SAS/OCCT/releases/download/V7_9_0_beta1/3rdparty-vc14-64.zip -OutFile 3rdparty-vc14-64.zip
          Expand-Archive -Path 3rdparty-vc14-64.zip -DestinationPath .
          Remove-Item 3rdparty-vc14-64.zip
      shell: pwsh

    - name: Download and extract Mesa3D
      run: |
        curl -L -o mesa3d.7z https://github.com/pal1000/mesa-dist-win/releases/download/24.3.2/mesa3d-24.3.2-release-mingw.7z
        7z x mesa3d.7z -omesa3d

    - name: Run system-wide deployment
      run: |
        cd mesa3d
        .\systemwidedeploy.cmd 1
        .\systemwidedeploy.cmd 5
      shell: cmd

    - name: Configure OCCT
      run: |
          mkdir build
          cd build
          cmake -T host=x64 `
                -D USE_FREETYPE=ON `
                -D USE_TK=OFF `
                -D BUILD_USE_PCH=ON `
                -D BUILD_OPT_PROFILE=Production `
                -D BUILD_INCLUDE_SYMLINK=ON `
                -D CMAKE_BUILD_TYPE=Release `
                -D 3RDPARTY_DIR=${{ github.workspace }}/3rdparty-vc14-64 `
                -D INSTALL_DIR=${{ github.workspace }}/install `
                -D USE_D3D=ON `
                -D USE_DRACO=ON `
                -D USE_FFMPEG=ON `
                -D USE_FREEIMAGE=ON `
                -D USE_GLES2=ON `
                -D USE_OPENVR=ON `
                -D USE_VTK=ON `
                -D USE_TBB=ON `
                -D USE_RAPIDJSON=ON `
                -D USE_OPENGL=ON `
                -D CMAKE_CXX_FLAGS="/W4 /WX" `
                -D CMAKE_C_FLAGS="/W4 /WX" ..
      shell: pwsh

    - name: Build OCCT
      run: |
          cd build
          cmake --build . --target install --config Release

    - name: Upload install directory
      uses: actions/upload-artifact@v4.4.3
      with:
        name: install-windows-x64
        path: install
        retention-days: 7

  prepare-and-build-windows-clang-x64:
    name: Prepare and Build on Windows with Clang (x64)
    runs-on: windows-2022

    steps:

    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Download and extract 3rdparty dependencies
      run: |
          Invoke-WebRequest -Uri https://github.com/Open-Cascade-SAS/OCCT/releases/download/V7_9_0_beta1/3rdparty-vc14-64.zip -OutFile 3rdparty-vc14-64.zip
          Expand-Archive -Path 3rdparty-vc14-64.zip -DestinationPath .
          Remove-Item 3rdparty-vc14-64.zip
      shell: pwsh

    - name: Download and extract Mesa3D
      run: |
        curl -L -o mesa3d.7z https://github.com/pal1000/mesa-dist-win/releases/download/24.3.2/mesa3d-24.3.2-release-mingw.7z
        7z x mesa3d.7z -omesa3d

    - name: Run system-wide deployment
      run: |
        cd mesa3d
        .\systemwidedeploy.cmd 1
        .\systemwidedeploy.cmd 5
      shell: cmd

    - name: Install Ninja
      run: |
        choco install ninja -y
        ninja --version
      shell: pwsh

    - name: Configure OCCT
      run: |
          mkdir build
          cd build
          cmake -G "Ninja" `
                -D CMAKE_C_COMPILER=clang `
                -D CMAKE_CXX_COMPILER=clang++ `
                -D USE_FREETYPE=ON `
                -D USE_TK=OFF `
                -D BUILD_USE_PCH=ON `
                -D BUILD_OPT_PROFILE=Production `
                -D BUILD_INCLUDE_SYMLINK=ON `
                -D CMAKE_BUILD_TYPE=Release `
                -D 3RDPARTY_DIR=${{ github.workspace }}/3rdparty-vc14-64 `
                -D INSTALL_DIR=${{ github.workspace }}/install `
                -D USE_D3D=ON `
                -D USE_DRACO=ON `
                -D USE_FFMPEG=ON `
                -D USE_FREEIMAGE=ON `
                -D USE_GLES2=ON `
                -D USE_OPENVR=ON `
                -D USE_VTK=OFF `
                -D USE_TBB=ON `
                -D USE_RAPIDJSON=ON `
                -D USE_OPENGL=ON `
                -D CMAKE_CXX_FLAGS="-Werror -Wall -Wextra -Wno-unknown-warning-option" `
                -D CMAKE_C_FLAGS="-Werror -Wall -Wextra -Wno-unknown-warning-option" ..
      shell: pwsh

    - name: Build OCCT
      run: |
          cd build
          cmake --build . --target install --config Release

    - name: Upload install directory
      uses: actions/upload-artifact@v4.4.3
      with:
        name: install-windows-clang-x64
        path: install
        retention-days: 7

  prepare-and-build-macos-x64:
    name: Prepare and Build on macOS with Clang (x64)
    runs-on: macos-15

    steps:

    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Install dependencies
      run: |
        brew update
        brew install tcl-tk tbb gl2ps xerces-c \
                     libxmu libxi libxft libxpm \
                     glew freeimage draco glfw

    - name: Install rapidjson
      run: |
        wget https://github.com/Tencent/rapidjson/archive/858451e5b7d1c56cf8f6d58f88cf958351837e53.zip -O rapidjson.zip
        unzip rapidjson.zip

    - name: Configure OCCT
      run: |
        mkdir -p build
        cd build
        cmake -G "Unix Makefiles" \
              -D CMAKE_C_COMPILER=clang \
              -D CMAKE_CXX_COMPILER=clang++ \
              -D BUILD_USE_PCH=ON \
              -D BUILD_INCLUDE_SYMLINK=ON \
              -D CMAKE_BUILD_TYPE=Release \
              -D INSTALL_DIR=${{ github.workspace }}/install \
              -D 3RDPARTY_RAPIDJSON_DIR=${{ github.workspace }}/rapidjson-858451e5b7d1c56cf8f6d58f88cf958351837e53 \
              -D USE_RAPIDJSON=ON \
              -D USE_DRACO=ON \
              -D USE_FREETYPE=ON \
              -D USE_OPENGL=ON \
              -D USE_FREEIMAGE=ON \
              -D CMAKE_CXX_FLAGS="-Werror -Wall -Wextra" \
              -D CMAKE_C_FLAGS="-Werror -Wall -Wextra" ..

    - name: Build OCCT
      run: |
        cd build
        make install -j$(sysctl -n hw.logicalcpu)

    - name: Upload install directory
      uses: actions/upload-artifact@v4.4.3
      with:
        name: install-macos-x64
        path: install
        retention-days: 7

  prepare-and-build-macos-gcc-x64:
    name: Prepare and Build on macOS with GCC (x64)
    runs-on: macos-15

    steps:

    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Install dependencies
      run: |
        brew update
        brew install tcl-tk tbb gl2ps xerces-c \
                     libxmu libxi libxft libxpm \
                     glew freeimage draco glfw

    - name: Install rapidjson
      run: |
        wget https://github.com/Tencent/rapidjson/archive/858451e5b7d1c56cf8f6d58f88cf958351837e53.zip -O rapidjson.zip
        unzip rapidjson.zip

    - name: Configure OCCT
      run: |
        mkdir -p build
        cd build
        cmake -G "Unix Makefiles" \
              -D CMAKE_C_COMPILER=gcc \
              -D CMAKE_CXX_COMPILER=g++ \
              -D BUILD_USE_PCH=ON \
              -D BUILD_INCLUDE_SYMLINK=ON \
              -D CMAKE_BUILD_TYPE=Release \
              -D INSTALL_DIR=${{ github.workspace }}/install \
              -D 3RDPARTY_RAPIDJSON_DIR=${{ github.workspace }}/rapidjson-858451e5b7d1c56cf8f6d58f88cf958351837e53 \
              -D USE_RAPIDJSON=ON \
              -D USE_DRACO=ON \
              -D USE_FREETYPE=ON \
              -D USE_OPENGL=ON \
              -D USE_FREEIMAGE=ON \
              -D CMAKE_CXX_FLAGS="-Werror -Wall -Wextra" \
              -D CMAKE_C_FLAGS="-Werror -Wall -Wextra" ..

    - name: Build OCCT
      run: |
        cd build
        make install -j$(sysctl -n hw.logicalcpu)

    - name: Upload install directory
      uses: actions/upload-artifact@v4.4.3
      with:
        name: install-macos-gcc-x64
        path: install
        retention-days: 7

  prepare-and-build-linux-clang-x64:
    name: Prepare and Build on Ubuntu with Clang (x64)
    runs-on: ubuntu-24.04

    steps:

    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Install dependencies
      run: sudo apt-get update && sudo apt-get install -y tcl-dev tk-dev cmake clang make libbtbb-dev libx11-dev libglu1-mesa-dev tcllib tcl-thread tcl libvtk9-dev libopenvr-dev libdraco-dev libfreeimage-dev libegl1-mesa-dev libgles2-mesa-dev libfreetype-dev

    - name: Install rapidjson
      run: |
        wget https://github.com/Tencent/rapidjson/archive/858451e5b7d1c56cf8f6d58f88cf958351837e53.zip -O rapidjson.zip
        unzip rapidjson.zip

    - name: Configure OCCT
      run: |
        mkdir -p build
        cd build
        cmake -G "Unix Makefiles" \
              -D CMAKE_C_COMPILER=clang \
              -D CMAKE_CXX_COMPILER=clang++ \
              -D BUILD_USE_PCH=ON \
              -D BUILD_INCLUDE_SYMLINK=ON \
              -D BUILD_OPT_PROFILE=Production \
              -D USE_TK=OFF \
              -D CMAKE_BUILD_TYPE=Release \
              -D INSTALL_DIR=${{ github.workspace }}/install \
              -D 3RDPARTY_RAPIDJSON_DIR=${{ github.workspace }}/rapidjson-858451e5b7d1c56cf8f6d58f88cf958351837e53 \
              -D USE_FREETYPE=ON \
              -D USE_DRACO=ON \
              -D USE_FFMPEG=OFF \
              -D USE_FREEIMAGE=ON \
              -D USE_GLES2=ON \
              -D USE_OPENVR=ON \
              -D USE_VTK=ON \
              -D USE_TBB=OFF \
              -D USE_RAPIDJSON=ON \
              -D USE_OPENGL=ON \
              -D CMAKE_CXX_FLAGS="-Werror -Wall -Wextra" \
              -D CMAKE_C_FLAGS="-Werror -Wall -Wextra" ..

    - name: Build OCCT
      run: |
        cd build
        cmake --build . --target install --config Release -- -j

    - name: Upload install directory
      uses: actions/upload-artifact@v4.4.3
      with:
        name: install-linux-clang-x64
        path: install
        retention-days: 7

  prepare-and-build-linux-gcc-x64:
    name: Prepare and Build on Ubuntu with GCC (x64)
    runs-on: ubuntu-24.04

    steps:

    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Install dependencies
      run: sudo apt-get update && sudo apt-get install -y tcl-dev tk-dev cmake gcc g++ make libbtbb-dev libx11-dev libglu1-mesa-dev tcllib tcl-thread tcl libvtk9-dev libopenvr-dev libdraco-dev libfreeimage-dev libegl1-mesa-dev libgles2-mesa-dev libfreetype-dev

    - name: Install rapidjson
      run: |
        wget https://github.com/Tencent/rapidjson/archive/858451e5b7d1c56cf8f6d58f88cf958351837e53.zip -O rapidjson.zip
        unzip rapidjson.zip

    - name: Configure OCCT
      run: |
        mkdir -p build
        cd build
        cmake -G "Unix Makefiles" \
              -D CMAKE_C_COMPILER=gcc \
              -D CMAKE_CXX_COMPILER=g++ \
              -D BUILD_USE_PCH=ON \
              -D BUILD_INCLUDE_SYMLINK=ON \
              -D BUILD_OPT_PROFILE=Production \
              -D USE_TK=OFF \
              -D CMAKE_BUILD_TYPE=Release \
              -D INSTALL_DIR=${{ github.workspace }}/install \
              -D 3RDPARTY_RAPIDJSON_DIR=${{ github.workspace }}/rapidjson-858451e5b7d1c56cf8f6d58f88cf958351837e53 \
              -D USE_FREETYPE=ON \
              -D USE_DRACO=ON \
              -D USE_FFMPEG=OFF \
              -D USE_FREEIMAGE=ON \
              -D USE_GLES2=ON \
              -D USE_OPENVR=ON \
              -D USE_VTK=ON \
              -D USE_TBB=OFF \
              -D USE_RAPIDJSON=ON \
              -D USE_OPENGL=ON ..

    - name: Build OCCT
      run: |
        cd build
        cmake --build . --target install --config Release -- -j

    - name: Upload install directory
      uses: actions/upload-artifact@v4.4.3
      with:
        name: install-linux-gcc-x64
        path: install
        retention-days: 7

  build-inspector-windows:
    name: Build TInspector on Windows
    needs: prepare-and-build-windows-x64
    runs-on: windows-2022
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Build TInspector
      uses: ./.github/actions/build-tinspector
      with:
        platform: windows
        install-artifact-name: install-windows-x64

  build-inspector-linux:
    name: Build TInspector on Linux
    needs: prepare-and-build-linux-clang-x64
    runs-on: ubuntu-24.04
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Build TInspector
      uses: ./.github/actions/build-tinspector
      with:
        platform: linux
        install-artifact-name: install-linux-clang-x64

  build-csharp-windows:
    name: Build CSharp Sample on Windows
    needs: prepare-and-build-windows-x64
    runs-on: windows-2022
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Build CSharp Sample
      uses: ./.github/actions/build-sample-csharp
      with:
        platform: windows
        install-artifact-name: install-windows-x64

  build-mfc-windows:
    name: Build MFC Sample on Windows
    needs: prepare-and-build-windows-x64
    runs-on: windows-2022
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Build MFC Sample
      uses: ./.github/actions/build-sample-mfc
      with:
        platform: windows
        install-artifact-name: install-windows-x64

  build-qt-windows:
    name: Build Qt Sample on Windows
    needs: prepare-and-build-windows-x64
    runs-on: windows-2022
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Build Qt Sample
      uses: ./.github/actions/build-sample-qt
      with:
        platform: windows
        install-artifact-name: install-windows-x64

  build-qt-linux:
    name: Build Qt Sample on Linux
    needs: prepare-and-build-linux-clang-x64
    runs-on: ubuntu-24.04
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Build Qt Sample
      uses: ./.github/actions/build-sample-qt
      with:
        platform: linux
        install-artifact-name: install-linux-clang-x64

  test-windows-x64:
    name: Test on Windows (x64)
    runs-on: windows-2022
    needs: prepare-and-build-windows-x64

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Download and extract 3rdparty dependencies
      run: |
          Invoke-WebRequest -Uri https://github.com/Open-Cascade-SAS/OCCT/releases/download/V7_9_0_beta1/3rdparty-vc14-64.zip -OutFile 3rdparty-vc14-64.zip
          Expand-Archive -Path 3rdparty-vc14-64.zip -DestinationPath .
          Remove-Item 3rdparty-vc14-64.zip
      shell: pwsh

    - name: Download and extract test data
      run: |
        cd data
        Invoke-WebRequest -Uri https://github.com/Open-Cascade-SAS/OCCT/releases/download/V7_9_0_beta1/opencascade-dataset-7.9.0.zip -OutFile opencascade-dataset-7.9.0.zip
        Expand-Archive -Path opencascade-dataset-7.9.0.zip -DestinationPath .
        Remove-Item opencascade-dataset-7.9.0.zip
      shell: pwsh

    - name: Download and extract install directory
      uses: actions/download-artifact@v4.1.7
      with:
        name: install-windows-x64
        path: install

    - name: Download and extract Mesa3D
      run: |
        curl -L -o mesa3d.7z https://github.com/pal1000/mesa-dist-win/releases/download/24.3.2/mesa3d-24.3.2-release-mingw.7z
        7z x mesa3d.7z -omesa3d

    - name: Run system-wide deployment
      run: |
        cd mesa3d
        .\systemwidedeploy.cmd 1
        .\systemwidedeploy.cmd 5
      shell: cmd

    - name: Install CJK Fonts
      run: |
        Invoke-WebRequest -Uri https://noto-website-2.storage.googleapis.com/pkgs/Noto-hinted.zip -OutFile Noto-hinted.zip
        Expand-Archive -Path Noto-hinted.zip -DestinationPath $env:windir\Fonts
        Remove-Item Noto-hinted.zip
      shell: pwsh

    - name: Run tests
      run: |
        cd install
        call env.bat vc14 win64 release
        DRAWEXE.exe -v -f ${{ github.workspace }}/.github/actions/testgrid/testwindows.tcl
      shell: cmd
      env:
        LIBGL_ALWAYS_SOFTWARE: 1
        CSF_TestScriptsPath: ${{ github.workspace }}/tests
        CSF_TestDataPath: ${{ github.workspace }}/data

    - name: Clean up test results
      run: |
        cd install
        call env.bat vc14 win64 release
        DRAWEXE.exe -v -c cleanuptest results/windows-x64
      shell: cmd
      env:
        LIBGL_ALWAYS_SOFTWARE: 1
        CSF_TestScriptsPath: ${{ github.workspace }}/tests
        CSF_TestDataPath: ${{ github.workspace }}/data

    - name: Upload test results
      uses: actions/upload-artifact@v4.4.3
      with:
        name: results-windows-x64
        path: |
          install/results/**/*.log
          install/results/**/*.png
          install/results/**/*.html
        retention-days: 15

  retest-windows-x64:
    name: Regression Test on Windows (x64)
    runs-on: windows-2022
    needs: test-windows-x64

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Download previous test results
      uses: actions/download-artifact@v4.1.7
      with:
        name: results-windows-x64
        path: install/results

    - name: Check for test failures
      id: check_failures
      run: |
        $failedCount = 0
        if (Test-Path "install/results/windows-x64/tests.log") {
          $content = Get-Content "install/results/windows-x64/tests.log"
          $totalLine = $content | Select-String "Total cases:"
          if ($totalLine) {
            if ($totalLine -match "FAILED") {
              $failedCount = ($totalLine | ForEach-Object { $_.Line -replace '.*?(\d+) FAILED.*','$1' }) -as [int]
            }
          }
          echo "failed_count=$failedCount" >> $env:GITHUB_OUTPUT
          if ($failedCount -gt 0) {
            echo "Tests failed count: $failedCount"
          }
        }
      shell: pwsh

    - name: Download and extract 3rdparty dependencies
      if: steps.check_failures.outputs.failed_count > 0
      run: |
          Invoke-WebRequest -Uri https://github.com/Open-Cascade-SAS/OCCT/releases/download/V7_9_0_beta1/3rdparty-vc14-64.zip -OutFile 3rdparty-vc14-64.zip
          Expand-Archive -Path 3rdparty-vc14-64.zip -DestinationPath .
          Remove-Item 3rdparty-vc14-64.zip
      shell: pwsh

    - name: Download test data
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        cd data
        Invoke-WebRequest -Uri https://github.com/Open-Cascade-SAS/OCCT/releases/download/V7_9_0_beta1/opencascade-dataset-7.9.0.zip -OutFile opencascade-dataset-7.9.0.zip
        Expand-Archive -Path opencascade-dataset-7.9.0.zip -DestinationPath .
        Remove-Item opencascade-dataset-7.9.0.zip
      shell: pwsh

    - name: Download and extract install directory
      if: steps.check_failures.outputs.failed_count > 0
      uses: actions/download-artifact@v4.1.7
      with:
        name: install-windows-x64
        path: install

    - name: Download and extract Mesa3D
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        curl -L -o mesa3d.7z https://github.com/pal1000/mesa-dist-win/releases/download/24.3.2/mesa3d-24.3.2-release-mingw.7z
        7z x mesa3d.7z -omesa3d

    - name: Run system-wide deployment
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        cd mesa3d
        .\systemwidedeploy.cmd 1
        .\systemwidedeploy.cmd 5
      shell: cmd

    - name: Install CJK Fonts
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        Invoke-WebRequest -Uri https://noto-website-2.storage.googleapis.com/pkgs/Noto-hinted.zip -OutFile Noto-hinted.zip
        Expand-Archive -Path Noto-hinted.zip -DestinationPath $env:windir\Fonts
        Remove-Item Noto-hinted.zip
      shell: pwsh

    - name: Run regression tests
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        cd install
        call env.bat vc14 win64 release
        DRAWEXE.exe -v -c testgrid -regress results/windows-x64 -outdir results/windows-x64-retest -parallel 0
      shell: cmd
      env:
        LIBGL_ALWAYS_SOFTWARE: 1
        CSF_TestScriptsPath: ${{ github.workspace }}/tests
        CSF_TestDataPath: ${{ github.workspace }}/data

    - name: Repeating failed tests
      if: steps.check_failures.outputs.failed_count > 0 && steps.check_failures.outputs.failed_count < 20
      run: |
        cd install
        call env.bat vc14 win64 release
        # Repeat failed tests for 10 times
        for /l %%i in (1,1,10) do (
          DRAWEXE.exe -v -c testgrid -regress results/windows-x64-retest -outdir results/windows-x64-retest -parallel 0 -overwrite
          DRAWEXE.exe -v -c "testsummarize results/windows-x64-retest"
        )
      shell: cmd
      env:
        LIBGL_ALWAYS_SOFTWARE: 1
        CSF_TestScriptsPath: ${{ github.workspace }}/tests
        CSF_TestDataPath: ${{ github.workspace }}/data

    - name: Upload regression test results
      if: steps.check_failures.outputs.failed_count > 0
      uses: actions/upload-artifact@v4.4.3
      with:
        name: results-windows-x64-retest
        path: install/results/windows-x64-retest
        retention-days: 15
        overwrite: true

    - name: Copy retest results back to original location
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        cd install/results/windows-x64-retest
        if exist "*" (
          xcopy /s /y /i . ..\windows-x64\
          cd ..\..\
          call env.bat vc14 win64 release
          DRAWEXE.exe -v -c "testsummarize results/windows-x64"
        ) else (
          echo No retest results to copy - directory is empty
        )
      shell: cmd

    - name: Upload updated test results
      if: ${{ hashFiles('install/results/windows-x64-retest/*') != '' }}
      uses: actions/upload-artifact@v4.4.3
      with:
        name: results-windows-x64
        path: install/results/windows*-x64/
        retention-days: 15
        overwrite: true

    - name: Check test failures
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        cd install/results/windows-x64-retest
        $failedCount = 0
        if (Test-Path tests.log) {
          $content = Get-Content tests.log
          $totalLine = $content | Select-String "Total cases:"
          if ($totalLine) {
            if ($totalLine -match "FAILED") {
              $failedCount = ($totalLine | ForEach-Object { $_.Line -replace '.*?(\d+) FAILED.*','$1' }) -as [int]
            }
          }
          if ($failedCount -gt 0) {
            Write-Error "Number of FAILED tests ($failedCount) exceeds threshold of 0"
            echo "FAILED_COUNT=$failedCount" >> $env:GITHUB_ENV
            exit 1
          }
          Write-Output "Found $failedCount FAILED tests"
        }
      shell: pwsh

  test-windows-clang-x64:
    name: Test on Windows with Clang (x64)
    runs-on: windows-2022
    needs: prepare-and-build-windows-clang-x64

    steps:

    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Download and extract 3rdparty dependencies
      run: |
          Invoke-WebRequest -Uri https://github.com/Open-Cascade-SAS/OCCT/releases/download/V7_9_0_beta1/3rdparty-vc14-64.zip -OutFile 3rdparty-vc14-64.zip
          Expand-Archive -Path 3rdparty-vc14-64.zip -DestinationPath .
          Remove-Item 3rdparty-vc14-64.zip
      shell: pwsh

    - name: Download and extract test data
      run: |
        cd data
        Invoke-WebRequest -Uri https://github.com/Open-Cascade-SAS/OCCT/releases/download/V7_9_0_beta1/opencascade-dataset-7.9.0.zip -OutFile opencascade-dataset-7.9.0.zip
        Expand-Archive -Path opencascade-dataset-7.9.0.zip -DestinationPath .
        Remove-Item opencascade-dataset-7.9.0.zip
      shell: pwsh

    - name: Download and extract install directory
      uses: actions/download-artifact@v4.1.7
      with:
        name: install-windows-clang-x64
        path: install

    - name: Download and extract Mesa3D
      run: |
        curl -L -o mesa3d.7z https://github.com/pal1000/mesa-dist-win/releases/download/24.3.2/mesa3d-24.3.2-release-mingw.7z
        7z x mesa3d.7z -omesa3d

    - name: Run system-wide deployment
      run: |
        cd mesa3d
        .\systemwidedeploy.cmd 1
        .\systemwidedeploy.cmd 5
      shell: cmd

    - name: Install CJK Fonts
      run: |
        Invoke-WebRequest -Uri https://noto-website-2.storage.googleapis.com/pkgs/Noto-hinted.zip -OutFile Noto-hinted.zip
        Expand-Archive -Path Noto-hinted.zip -DestinationPath $env:windir\Fonts
        Remove-Item Noto-hinted.zip
      shell: pwsh

    - name: Run tests
      run: |
        cd install
        call env.bat clang win64 release
        DRAWEXE.exe -v -f ${{ github.workspace }}/.github/actions/testgrid/testwindowsclang.tcl
      shell: cmd
      env:
        LIBGL_ALWAYS_SOFTWARE: 1
        CSF_TestScriptsPath: ${{ github.workspace }}/tests
        CSF_TestDataPath: ${{ github.workspace }}/data

    - name: Clean up test results
      run: |
        cd install
        call env.bat clang win64 release
        DRAWEXE.exe -v -c cleanuptest results/windows-clang-x64
      shell: cmd
      env:
        LIBGL_ALWAYS_SOFTWARE: 1
        CSF_TestScriptsPath: ${{ github.workspace }}/tests
        CSF_TestDataPath: ${{ github.workspace }}/data

    - name: Upload test results
      uses: actions/upload-artifact@v4.4.3
      with:
        name: results-windows-clang-x64
        path: |
          install/results/**/*.log
          install/results/**/*.png
          install/results/**/*.html
        retention-days: 15

  retest-windows-clang-x64:
    name: Regression Test on Windows with Clang (x64)
    runs-on: windows-2022
    needs: test-windows-clang-x64

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Download previous test results
      uses: actions/download-artifact@v4.1.7
      with:
        name: results-windows-clang-x64
        path: install/results

    - name: Check for test failures
      id: check_failures
      run: |
        $failedCount = 0
        if (Test-Path "install/results/windows-clang-x64/tests.log") {
          $content = Get-Content "install/results/windows-clang-x64/tests.log"
          $totalLine = $content | Select-String "Total cases:"
          if ($totalLine) {
            if ($totalLine -match "FAILED") {
              $failedCount = ($totalLine | ForEach-Object { $_.Line -replace '.*?(\d+) FAILED.*','$1' }) -as [int]
            }
          }
          echo "failed_count=$failedCount" >> $env:GITHUB_OUTPUT
          if ($failedCount -gt 0) {
            echo "Tests failed count: $failedCount"
          }
        }
      shell: pwsh

    - name: Download and extract 3rdparty dependencies
      if: steps.check_failures.outputs.failed_count > 0
      run: |
          Invoke-WebRequest -Uri https://github.com/Open-Cascade-SAS/OCCT/releases/download/V7_9_0_beta1/3rdparty-vc14-64.zip -OutFile 3rdparty-vc14-64.zip
          Expand-Archive -Path 3rdparty-vc14-64.zip -DestinationPath .
          Remove-Item 3rdparty-vc14-64.zip
      shell: pwsh

    - name: Download test data
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        cd data
        Invoke-WebRequest -Uri https://github.com/Open-Cascade-SAS/OCCT/releases/download/V7_9_0_beta1/opencascade-dataset-7.9.0.zip -OutFile opencascade-dataset-7.9.0.zip
        Expand-Archive -Path opencascade-dataset-7.9.0.zip -DestinationPath .
        Remove-Item opencascade-dataset-7.9.0.zip
      shell: pwsh

    - name: Download and extract install directory
      if: steps.check_failures.outputs.failed_count > 0
      uses: actions/download-artifact@v4.1.7
      with:
        name: install-windows-clang-x64
        path: install

    - name: Download and extract Mesa3D
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        curl -L -o mesa3d.7z https://github.com/pal1000/mesa-dist-win/releases/download/24.3.2/mesa3d-24.3.2-release-mingw.7z
        7z x mesa3d.7z -omesa3d

    - name: Run system-wide deployment
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        cd mesa3d
        .\systemwidedeploy.cmd 1
        .\systemwidedeploy.cmd 5
      shell: cmd

    - name: Install CJK Fonts
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        Invoke-WebRequest -Uri https://noto-website-2.storage.googleapis.com/pkgs/Noto-hinted.zip -OutFile Noto-hinted.zip
        Expand-Archive -Path Noto-hinted.zip -DestinationPath $env:windir\Fonts
        Remove-Item Noto-hinted.zip
      shell: pwsh

    - name: Run regression tests
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        cd install
        call env.bat clang win64 release
        DRAWEXE.exe -v -c testgrid -regress results/windows-clang-x64 -outdir results/windows-clang-x64-retest -parallel 0
      shell: cmd
      env:
        LIBGL_ALWAYS_SOFTWARE: 1
        CSF_TestScriptsPath: ${{ github.workspace }}/tests
        CSF_TestDataPath: ${{ github.workspace }}/data

    - name: Repeating failed tests
      if: steps.check_failures.outputs.failed_count > 0 && steps.check_failures.outputs.failed_count < 20
      run: |
        cd install
        call env.bat clang win64 release
        # Repeat failed tests for 10 times
        for /l %%i in (1,1,10) do (
          DRAWEXE.exe -v -c testgrid -regress results/windows-clang-x64-retest -outdir results/windows-clang-x64-retest -parallel 0 -overwrite
          DRAWEXE.exe -v -c "testsummarize results/windows-clang-x64-retest"
        )
      shell: cmd
      env:
        LIBGL_ALWAYS_SOFTWARE: 1
        CSF_TestScriptsPath: ${{ github.workspace }}/tests
        CSF_TestDataPath: ${{ github.workspace }}/data

    - name: Upload regression test results
      if: steps.check_failures.outputs.failed_count > 0
      uses: actions/upload-artifact@v4.4.3
      with:
        name: results-windows-clang-x64-retest
        path: install/results/windows-clang-x64-retest
        retention-days: 15
        overwrite: true

    - name: Copy retest results back to original location
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        cd install/results/windows-clang-x64-retest
        if exist "*" (
          xcopy /s /y /i . ..\windows-clang-x64\
          cd ..\..\
          call env.bat clang win64 release
          DRAWEXE.exe -v -c "testsummarize results/windows-clang-x64"
        ) else (
          echo No retest results to copy - directory is empty
        )
      shell: cmd

    - name: Upload updated test results
      if: ${{ hashFiles('install/results/windows-clang-x64-retest/*') != '' }}
      uses: actions/upload-artifact@v4.4.3
      with:
        name: results-windows-clang-x64
        path: install/results/windows*-x64/
        retention-days: 15
        overwrite: true

    - name: Check test failures
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        cd install/results/windows-clang-x64-retest
        $failedCount = 0
        if (Test-Path tests.log) {
          $content = Get-Content tests.log
          $totalLine = $content | Select-String "Total cases:"
          if ($totalLine) {
            if ($totalLine -match "FAILED") {
              $failedCount = ($totalLine | ForEach-Object { $_.Line -replace '.*?(\d+) FAILED.*','$1' }) -as [int]
            }
          }
          if ($failedCount -gt 0) {
            Write-Error "Number of FAILED tests ($failedCount) exceeds threshold of 0"
            echo "FAILED_COUNT=$failedCount" >> $env:GITHUB_ENV
            exit 1
          }
          Write-Output "Found $failedCount FAILED tests"
        }
      shell: pwsh

  test-macos-x64:
    name: Test on macOS (x64)
    runs-on: macos-15
    needs: prepare-and-build-macos-x64

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Install dependencies
      run: |
        brew update
        brew install tcl-tk tbb gl2ps xerces-c \
                     libxmu libxi libxft libxpm \
                     glew freeimage draco glfw

    - name: Download test data
      run: |
        cd data
        curl -L -O https://github.com/Open-Cascade-SAS/OCCT/releases/download/V7_9_0_beta1/opencascade-dataset-7.9.0.tar.xz
        tar -xf opencascade-dataset-7.9.0.tar.xz

    - name: Download and extract install directory
      uses: actions/download-artifact@v4.1.7
      with:
        name: install-macos-x64
        path: install

    - name: Set LIBGL_ALWAYS_SOFTWARE environment variable
      run: echo "LIBGL_ALWAYS_SOFTWARE=1" >> $GITHUB_ENV

    - name: Set execute permissions on DRAWEXE
      run: chmod +x install/bin/DRAWEXE

    - name: Run tests
      run: |
         cd install
         cd bin
         source env.sh
         ./DRAWEXE -v -f ${{ github.workspace }}/.github/actions/testgrid/testmacos.tcl
      shell: bash
      env:
        LIBGL_ALWAYS_SOFTWARE: 1
        CSF_TestScriptsPath: ${{ github.workspace }}/tests
        CSF_TestDataPath: ${{ github.workspace }}/data

    - name: Clean up test results
      run: |
         cd install
         cd bin
         source env.sh
         ./DRAWEXE -v -c cleanuptest results/macos-x64
      shell: bash
      env:
        LIBGL_ALWAYS_SOFTWARE: 1
        CSF_TestScriptsPath: ${{ github.workspace }}/tests
        CSF_TestDataPath: ${{ github.workspace }}/data

    - name: Upload test results
      uses: actions/upload-artifact@v4.4.3
      with:
        name: results-macos-x64
        path: |
          install/bin/results/**/*.log
          install/bin/results/**/*.png
          install/bin/results/**/*.html
        retention-days: 15

  retest-macos-x64:
    name: Regression Test on macOS (x64)
    runs-on: macos-15
    needs: test-macos-x64

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Download previous test results
      uses: actions/download-artifact@v4.1.7
      with:
        name: results-macos-x64
        path: install/bin/results

    - name: Check for test failures
      id: check_failures
      run: |
        failed_count=0
        if [ -f "install/bin/results/macos-x64/tests.log" ]; then
          total_line=$(grep "Total cases:" install/bin/results/macos-x64/tests.log)
          if [ ! -z "$total_line" ]; then
            if [[ $total_line =~ "FAILED" ]]; then
              failed_count=$(echo "$total_line" | grep -o "[0-9]* FAILED" | awk '{print $1}')
            fi
          fi
          echo "failed_count=$failed_count" >> $GITHUB_OUTPUT
          if [ "$failed_count" -gt 0 ]; then
            echo "Tests failed count: $failed_count"
          fi
        fi
      shell: bash

    - name: Install dependencies
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        brew update
        brew install tcl-tk tbb gl2ps xerces-c \
                     libxmu libxi libxft libxpm \
                     glew freeimage draco glfw

    - name: Download test data
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        cd data
        curl -L -O https://github.com/Open-Cascade-SAS/OCCT/releases/download/V7_9_0_beta1/opencascade-dataset-7.9.0.tar.xz
        tar -xf opencascade-dataset-7.9.0.tar.xz

    - name: Download and extract install directory
      if: steps.check_failures.outputs.failed_count > 0
      uses: actions/download-artifact@v4.1.7
      with:
        name: install-macos-x64
        path: install

    - name: Set LIBGL_ALWAYS_SOFTWARE environment variable
      if: steps.check_failures.outputs.failed_count > 0
      run: echo "LIBGL_ALWAYS_SOFTWARE=1" >> $GITHUB_ENV

    - name: Set execute permissions on DRAWEXE
      if: steps.check_failures.outputs.failed_count > 0
      run: chmod +x install/bin/DRAWEXE

    - name: Run regression tests
      if: steps.check_failures.outputs.failed_count > 0
      run: |
         cd install
         cd bin
         source env.sh
         ./DRAWEXE -v -c testgrid -regress results/macos-x64 -outdir results/macos-x64-retest -parallel 0
      shell: bash
      env:
        LIBGL_ALWAYS_SOFTWARE: 1
        CSF_TestScriptsPath: ${{ github.workspace }}/tests
        CSF_TestDataPath: ${{ github.workspace }}/data

    - name: Repeating failed tests
      if: steps.check_failures.outputs.failed_count > 0 && steps.check_failures.outputs.failed_count < 20
      run: |
        cd install
        cd bin
        source env.sh
        # Repeat failed tests for 10 times
        for i in {1..10}; do
          ./DRAWEXE -v -c testgrid -regress results/macos-x64-retest -outdir results/macos-x64-retest -parallel 0 -overwrite
          ./DRAWEXE -v -c "testsummarize results/macos-x64-retest"
        done
      shell: bash
      env:
        LIBGL_ALWAYS_SOFTWARE: 1
        CSF_TestScriptsPath: ${{ github.workspace }}/tests
        CSF_TestDataPath: ${{ github.workspace }}/data

    - name: Copy retest results back to original location
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        cd install/bin/results/macos-x64-retest
        if [ "$(ls -A)" ]; then
          cp -rf * ../macos-x64/
          
          cd ../../
          source env.sh
          ./DRAWEXE -v -c testsummarize results/macos-x64
        else
          echo "No retest results to copy - directory is empty"
        fi

    - name: Upload regression test results
      if: steps.check_failures.outputs.failed_count > 0
      uses: actions/upload-artifact@v4.4.3
      with:
        name: results-macos-x64-retest
        path: install/bin/results/macos-x64-retest
        retention-days: 15
        overwrite: true

    - name: Upload updated test results
      if: ${{ hashFiles('install/bin/results/macos-x64-retest/*') != '' }}
      uses: actions/upload-artifact@v4.4.3
      with:
        name: results-macos-x64
        path: install/bin/results/macos*-x64/
        retention-days: 15
        overwrite: true

    - name: Check test failures
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        cd install/bin/results/macos-x64-retest
        if [ -f tests.log ]; then
          FAILED_COUNT=$(grep "Total cases:" tests.log | grep -o "[0-9]* FAILED" | awk '{print $1}')
          if [ ! -z "$FAILED_COUNT" ] && [ $FAILED_COUNT -gt 0 ]; then
            echo "::error::Number of FAILED tests ($FAILED_COUNT) exceeds threshold of 0"
            echo "FAILED_COUNT=$FAILED_COUNT" >> $GITHUB_ENV
            exit 1
          fi
          echo "Found $FAILED_COUNT FAILED tests"
        fi

  test-macos-gcc-x64:
    name: Test on macOS with GCC (x64)
    runs-on: macos-15
    needs: prepare-and-build-macos-gcc-x64

    steps:

    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Install dependencies
      run: |
        brew update
        brew install tcl-tk tbb gl2ps xerces-c \
                     libxmu libxi libxft libxpm \
                     glew freeimage draco glfw

    - name: Download test data
      run: |
        cd data
        curl -L -O https://github.com/Open-Cascade-SAS/OCCT/releases/download/V7_9_0_beta1/opencascade-dataset-7.9.0.tar.xz
        tar -xf opencascade-dataset-7.9.0.tar.xz

    - name: Download and extract install directory
      uses: actions/download-artifact@v4.1.7
      with:
        name: install-macos-gcc-x64
        path: install

    - name: Set LIBGL_ALWAYS_SOFTWARE environment variable
      run: echo "LIBGL_ALWAYS_SOFTWARE=1" >> $GITHUB_ENV

    - name: Set execute permissions on DRAWEXE
      run: chmod +x install/bin/DRAWEXE

    - name: Run tests
      run: |
         cd install
         cd bin
         source env.sh
         ./DRAWEXE -v -f ${{ github.workspace }}/.github/actions/testgrid/testmacosgcc.tcl
      shell: bash
      env:
        LIBGL_ALWAYS_SOFTWARE: 1
        CSF_TestScriptsPath: ${{ github.workspace }}/tests
        CSF_TestDataPath: ${{ github.workspace }}/data

    - name: Clean up test results
      run: |
         cd install
         cd bin
         source env.sh
         ./DRAWEXE -v -c cleanuptest results/macos-gcc-x64
      shell: bash
      env:
        LIBGL_ALWAYS_SOFTWARE: 1
        CSF_TestScriptsPath: ${{ github.workspace }}/tests
        CSF_TestDataPath: ${{ github.workspace }}/data

    - name: Upload test results
      uses: actions/upload-artifact@v4.4.3
      with:
        name: results-macos-gcc-x64
        path: |
          install/bin/results/**/*.log
          install/bin/results/**/*.png
          install/bin/results/**/*.html
        retention-days: 15

  retest-macos-gcc-x64:
    name: Regression Test on macOS with GCC (x64)
    runs-on: macos-15
    needs: test-macos-gcc-x64

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Download previous test results
      uses: actions/download-artifact@v4.1.7
      with:
        name: results-macos-gcc-x64
        path: install/bin/results

    - name: Check for test failures
      id: check_failures
      run: |
        failed_count=0
        if [ -f "install/bin/results/macos-gcc-x64/tests.log" ]; then
          total_line=$(grep "Total cases:" install/bin/results/macos-gcc-x64/tests.log)
          if [ ! -z "$total_line" ]; then
            if [[ $total_line =~ "FAILED" ]]; then
              failed_count=$(echo "$total_line" | grep -o "[0-9]* FAILED" | awk '{print $1}')
            fi
          fi
          echo "failed_count=$failed_count" >> $GITHUB_OUTPUT
          if [ "$failed_count" -gt 0 ]; then
            echo "Tests failed count: $failed_count"
          fi
        fi
      shell: bash

    - name: Install dependencies
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        brew update
        brew install tcl-tk tbb gl2ps xerces-c \
                     libxmu libxi libxft libxpm \
                     glew draco glfw

    - name: Download test data
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        cd data
        curl -L -O https://github.com/Open-Cascade-SAS/OCCT/releases/download/V7_9_0_beta1/opencascade-dataset-7.9.0.tar.xz
        tar -xf opencascade-dataset-7.9.0.tar.xz

    - name: Download and extract install directory
      if: steps.check_failures.outputs.failed_count > 0
      uses: actions/download-artifact@v4.1.7
      with:
        name: install-macos-gcc-x64
        path: install

    - name: Set LIBGL_ALWAYS_SOFTWARE environment variable
      if: steps.check_failures.outputs.failed_count > 0
      run: echo "LIBGL_ALWAYS_SOFTWARE=1" >> $GITHUB_ENV

    - name: Set execute permissions on DRAWEXE
      if: steps.check_failures.outputs.failed_count > 0
      run: chmod +x install/bin/DRAWEXE

    - name: Run regression tests
      if: steps.check_failures.outputs.failed_count > 0
      run: |
         cd install
         cd bin
         source env.sh
         ./DRAWEXE -v -c testgrid -regress results/macos-gcc-x64 -outdir results/macos-gcc-x64-retest -parallel 0
      shell: bash
      env:
        LIBGL_ALWAYS_SOFTWARE: 1
        CSF_TestScriptsPath: ${{ github.workspace }}/tests
        CSF_TestDataPath: ${{ github.workspace }}/data

    - name: Repeating failed tests
      if: steps.check_failures.outputs.failed_count > 0 && steps.check_failures.outputs.failed_count < 20
      run: |
        cd install
        cd bin
        source env.sh
        # Repeat failed tests for 10 times
        for i in {1..10}; do
          ./DRAWEXE -v -c testgrid -regress results/macos-gcc-x64-retest -outdir results/macos-gcc-x64-retest -parallel 0 -overwrite
          ./DRAWEXE -v -c "testsummarize results/macos-gcc-x64-retest"
        done
      shell: bash
      env:
        LIBGL_ALWAYS_SOFTWARE: 1
        CSF_TestScriptsPath: ${{ github.workspace }}/tests
        CSF_TestDataPath: ${{ github.workspace }}/data

    - name: Upload regression test results
      if: steps.check_failures.outputs.failed_count > 0
      uses: actions/upload-artifact@v4.4.3
      with:
        name: results-macos-gcc-x64-retest
        path: install/bin/results/macos-gcc-x64-retest
        retention-days: 15
        overwrite: true

    - name: Copy retest results back to original location
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        cd install/bin/results/macos-gcc-x64-retest
        if [ "$(ls -A)" ]; then
          cp -rf * ../macos-gcc-x64/
          
          cd ../../
          source env.sh
          ./DRAWEXE -v -c testsummarize results/macos-gcc-x64
        else
          echo "No retest results to copy - directory is empty"
        fi

    - name: Upload updated test results
      if: ${{ hashFiles('install/bin/results/macos-gcc-x64-retest/*') != '' }}
      uses: actions/upload-artifact@v4.4.3
      with:
        name: results-macos-gcc-x64
        path: install/bin/results/macos*-x64/
        retention-days: 15
        overwrite: true

    - name: Check test failures
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        cd install/bin/results/macos-gcc-x64-retest
        if [ -f tests.log ]; then
          FAILED_COUNT=$(grep "Total cases:" tests.log | grep -o "[0-9]* FAILED" | awk '{print $1}')
          if [ ! -z "$FAILED_COUNT" ] && [ $FAILED_COUNT -gt 0 ]; then
            echo "::error::Number of FAILED tests ($FAILED_COUNT) exceeds threshold of 0"
            echo "FAILED_COUNT=$FAILED_COUNT" >> $GITHUB_ENV
            exit 1
          fi
          echo "Found $FAILED_COUNT FAILED tests"
        fi

  test-linux-clang-x64:
    name: Test on Linux with Clang (x64)
    runs-on: ubuntu-24.04
    needs: prepare-and-build-linux-clang-x64

    steps:

    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Install dependencies
      run: sudo apt-get update && sudo apt-get install -y tcl-dev tk-dev cmake clang make libbtbb-dev libx11-dev libglu1-mesa-dev tcllib tcl-thread tcl libvtk9-dev libopenvr-dev libdraco-dev libfreeimage-dev libegl1-mesa-dev libgles2-mesa-dev libfreetype-dev fonts-noto-cjk fonts-liberation fonts-ubuntu fonts-liberation fonts-ubuntu fonts-noto-cjk fonts-ipafont-gothic fonts-ipafont-mincho fonts-unfonts-core

    - name: Setup Xvfb and Mesa
      uses: ./.github/actions/setup-xvfb-mesa

    - name: Download test data
      run: |
        cd data
        wget https://github.com/Open-Cascade-SAS/OCCT/releases/download/V7_9_0_beta1/opencascade-dataset-7.9.0.tar.xz
        tar -xf opencascade-dataset-7.9.0.tar.xz

    - name: Download and extract install directory
      uses: actions/download-artifact@v4.1.7
      with:
        name: install-linux-clang-x64
        path: install

    - name: Set execute permissions on DRAWEXE
      run: chmod +x install/bin/DRAWEXE

    - name: Run tests
      run: |
         cd install
         cd bin
         source env.sh
         ./DRAWEXE -v -f ${{ github.workspace }}/.github/actions/testgrid/testlinuxclang.tcl
      shell: bash
      env:
        DISPLAY: :99
        LIBGL_ALWAYS_SOFTWARE: 1
        CSF_TestScriptsPath: ${{ github.workspace }}/tests
        CSF_TestDataPath: ${{ github.workspace }}/data

    - name: Clean up test results
      run: |
         cd install
         cd bin
         source env.sh
         ./DRAWEXE -v -c cleanuptest results/linux-clang-x64
      shell: bash
      env:
        DISPLAY: :99
        LIBGL_ALWAYS_SOFTWARE: 1
        CSF_TestScriptsPath: ${{ github.workspace }}/tests
        CSF_TestDataPath: ${{ github.workspace }}/data

    - name: Upload test results
      uses: actions/upload-artifact@v4.4.3
      with:
        name: results-linux-clang-x64
        path: |
          install/bin/results/**/*.log
          install/bin/results/**/*.png
          install/bin/results/**/*.html
        retention-days: 15

  retest-linux-clang-x64:
    name: Regression Test on Linux with Clang (x64)
    runs-on: ubuntu-24.04
    needs: test-linux-clang-x64

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Download previous test results
      uses: actions/download-artifact@v4.1.7
      with:
        name: results-linux-clang-x64
        path: install/bin/results

    - name: Check for test failures
      id: check_failures
      run: |
        failed_count=0
        if [ -f "install/bin/results/linux-clang-x64/tests.log" ]; then
          total_line=$(grep "Total cases:" install/bin/results/linux-clang-x64/tests.log)
          if [ ! -z "$total_line" ]; then
            if [[ $total_line =~ "FAILED" ]]; then
              failed_count=$(echo "$total_line" | grep -o "[0-9]* FAILED" | awk '{print $1}')
            fi
          fi
          echo "failed_count=$failed_count" >> $GITHUB_OUTPUT
          if [ "$failed_count" -gt 0 ]; then
            echo "Tests failed count: $failed_count"
          fi
        fi
      shell: bash

    - name: Install dependencies
      if: steps.check_failures.outputs.failed_count > 0
      run: sudo apt-get update && sudo apt-get install -y tcl-dev tk-dev cmake clang make libbtbb-dev libx11-dev libglu1-mesa-dev tcllib tcl-thread tcl libvtk9-dev libopenvr-dev libdraco-dev libfreeimage-dev libegl1-mesa-dev libgles2-mesa-dev libfreetype-dev fonts-noto-cjk fonts-liberation fonts-ubuntu fonts-liberation fonts-ubuntu fonts-noto-cjk fonts-ipafont-gothic fonts-ipafont-mincho fonts-unfonts-core

    - name: Setup Xvfb and Mesa
      if: steps.check_failures.outputs.failed_count > 0
      uses: ./.github/actions/setup-xvfb-mesa

    - name: Download test data
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        cd data
        wget https://github.com/Open-Cascade-SAS/OCCT/releases/download/V7_9_0_beta1/opencascade-dataset-7.9.0.tar.xz
        tar -xf opencascade-dataset-7.9.0.tar.xz

    - name: Download and extract install directory
      if: steps.check_failures.outputs.failed_count > 0
      uses: actions/download-artifact@v4.1.7
      with:
        name: install-linux-clang-x64
        path: install

    - name: Set execute permissions on DRAWEXE
      if: steps.check_failures.outputs.failed_count > 0
      run: chmod +x install/bin/DRAWEXE

    - name: Run regression tests
      if: steps.check_failures.outputs.failed_count > 0
      run: |
         cd install
         cd bin
         source env.sh
         ./DRAWEXE -v -c testgrid -regress results/linux-clang-x64 -outdir results/linux-clang-x64-retest -parallel 0
      shell: bash
      env:
        DISPLAY: :99
        LIBGL_ALWAYS_SOFTWARE: 1
        CSF_TestScriptsPath: ${{ github.workspace }}/tests
        CSF_TestDataPath: ${{ github.workspace }}/data

    - name: Repeating failed tests
      if: steps.check_failures.outputs.failed_count > 0 && steps.check_failures.outputs.failed_count < 20
      run: |
        cd install
        cd bin
        source env.sh
        # Repeat failed tests for 10 times
        for i in {1..10}; do
          ./DRAWEXE -v -c testgrid -regress results/linux-clang-x64-retest -outdir results/linux-clang-x64-retest -parallel 0 -overwrite
          ./DRAWEXE -v -c "testsummarize results/linux-clang-x64-retest"
        done
      shell: bash
      env:
        DISPLAY: :99
        LIBGL_ALWAYS_SOFTWARE: 1
        CSF_TestScriptsPath: ${{ github.workspace }}/tests
        CSF_TestDataPath: ${{ github.workspace }}/data

    - name: Upload regression test results
      if: steps.check_failures.outputs.failed_count > 0
      uses: actions/upload-artifact@v4.4.3
      with:
        name: results-linux-clang-x64-retest
        path: install/bin/results/linux-clang-x64-retest
        retention-days: 15
        overwrite: true

    - name: Copy retest results back to original location
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        cd install/bin/results/linux-clang-x64-retest
        if [ "$(ls -A)" ]; then
          cp -rf * ../linux-clang-x64/
          
          cd ../../
          source env.sh
          ./DRAWEXE -v -c testsummarize results/linux-clang-x64
        else
          echo "No retest results to copy - directory is empty"
        fi

    - name: Upload updated test results
      if: ${{ hashFiles('install/bin/results/linux-clang-x64-retest/*') != '' }}
      uses: actions/upload-artifact@v4.4.3
      with:
        name: results-linux-clang-x64
        path: install/bin/results/linux*-x64/
        retention-days: 15
        overwrite: true

    - name: Check test failures
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        cd install/bin/results/linux-clang-x64-retest
        if [ -f tests.log ]; then
          FAILED_COUNT=$(grep "Total cases:" tests.log | grep -o "[0-9]* FAILED" | awk '{print $1}')
          if [ ! -z "$FAILED_COUNT" ] && [ $FAILED_COUNT -gt 0 ]; then
            echo "::error::Number of FAILED tests ($FAILED_COUNT) exceeds threshold of 0"
            echo "FAILED_COUNT=$FAILED_COUNT" >> $GITHUB_ENV
            exit 1
          fi
          echo "Found $FAILED_COUNT FAILED tests"
        fi

  test-linux-gcc-x64:
    name: Test on Linux with GCC (x64)
    runs-on: ubuntu-24.04
    needs: prepare-and-build-linux-gcc-x64

    steps:

    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Install dependencies
      run: sudo apt-get update && sudo apt-get install -y tcl-dev tk-dev cmake gcc g++ make libbtbb-dev libx11-dev libglu1-mesa-dev tcllib tcl-thread tcl libvtk9-dev libopenvr-dev libdraco-dev libfreeimage-dev libegl1-mesa-dev libgles2-mesa-dev libfreetype-dev fonts-noto-cjk fonts-liberation fonts-ubuntu fonts-liberation fonts-ubuntu fonts-noto-cjk fonts-ipafont-gothic fonts-ipafont-mincho fonts-unfonts-core

    - name: Setup Xvfb and Mesa
      uses: ./.github/actions/setup-xvfb-mesa

    - name: Download test data
      run: |
        cd data
        wget https://github.com/Open-Cascade-SAS/OCCT/releases/download/V7_9_0_beta1/opencascade-dataset-7.9.0.tar.xz
        tar -xf opencascade-dataset-7.9.0.tar.xz

    - name: Download and extract install directory
      uses: actions/download-artifact@v4.1.7
      with:
        name: install-linux-gcc-x64
        path: install

    - name: Set execute permissions on DRAWEXE
      run: chmod +x install/bin/DRAWEXE

    - name: Run tests
      run: |
         cd install
         cd bin
         source env.sh
         ./DRAWEXE -v -f ${{ github.workspace }}/.github/actions/testgrid/testlinuxgcc.tcl
      shell: bash
      env:
        DISPLAY: :99
        LIBGL_ALWAYS_SOFTWARE: 1
        CSF_TestScriptsPath: ${{ github.workspace }}/tests
        CSF_TestDataPath: ${{ github.workspace }}/data

    - name: Clean up test results
      run: |
         cd install
         cd bin
         source env.sh
         ./DRAWEXE -v -c cleanuptest results/linux-gcc-x64
      shell: bash
      env:
        DISPLAY: :99
        LIBGL_ALWAYS_SOFTWARE: 1
        CSF_TestScriptsPath: ${{ github.workspace }}/tests
        CSF_TestDataPath: ${{ github.workspace }}/data

    - name: Upload test results
      uses: actions/upload-artifact@v4.4.3
      with:
        name: results-linux-gcc-x64
        path: |
          install/bin/results/**/*.log
          install/bin/results/**/*.png
          install/bin/results/**/*.html
        retention-days: 15

  retest-linux-gcc-x64:
    name: Regression Test on Linux with GCC (x64)
    runs-on: ubuntu-24.04
    needs: test-linux-gcc-x64

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4.1.7

    - name: Download previous test results
      uses: actions/download-artifact@v4.1.7
      with:
        name: results-linux-gcc-x64
        path: install/bin/results

    - name: Check for test failures
      id: check_failures
      run: |
        failed_count=0
        if [ -f "install/bin/results/linux-gcc-x64/tests.log" ]; then
          total_line=$(grep "Total cases:" install/bin/results/linux-gcc-x64/tests.log)
          if [ ! -z "$total_line" ]; then
            if [[ $total_line =~ "FAILED" ]]; then
              failed_count=$(echo "$total_line" | grep -o "[0-9]* FAILED" | awk '{print $1}')
            fi
          fi
          echo "failed_count=$failed_count" >> $GITHUB_OUTPUT
          if [ "$failed_count" -gt 0 ]; then
            echo "Tests failed count: $failed_count"
          fi
        fi
      shell: bash

    - name: Install dependencies
      if: steps.check_failures.outputs.failed_count > 0
      run: sudo apt-get update && sudo apt-get install -y tcl-dev tk-dev cmake gcc g++ make libbtbb-dev libx11-dev libglu1-mesa-dev tcllib tcl-thread tcl libvtk9-dev libopenvr-dev libdraco-dev libfreeimage-dev libegl1-mesa-dev libgles2-mesa-dev libfreetype-dev fonts-noto-cjk fonts-liberation fonts-ubuntu fonts-liberation fonts-ubuntu fonts-noto-cjk fonts-ipafont-gothic fonts-ipafont-mincho fonts-unfonts-core

    - name: Setup Xvfb and Mesa
      if: steps.check_failures.outputs.failed_count > 0
      uses: ./.github/actions/setup-xvfb-mesa

    - name: Download test data
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        cd data
        wget https://github.com/Open-Cascade-SAS/OCCT/releases/download/V7_9_0_beta1/opencascade-dataset-7.9.0.tar.xz
        tar -xf opencascade-dataset-7.9.0.tar.xz

    - name: Download and extract install directory
      if: steps.check_failures.outputs.failed_count > 0
      uses: actions/download-artifact@v4.1.7
      with:
        name: install-linux-gcc-x64
        path: install

    - name: Set execute permissions on DRAWEXE
      if: steps.check_failures.outputs.failed_count > 0
      run: chmod +x install/bin/DRAWEXE

    - name: Run regression tests
      if: steps.check_failures.outputs.failed_count > 0
      run: |
         cd install
         cd bin
         source env.sh
         ./DRAWEXE -v -c testgrid -regress results/linux-gcc-x64 -outdir results/linux-gcc-x64-retest -parallel 0
      shell: bash
      env:
        DISPLAY: :99
        LIBGL_ALWAYS_SOFTWARE: 1
        CSF_TestScriptsPath: ${{ github.workspace }}/tests
        CSF_TestDataPath: ${{ github.workspace }}/data

    - name: Repeating failed tests
      if: steps.check_failures.outputs.failed_count > 0 && steps.check_failures.outputs.failed_count < 20
      run: |
        cd install
        cd bin
        source env.sh
        # Repeat failed tests for 10 times
        for i in {1..10}; do
          ./DRAWEXE -v -c testgrid -regress results/linux-gcc-x64-retest -outdir results/linux-gcc-x64-retest -parallel 0 -overwrite
          ./DRAWEXE -v -c "testsummarize results/linux-gcc-x64-retest"
        done
      shell: bash
      env:
        DISPLAY: :99
        LIBGL_ALWAYS_SOFTWARE: 1
        CSF_TestScriptsPath: ${{ github.workspace }}/tests
        CSF_TestDataPath: ${{ github.workspace }}/data

    - name: Upload regression test results
      if: steps.check_failures.outputs.failed_count > 0
      uses: actions/upload-artifact@v4.4.3
      with:
        name: results-linux-gcc-x64-retest
        path: install/bin/results/linux-gcc-x64-retest
        retention-days: 15
        overwrite: true

    - name: Copy retest results back to original location
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        cd install/bin/results/linux-gcc-x64-retest
        if [ "$(ls -A)" ]; then
          cp -rf * ../linux-gcc-x64/
          
          cd ../../
          source env.sh
          ./DRAWEXE -v -c testsummarize results/linux-gcc-x64
        else
          echo "No retest results to copy - directory is empty"
        fi

    - name: Upload updated test results
      if: ${{ hashFiles('install/bin/results/linux-gcc-x64-retest/*') != '' }}
      uses: actions/upload-artifact@v4.4.3
      with:
        name: results-linux-gcc-x64
        path: install/bin/results/linux*-x64/
        retention-days: 15
        overwrite: true

    - name: Check test failures
      if: steps.check_failures.outputs.failed_count > 0
      run: |
        cd install/bin/results/linux-gcc-x64-retest
        if [ -f tests.log ]; then
          FAILED_COUNT=$(grep "Total cases:" tests.log | grep -o "[0-9]* FAILED" | awk '{print $1}')
          if [ ! -z "$FAILED_COUNT" ] && [ $FAILED_COUNT -gt 0 ]; then
            echo "::error::Number of FAILED tests ($FAILED_COUNT) exceeds threshold of 0"
            echo "FAILED_COUNT=$FAILED_COUNT" >> $GITHUB_ENV
            exit 1
          fi
          echo "Found $FAILED_COUNT FAILED tests"
        fi

  test-summary:
    name: 'Summarize Test Results'
    runs-on: ubuntu-24.04
    if: ${{ !cancelled() && github.event_name == 'pull_request' }}
    needs: [retest-windows-x64, retest-windows-clang-x64, retest-macos-x64, retest-macos-gcc-x64, retest-linux-clang-x64, retest-linux-gcc-x64]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4.1.7

      - name: Install dependencies
        run: sudo apt-get update && sudo apt-get install -y tcl-dev tk-dev cmake gcc g++ make libbtbb-dev libx11-dev libglu1-mesa-dev tcllib tcl-thread tcl libvtk9-dev libopenvr-dev libdraco-dev libfreeimage-dev libegl1-mesa-dev libgles2-mesa-dev libfreetype-dev

      - name: Setup Xvfb and Mesa
        uses: ./.github/actions/setup-xvfb-mesa

      - name: Set environment variables
        run: |
          echo "DISPLAY=:99" >> $GITHUB_ENV
          echo "LIBGL_ALWAYS_SOFTWARE=1" >> $GITHUB_ENV

      - name: Download and extract install directory
        uses: actions/download-artifact@v4.1.7
        with:
          name: install-linux-gcc-x64
          path: install

      - name: Set execute permissions on DRAWEXE
        run: chmod +x install/bin/DRAWEXE

      - name: Get latest workflow run ID from target branch
        id: get_run_id
        run: |
          response=$(curl -s \
            -H "Accept: application/vnd.github.v3+json" \
            "https://api.github.com/repos/${{ github.repository }}/actions/runs?branch=${{ github.event.pull_request.base.ref }}&status=success")
          latest_run_id=$(echo "$response" | jq -r '.workflow_runs[] | select(.name=="Build and Test OCCT on Multiple Platforms") | .id' | head -n 1)
          echo "latest_run_id=$latest_run_id" >> $GITHUB_ENV

      - name: Download master branch test results
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          for platform in windows-x64 windows-clang-x64 macos-x64 macos-gcc-x64 linux-clang-x64 linux-gcc-x64; do
            echo "Downloading results for $platform"
            gh run download ${{ env.latest_run_id }} -n "results-$platform" -D "install/bin/results/master/"
          done

      - name: Download current branch test results
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          for platform in windows-x64 windows-clang-x64 macos-x64 macos-gcc-x64 linux-clang-x64 linux-gcc-x64; do
            echo "Downloading results for $platform"
            gh run download -n "results-$platform" -D "install/bin/results/current/"
          done

      - name: Compare test results
        run: |
          echo "Comparing test results..."
          cd install/bin
          source env.sh
          for platform in windows-x64 windows-clang-x64 macos-x64 macos-gcc-x64 linux-clang-x64 linux-gcc-x64; do
            ./DRAWEXE -v -c testdiff "results/current/$platform" "results/master/$platform" &
          done
          wait

      - name: Install BeautifulSoup
        run: pip install beautifulsoup4

      - name: Clean unused test images
        run: |
          # copy to the install/bin/results directory
          cp ${{ github.workspace }}/.github/actions/scripts/cleanup_test_images.py install/bin/results
          cd install/bin/results
          python cleanup_test_images.py

      - name: Upload comparison results
        uses: actions/upload-artifact@v4.4.3
        with:
          name: test-compare-results
          retention-days: 15
          overwrite: true
          path: |
            install/bin/results/**/diff-*.html
            install/bin/results/**/diff-*.log
            install/bin/results/**/summary.html
            install/bin/results/**/tests.log
            install/bin/results/**/*.png
