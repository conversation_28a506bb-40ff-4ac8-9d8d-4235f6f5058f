# Contributing to OCCT

## Contribute Code

In order to contribute code to OCCT, you must register on this [portal](https://dev.opencascade.org/) and sign the Contributor License Agreement (CLA).

- [Introduction: What is CLA and Why It Is Needed?](CLA_SIGNING.md)
- [Contribution License Agreement](CLA.md)
- [CLA Submission Form](https://dev.opencascade.org/get_involed/cla_submission_form)

### Steps to Submit Your Contribution
1. **Clone OCCT Git Repository**: See [Guide to installing and using Git for OCCT development](https://dev.opencascade.org/doc/overview/html/occt_contribution__git_guide.html) if you are not familiar with Git.
2. **Develop Your Change**: Ensure it complies with [OCCT Coding Rules](https://dev.opencascade.org/doc/overview/html/occt_contribution__coding_rules.html).
3. **Build and Verify**: Build the modified version of OCCT and verify it works as expected. Consider creating a test case.
4. **Register an Issue**: Register an issue in the [Mantis bug tracker](https://tracker.dev.opencascade.org/login_select_proj_page.php?ref=bug_report_page.php) or [GitHub issues](https://github.com/Open-Cascade-SAS/OCCT/issues) describing your change.
5. **Push Your Change**: Push your change to the Git repository in a branch with a name starting with "CR" followed by the issue ID, then switch the issue to Resolved.

The contribution then passes code review and testing; if everything is OK, it will be integrated into the master branch in about one week.

See [Contribution Workflow](https://dev.opencascade.org/doc/overview/html/occt_contribution__contribution_workflow.html) for other possibilities and details on how contributions are processed.

For more details on integration into GitHub, see the [GitHub Discussions Guide](https://github.com/Open-Cascade-SAS/OCCT/discussions/36).

## Contribute Ideas

Every big thing starts with an idea. We appreciate your vision on how to enhance Open CASCADE technology. Share your thoughts on the [OCCT product development forum](https://dev.opencascade.org/forums) or submit your meaningful ideas and bug reports via [Mantis tracker](https://tracker.dev.opencascade.org) or [GitHub issues](https://github.com/Open-Cascade-SAS/OCCT/issues).

- **Forum**: [OCCT product development forum](https://dev.opencascade.org/forums)
- **Reporting Issues**: [Mantis tracker](https://tracker.dev.opencascade.org) or [GitHub issues](https://github.com/Open-Cascade-SAS/OCCT/issues)

## Contribute Knowledge

Know a lot about OCCT? You can help educate other OCCT users by writing OCCT-related articles or blog posts, creating samples, examples, or tutorials, and even by writing a book about OCCT! If you would like us to share your content via official OCCT resources, please [contact us](https://dev.opencascade.org/webform/contact_us).

## Contribute Documentation and Tutorials

Do you have an idea on how to make OCCT Documentation easier for new users or even more exhaustive for professionals? Or want to help with proofreading and technical writing? Translating OCCT Documentation and materials into your native language is also very much appreciated. You are always welcome to submit your documentation improvement suggestions via [Mantis tracker](https://tracker.dev.opencascade.org) or [GitHub issues](https://github.com/Open-Cascade-SAS/OCCT/issues)..

## Contribute to the Community

At any community interaction points, we value your support in starting forum topics or replying to other users’ posts, joining Open CASCADE social networks, participating in GitHub or Stack Overflow projects, and just spreading the word about OCCT! Welcome to our community!