name: 'Build CSharp Sample'
description: 'Build CSharp sample using OCCT installation'

inputs:
  platform:
    description: 'Build platform (windows)'
    required: true
  install-artifact-name:
    description: 'OCCT installation artifact name'
    required: true

runs:
  using: "composite"
  steps:
    - name: Download OCCT installation
      uses: actions/download-artifact@v4.1.7
      with:
        name: ${{ inputs.install-artifact-name }}
        path: occt-install

    - name: Build CSharp Sample
      if: inputs.platform == 'windows'
      shell: cmd
      run: |
        REM Setup environment
        call "${{ github.workspace }}\occt-install\env.bat" vc14 win64 Release
        call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat" x64
        set "PATH=C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE;%PATH%"

        cd ${{ github.workspace }}/samples/CSharp

        REM First upgrade solutions to VS2022
        echo "Upgrading solution files to VS2022..."
        devenv.exe CSharp.sln /upgrade
        devenv.exe CSharp_D3D.sln /upgrade

        REM Update project platform toolset
        powershell -Command "(Get-Content OCCTProxy\OCCTProxy.vcxproj) -replace '<PlatformToolset>v100</PlatformToolset>', '<PlatformToolset>v143</PlatformToolset>' | Set-Content OCCTProxy\OCCTProxy.vcxproj"
        powershell -Command "(Get-Content OCCTProxy_D3D\OCCTProxy_D3D.vcxproj) -replace '<PlatformToolset>v100</PlatformToolset>', '<PlatformToolset>v143</PlatformToolset>' | Set-Content OCCTProxy_D3D\OCCTProxy_D3D.vcxproj"

        REM Restore NuGet packages
        echo "Upgrading solution files..."
        msbuild.exe CSharp.sln -t:Restore -p:Configuration=Release -p:Platform=x64 /consoleloggerparameters:Verbosity=normal;Summary /flp:LogFile=restore_csharp.log;Verbosity=detailed
        msbuild.exe CSharp_D3D.sln -t:Restore -p:Configuration=Release -p:Platform=x64 /consoleloggerparameters:Verbosity=normal;Summary /flp:LogFile=restore_d3d.log;Verbosity=detailed
        
        REM Build solutions with real-time console output
        echo "Building CSharp.sln..."
        msbuild.exe CSharp.sln /p:Configuration=Release /p:Platform=x64 /consoleloggerparameters:Verbosity=normal;Summary /flp:LogFile=build_csharp.log;Verbosity=detailed /m
        echo "Building CSharp_D3D.sln..."
        msbuild.exe CSharp_D3D.sln /p:Configuration=Release /p:Platform=x64 /consoleloggerparameters:Verbosity=normal;Summary /flp:LogFile=build_d3d.log;Verbosity=detailed /m

    - name: Upload CSharp Sample
      uses: actions/upload-artifact@v4.4.3
      with:
        name: csharp-sample-${{ inputs.platform }}-x64
        path: samples/CSharp
        retention-days: 7
