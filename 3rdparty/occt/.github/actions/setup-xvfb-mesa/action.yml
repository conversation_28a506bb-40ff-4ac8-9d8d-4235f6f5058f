name: 'Setup Xvfb and Mesa'
description: 'Installs and configures Xvfb and Mesa for graphical testing on Linux'

runs:
  using: composite
  steps:
    - name: Install Xvfb and Mesa
      run: |
        sudo apt-get update
        sudo apt-get install -y xvfb mesa-utils libgl1-mesa-dri
      shell: bash

    - name: Start Xvfb
      run: |
        Xvfb :99 -screen 0 1920x1080x24 &
        echo "DISPLAY=:99" >> $GITHUB_ENV
        echo "LIBGL_ALWAYS_SOFTWARE=1" >> $GITHUB_ENV
      shell: bash
