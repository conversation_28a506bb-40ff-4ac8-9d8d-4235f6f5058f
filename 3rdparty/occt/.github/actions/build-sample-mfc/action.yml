name: 'Build MFC Sample'
description: 'Build MFC sample using OCCT installation'

inputs:
  platform:
    description: 'Build platform (windows)'
    required: true
  install-artifact-name:
    description: 'OCCT installation artifact name'
    required: true

runs:
  using: "composite"
  steps:
    - name: Download OCCT installation 
      uses: actions/download-artifact@v4.1.7
      with:
        name: ${{ inputs.install-artifact-name }}
        path: occt-install

    - name: Build MFC Sample
      if: inputs.platform == 'windows'
      shell: cmd
      run: |
        REM Setup environment
        call "${{ github.workspace }}\occt-install\env.bat" vc14 win64 Release
        call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat" x64

        cd ${{ github.workspace }}/samples/mfc/standard
        set "PATH=C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE;%PATH%"
        
        REM First restore the solution
        echo "Restoring solution..."
        msbuild.exe ALL-vc14.sln -t:Restore -p:Configuration=Release -p:Platform=x64 /consoleloggerparameters:Verbosity=normal;Summary /flp:LogFile=restore.log;Verbosity=detailed
        
        REM Build solution with detailed logging
        echo "Building solution..."
        msbuild.exe ALL-vc14.sln /p:Configuration=Release /p:Platform=x64 /p:PlatformToolset=v143 /consoleloggerparameters:Verbosity=normal;Summary /flp:LogFile=build.log;Verbosity=detailed /m

        REM Display logs if build fails
        if errorlevel 1 (
          echo "Build failed. Contents of restore.log:"
          type restore.log
          echo "Contents of build.log:"
          type build.log
          exit /b 1
        )

    - name: Upload MFC Sample
      uses: actions/upload-artifact@v4.4.3
      with:
        name: mfc-sample-${{ inputs.platform }}-x64
        path: samples/mfc/
        retention-days: 7
