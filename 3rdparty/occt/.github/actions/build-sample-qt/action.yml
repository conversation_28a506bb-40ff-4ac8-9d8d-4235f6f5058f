name: 'Build Qt Sample'
description: 'Build Qt samples using OCCT installation'

inputs:
  platform:
    description: 'Build platform (windows/linux)'
    required: true
  install-artifact-name:
    description: 'OCCT installation artifact name'
    required: true
  thirdparty_url:
    description: 'URL to download 3rdparty dependencies'
    required: false
    default: 'https://github.com/Open-Cascade-SAS/OCCT/releases/download/V7_9_0_beta1/3rdparty-vc14-64.zip'

runs:
  using: "composite"
  steps:
    - name: Download OCCT installation
      uses: actions/download-artifact@v4.1.7
      with:
        name: ${{ inputs.install-artifact-name }}
        path: occt-install

    - name: Install Windows dependencies
      if: inputs.platform == 'windows'
      shell: pwsh
      run: |
          Invoke-WebRequest -Uri ${{ inputs.thirdparty_url }} -OutFile 3rdparty-vc14-64.zip
          Expand-Archive -Path 3rdparty-vc14-64.zip -DestinationPath .
          Remove-Item 3rdparty-vc14-64.zip

    - name: Install Linux dependencies
      if: inputs.platform == 'linux'
      shell: bash
      run: sudo apt-get update && sudo apt-get install -y tcl-dev tk-dev cmake gcc g++ make libbtbb-dev libx11-dev libglu1-mesa-dev tcllib tcl-thread tcl libvtk9-dev libopenvr-dev libdraco-dev libfreeimage-dev libegl1-mesa-dev libgles2-mesa-dev libfreetype-dev qtbase5-dev qt5-qmake qtbase5-dev-tools qtdeclarative5-dev qttools5-dev qttools5-dev-tools

    - name: Setup MSBuild
      if: inputs.platform == 'windows'
      uses: microsoft/setup-msbuild@v2

    - name: Build Qt Samples - Windows
      if: inputs.platform == 'windows'
      shell: cmd
      run: |
        REM Setup environment
        cd ${{ github.workspace }}/occt-install/
        call env.bat vc14 win64 Release

        call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat" x64

        REM Setup Qt environment
        set "QTDIR=${{ github.workspace }}\3rdparty-vc14-64\qt5.11.2-vc14-64"
        set "PATH=%QTDIR%\bin;C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE;%PATH%"

        cd ${{ github.workspace }}/samples/qt

        for %%s in (IESample Tutorial FuncDemo) do (
          cd %%s
          echo "Generating project for %%s..."
          qmake -tp vc -r -o %%s.sln %%s0.pro

          echo "Restoring %%s..."
          msbuild.exe %%s.sln -t:Restore -p:Configuration=Release -p:Platform=x64 /consoleloggerparameters:Verbosity=normal;Summary /flp:LogFile=%%s_restore.log;Verbosity=detailed
          
          echo "Building %%s..."
          msbuild.exe %%s.sln /p:Configuration=Release /p:Platform=x64 /p:PlatformToolset=v143 /consoleloggerparameters:Verbosity=normal;Summary /flp:LogFile=%%s_build.log;Verbosity=detailed /m
          
          REM Display logs if build fails
          if errorlevel 1 (
            echo "Build failed for %%s. Contents of restore log:"
            type %%s_restore.log
            echo "Contents of build log:"
            type %%s_build.log
            exit /b 1
          )
          
          cd ..
        )

    - name: Build Qt Samples - Linux
      if: inputs.platform == 'linux'
      shell: bash
      run: |
        cd ${{ github.workspace }}/occt-install/bin
        source env.sh
        cd ${{ github.workspace }}/samples/qt
        
        for sample in IESample Tutorial FuncDemo; do
          cd $sample
          aQMakePath=`which qmake`
          host=`uname -s`
          export STATION=$host
          export RES_DIR="${{ github.workspace }}/samples/qt/${sample}/result"
          qmake $sample.pro
          aNbJobs="$(getconf _NPROCESSORS_ONLN)"
          make -j$aNbJobs release
          cd ..
        done

    - name: Upload Qt Samples
      uses: actions/upload-artifact@v4.4.3
      with:
        name: qt-samples-${{ inputs.platform }}-x64
        path: |
          samples/qt/
          samples/qt/
        retention-days: 7
