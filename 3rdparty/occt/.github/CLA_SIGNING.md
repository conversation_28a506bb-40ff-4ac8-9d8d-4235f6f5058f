# Signing the Contributor License Agreement

## In order to contribute code to OCCT, you (or the legal entity you represent) must sign the Contributor License Agreement (CLA).

### What is the Contributor License Agreement and why is it necessary to sign it?
The Contributor License Agreement is needed to clarify the terms of usage of contributions by OPEN CASCADE SAS and the entire open source community.

The Contributor License Agreement clarifies the intellectual property rights applicable to contributions. It confirms that the contributor retains ownership of his contributions (the OPEN CASCADE company does not require exclusive copyright transfer for these contributions) and gives all rights to OPEN CASCADE to use the contribution. In turn, OPEN CASCADE guarantees the open source availability of all integrated contributions.

### How to sign the Contributor License Agreement
Accepting and signing the Contributor License Agreement is a prerequisite for contributing code. To sign the CLA, please register yourself first, and then follow the steps below:
1. Select the "Get Involved" menu
2. Download the Contribution License Agreement pdf file and print it
3. Read the agreement and sign it
4. Scan the signed CLA to a pdf file
5. If not yet done, log on to this website
6. In the CLA sending form, press the "Browse" button
7. Select your signed CLA file
8. Press "Send"

We will need some time to approve the CLA that you have submitted. You will be notified by e-mail when your CLA is approved and you are assigned the Contributor role.

### Q&A on the Contribution Agreement

**I want to contribute code that I developed as part of my job. Can I contribute this code?**

Yes, but as (under applicable legislation) your employer typically will own all intellectual property that you create as part of your employment, it is important that your employer has consented to your participation and contributions in the OCCT Project. That provides clarity to all parties involved.

**Can we sign a CLA to enable contributions from several employees in our company?**

Yes, if your company has several employees ready to contribute, a person representing your company can sign the CLA whereby your employer can specify and manage who is able to contribute from your company.

**I want to notify the OCCT Project of a bug I found. Can I report this bug?**

Yes, you simply need to register (you then become a Member) for entering a bug. The bug can be entered using our bug tracker from the Get Involved page. But please note that as a Member of this project, you have accepted the Terms and Conditions of this website, so the materials you provide must conform to these Terms.

**Can I terminate my Contributor License Agreement?**

Yes, you may terminate your Contributor License Agreement by sending a written notice through the contact form of this portal. The termination of your CLA will become effective not later than 5 days from the notice date. Please note, however, that all contributions you might have made to the OCCT Project before your CLA termination remain covered by that CLA.