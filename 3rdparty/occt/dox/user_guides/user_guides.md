User Guides {#user_guides}
===========

OCCT User Guides are organized by OCCT modules:

* @subpage occt_user_guides__foundation_classes "Foundation Classes"
* @subpage occt_user_guides__modeling_data "Modeling Data"
* @subpage occt_user_guides__modeling_algos "Modeling Algorithms"
* @subpage occt_user_guides__mesh "Mesh"
* @subpage occt_user_guides__shape_healing "Shape Healing"
* @subpage occt_user_guides__visualization "Visualization"
* @subpage occt_user_guides__vis "VTK Integration Services"
* @subpage occt_user_guides__iges "IGES Translator"
* @subpage occt_user_guides__step "STEP Translator"
* @subpage occt_user_guides__xde "Extended Data Exchange (XDE)"
* @subpage occt_user_guides__de_wrapper "Data Exchange Wrapper (DE Wrapper)"
* @subpage occt_user_guides__ocaf "Open CASCADE Application Framework (OCAF)"
* @subpage occt_user_guides__test_harness "DRAW Test Harness"
* @subpage occt_user_guides__inspector "Inspector"
