<?xml version="1.0" encoding="UTF-8"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" stroke-dasharray="none" shape-rendering="auto" font-family="'Dialog'" width="1514" text-rendering="auto" fill-opacity="1" contentScriptType="text/ecmascript" color-interpolation="auto" color-rendering="auto" preserveAspectRatio="xMidYMid meet" font-size="12" viewBox="0 0 1514 1571" fill="black" stroke="black" image-rendering="auto" stroke-miterlimit="10" zoomAndPan="magnify" version="1.0" stroke-linecap="square" stroke-linejoin="miter" contentStyleType="text/css" font-style="normal" height="1571" stroke-width="1" stroke-dashoffset="0" font-weight="normal" stroke-opacity="1">
<!--Generated by the Batik Graphics2D SVG Generator-->
<defs id="genericDefs"/>
<g>
<defs id="defs1">
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath1">
<path d="M1296 189 L1505 189 L1505 291 L1296 291 L1296 189 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath2">
<path d="M1296 189 L1503 189 L1503 289 L1296 289 L1296 189 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath3">
<path d="M1307 194 L1492 194 L1492 212 L1307 212 L1307 194 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath4">
<path d="M1307 194 L1495 194 L1495 212 L1307 212 L1307 194 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath5">
<path d="M1301 217 L1497 217 L1497 245 L1301 245 L1301 217 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath6">
<path d="M1301 217 L1499 217 L1499 245 L1301 245 L1301 217 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath7">
<path d="M1297 215 L1502 215 L1502 288 L1297 288 L1297 215 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath8">
<path d="M1296 345 L1505 345 L1505 434 L1296 434 L1296 345 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath9">
<path d="M1296 345 L1503 345 L1503 432 L1296 432 L1296 345 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath10">
<path d="M1308 350 L1490 350 L1490 368 L1308 368 L1308 350 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath11">
<path d="M1308 350 L1493 350 L1493 368 L1308 368 L1308 350 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath12">
<path d="M1297 371 L1502 371 L1502 431 L1297 431 L1297 371 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath13">
<path d="M12 165 L1168 165 L1168 1562 L12 1562 L12 165 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath14">
<path d="M12 165 L1166 165 L1166 1560 L12 1560 L12 165 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath15">
<path d="M13 170 L135 170 L135 188 L13 188 L13 170 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath16">
<path d="M13 170 L137 170 L137 188 L13 188 L13 170 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath17">
<path d="M291 189 L523 189 L523 291 L291 291 L291 189 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath18">
<path d="M291 189 L521 189 L521 289 L291 289 L291 189 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath19">
<path d="M334 194 L478 194 L478 212 L334 212 L334 194 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath20">
<path d="M334 194 L481 194 L481 212 L334 212 L334 194 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath21">
<path d="M296 217 L458 217 L458 245 L296 245 L296 217 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath22">
<path d="M296 217 L460 217 L460 245 L296 245 L296 217 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath23">
<path d="M292 215 L520 215 L520 288 L292 288 L292 215 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath24">
<path d="M446 465 L739 465 L739 567 L446 567 L446 465 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath25">
<path d="M446 465 L737 465 L737 565 L446 565 L446 465 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath26">
<path d="M482 470 L700 470 L700 488 L482 488 L482 470 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath27">
<path d="M482 470 L703 470 L703 488 L482 488 L482 470 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath28">
<path d="M447 491 L736 491 L736 564 L447 564 L447 491 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath29">
<path d="M444 777 L737 777 L737 879 L444 879 L444 777 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath30">
<path d="M444 777 L735 777 L735 877 L444 877 L444 777 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath31">
<path d="M452 782 L727 782 L727 814 L452 814 L452 782 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath32">
<path d="M452 782 L730 782 L730 814 L452 814 L452 782 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath33">
<path d="M445 817 L734 817 L734 876 L445 876 L445 817 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath34">
<path d="M792 777 L1097 777 L1097 879 L792 879 L792 777 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath35">
<path d="M792 777 L1095 777 L1095 877 L792 877 L792 777 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath36">
<path d="M796 782 L1091 782 L1091 814 L796 814 L796 782 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath37">
<path d="M796 782 L1094 782 L1094 814 L796 814 L796 782 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath38">
<path d="M793 817 L1094 817 L1094 876 L793 876 L793 817 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath39">
<path d="M74 465 L367 465 L367 567 L74 567 L74 465 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath40">
<path d="M74 465 L365 465 L365 565 L74 565 L74 465 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath41">
<path d="M118 470 L320 470 L320 488 L118 488 L118 470 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath42">
<path d="M118 470 L323 470 L323 488 L118 488 L118 470 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath43">
<path d="M75 491 L364 491 L364 564 L75 564 L75 491 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath44">
<path d="M72 777 L365 777 L365 879 L72 879 L72 777 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath45">
<path d="M72 777 L363 777 L363 877 L72 877 L72 777 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath46">
<path d="M75 782 L360 782 L360 814 L75 814 L75 782 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath47">
<path d="M75 782 L363 782 L363 814 L75 814 L75 782 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath48">
<path d="M73 817 L362 817 L362 876 L73 876 L73 817 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath49">
<path d="M36 944 L1145 944 L1145 1538 L36 1538 L36 944 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath50">
<path d="M36 944 L1143 944 L1143 1536 L36 1536 L36 944 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath51">
<path d="M37 949 L135 949 L135 967 L37 967 L37 949 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath52">
<path d="M37 949 L137 949 L137 967 L37 967 L37 949 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath53">
<path d="M492 980 L713 980 L713 1082 L492 1082 L492 980 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath54">
<path d="M492 980 L711 980 L711 1080 L492 1080 L492 980 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath55">
<path d="M499 985 L704 985 L704 1003 L499 1003 L499 985 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath56">
<path d="M499 985 L707 985 L707 1003 L499 1003 L499 985 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath57">
<path d="M493 1006 L710 1006 L710 1079 L493 1079 L493 1006 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath58">
<path d="M60 1124 L281 1124 L281 1226 L60 1226 L60 1124 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath59">
<path d="M60 1124 L279 1124 L279 1224 L60 1224 L60 1124 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath60">
<path d="M74 1129 L265 1129 L265 1147 L74 1147 L74 1129 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath61">
<path d="M74 1129 L268 1129 L268 1147 L74 1147 L74 1129 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath62">
<path d="M61 1150 L278 1150 L278 1223 L61 1223 L61 1150 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath63">
<path d="M300 1124 L521 1124 L521 1226 L300 1226 L300 1124 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath64">
<path d="M300 1124 L519 1124 L519 1224 L300 1224 L300 1124 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath65">
<path d="M305 1129 L513 1129 L513 1147 L305 1147 L305 1129 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath66">
<path d="M305 1129 L516 1129 L516 1147 L305 1147 L305 1129 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath67">
<path d="M301 1150 L518 1150 L518 1223 L301 1223 L301 1150 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath68">
<path d="M535 1124 L756 1124 L756 1226 L535 1226 L535 1124 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath69">
<path d="M535 1124 L754 1124 L754 1224 L535 1224 L535 1124 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath70">
<path d="M543 1129 L746 1129 L746 1147 L543 1147 L543 1129 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath71">
<path d="M543 1129 L749 1129 L749 1147 L543 1147 L543 1129 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath72">
<path d="M536 1150 L753 1150 L753 1223 L536 1223 L536 1150 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath73">
<path d="M775 1124 L995 1124 L995 1226 L775 1226 L775 1124 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath74">
<path d="M775 1124 L993 1124 L993 1224 L775 1224 L775 1124 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath75">
<path d="M776 1129 L991 1129 L991 1147 L776 1147 L776 1129 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath76">
<path d="M776 1129 L994 1129 L994 1147 L776 1147 L776 1129 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath77">
<path d="M776 1150 L992 1150 L992 1223 L776 1223 L776 1150 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath78">
<path d="M649 1280 L869 1280 L869 1382 L649 1382 L649 1280 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath79">
<path d="M649 1280 L867 1280 L867 1380 L649 1380 L649 1280 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath80">
<path d="M661 1285 L855 1285 L855 1303 L661 1303 L661 1285 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath81">
<path d="M661 1285 L858 1285 L858 1303 L661 1303 L661 1285 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath82">
<path d="M650 1306 L866 1306 L866 1379 L650 1379 L650 1306 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath83">
<path d="M900 1280 L1119 1280 L1119 1382 L900 1382 L900 1280 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath84">
<path d="M900 1280 L1117 1280 L1117 1380 L900 1380 L900 1280 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath85">
<path d="M906 1285 L1110 1285 L1110 1303 L906 1303 L906 1285 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath86">
<path d="M906 1285 L1113 1285 L1113 1303 L906 1303 L906 1285 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath87">
<path d="M901 1306 L1116 1306 L1116 1379 L901 1379 L901 1306 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath88">
<path d="M852 1413 L1119 1413 L1119 1515 L852 1515 L852 1413 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath89">
<path d="M852 1413 L1117 1413 L1117 1513 L852 1513 L852 1413 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath90">
<path d="M856 1418 L1112 1418 L1112 1436 L856 1436 L856 1418 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath91">
<path d="M856 1418 L1115 1418 L1115 1436 L856 1436 L856 1418 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath92">
<path d="M853 1439 L1116 1439 L1116 1512 L853 1512 L853 1439 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath93">
<path d="M290 321 L523 321 L523 423 L290 423 L290 321 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath94">
<path d="M290 321 L521 321 L521 421 L290 421 L290 321 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath95">
<path d="M322 326 L489 326 L489 344 L322 344 L322 326 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath96">
<path d="M322 326 L492 326 L492 344 L322 344 L322 326 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath97">
<path d="M291 347 L520 347 L520 420 L291 420 L291 347 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath98">
<path d="M822 321 L1067 321 L1067 423 L822 423 L822 321 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath99">
<path d="M822 321 L1065 321 L1065 421 L822 421 L822 321 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath100">
<path d="M834 326 L1053 326 L1053 344 L834 344 L834 326 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath101">
<path d="M834 326 L1056 326 L1056 344 L834 344 L834 326 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath102">
<path d="M823 347 L1064 347 L1064 420 L823 420 L823 347 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath103">
<path d="M276 621 L538 621 L538 723 L276 723 L276 621 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath104">
<path d="M276 621 L536 621 L536 721 L276 721 L276 621 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath105">
<path d="M294 626 L518 626 L518 658 L294 658 L294 626 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath106">
<path d="M294 626 L521 626 L521 658 L294 658 L294 626 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath107">
<path d="M294 626 L520 626 L520 658 L294 658 L294 626 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath108">
<path d="M277 661 L535 661 L535 720 L277 720 L277 661 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath109">
<path d="M792 9 L986 9 L986 111 L792 111 L792 9 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath110">
<path d="M792 9 L984 9 L984 109 L792 109 L792 9 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath111">
<path d="M812 14 L963 14 L963 32 L812 32 L812 14 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath112">
<path d="M812 14 L966 14 L966 32 L812 32 L812 14 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath113">
<path d="M793 35 L983 35 L983 108 L793 108 L793 35 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath114">
<path d="M-1 -1 L1515 -1 L1515 1572 L-1 1572 L-1 -1 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath115">
<path d="M22 597 L365 597 L365 615 L22 615 L22 597 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath116">
<path d="M22 597 L367 597 L367 615 L22 615 L22 597 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath117">
<path d="M366 616 L377 616 L377 624 L366 624 L366 616 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath118">
<path d="M828 908 L1001 908 L1001 926 L828 926 L828 908 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath119">
<path d="M828 908 L1003 908 L1003 926 L828 926 L828 908 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath120">
<path d="M904 875 L915 875 L915 883 L904 883 L904 875 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath121">
<path d="M597 1078 L608 1078 L608 1089 L597 1089 L597 1078 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath122">
<path d="M400 419 L411 419 L411 430 L400 430 L400 419 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath123">
<path d="M880 1222 L891 1222 L891 1233 L880 1233 L880 1222 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath124">
<path d="M127 902 L300 902 L300 920 L127 920 L127 902 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath125">
<path d="M127 902 L302 902 L302 920 L127 920 L127 902 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath126">
<path d="M252 875 L263 875 L263 883 L252 883 L252 875 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath127">
<path d="M434 597 L794 597 L794 615 L434 615 L434 597 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath128">
<path d="M434 597 L796 597 L796 615 L434 615 L434 597 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath129">
<path d="M446 616 L457 616 L457 624 L446 624 L446 616 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath130">
<path d="M1004 1378 L1015 1378 L1015 1389 L1004 1389 L1004 1378 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath131">
<path d="M733 823 L743 823 L743 832 L733 832 L733 823 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath132">
<path d="M402 719 L413 719 L413 730 L402 730 L402 719 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath133">
<path d="M408 896 L581 896 L581 914 L408 914 L408 896 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath134">
<path d="M408 896 L583 896 L583 914 L408 914 L408 896 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath135">
<path d="M586 875 L595 875 L595 885 L586 885 L586 875 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath136">
<path d="M666 359 L721 359 L721 374 L666 374 L666 359 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath137">
<path d="M815 372 L825 372 L825 381 L815 381 L815 372 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath138">
<path d="M401 287 L412 287 L412 298 L401 298 L401 287 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath139">
<path d="M1394 287 L1405 287 L1405 298 L1394 298 L1394 287 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath140">
<path d="M1164 394 L1174 394 L1174 403 L1164 403 L1164 394 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath141">
<path d="M579 123 L634 123 L634 138 L579 138 L579 123 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath142">
<path d="M408 185 L418 185 L418 194 L408 194 L408 185 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath143">
<path d="M1140 129 L1195 129 L1195 144 L1140 144 L1140 129 Z"/>
</clipPath>
<clipPath clipPathUnits="userSpaceOnUse" id="clipPath144">
<path d="M1385 185 L1395 185 L1395 194 L1385 194 L1385 185 Z"/>
</clipPath>
</defs>
<g fill="rgb(131,122,133)" font-family="'Segoe UI'" stroke-linejoin="round" stroke="rgb(131,122,133)" font-weight="bold" stroke-width="0" stroke-miterlimit="0">
<rect x="1298" y="191" clip-path="url(#clipPath1)" width="205" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="1299" y="192" clip-path="url(#clipPath1)" width="205" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="1297" y="190" clip-path="url(#clipPath2)" fill="white" width="205" rx="2.5" ry="2.5" height="98" stroke="none"/>
</g>
<g stroke-linecap="butt" fill="rgb(173,127,168)" font-family="'Segoe UI'" stroke="rgb(173,127,168)" font-weight="bold" stroke-width="1.1">
<rect x="1297" y="190" clip-path="url(#clipPath2)" fill="none" width="204" rx="2.5" ry="2.5" height="97"/>
<image x="1308" y="195" clip-path="url(#clipPath3)" width="16" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABHElEQVR42mNkgIKC&#13;&#10;BUf+P3jyCchiBOL/QMgIYTFCREDg/38GOFCU4WWYkGDDyAITuP/4E8OkNDsGYkHu&#13;&#10;rINgGm7Av7//GNjZmMHsEzceMVhoyDFYVK6EazjRHg4Xh2iAOIcJ2VQWZiawooKF&#13;&#10;x8HsM12RYHEQDeKDxEHyIDYMoBnACFZ0oTcKzAZhmDgIg8QhhjNiN4AVaPKEeEsG&#13;&#10;g+JlYDYr1CYYGyQOkmfF6gJgELMCTfYyUYEqYmTQLlgClgLRID5IHCQPYv+DhgEL&#13;&#10;Qv8/BmYmiNN8zVTB9N2pcSghDxOHqEczAAT8GjaB08F/NBIGECkEyGZkxDRgaYUX&#13;&#10;0ekgpnM7ZiCSA1iwmUq2AUvKPQfQCypKwiR5AaSeKgAABcZdRqE6GaQAAAAASUVO&#13;&#10;RK5CYII=" xlink:type="simple" xlink:actuate="onLoad" height="16" preserveAspectRatio="none" xlink:show="embed"/>
<text x="1327" y="208" clip-path="url(#clipPath4)" fill="black" stroke="none" xml:space="preserve">IMeshTools_MeshAlgoFactory</text>
</g>
<g stroke-width="1.1" font-size="11" font-family="'Segoe UI'" stroke-linecap="butt">
<image x="1302" y="223" clip-path="url(#clipPath5)" width="16" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAACM0lEQVR42sVTX0hT&#13;&#10;URz+7p3OCIqkaMLq0R6CCIRMaCMyr9ushkT17Fq9RNk/iJBk5z5s+BAhBQYh6Hyp&#13;&#10;B5W0NufcbCuNFcToIQrxz5g0IsVE5u52d+89HScMYw56CPo9nHN+/L7z8fGd7wD/&#13;&#10;u3TlBgIJ0nmlmljb3GQ2OiCWw/FbmyYxQGG+SzfOE+HXePfkNgLhwc3hiZvUSsK0&#13;&#10;rAKLK0gnwq8w+fA6vF950v/gCnZWVaGxoQ4jC3oy+agdnU/7IVzuIvNRb1ERV5Ri&#13;&#10;aqejXTcASsFxKjRaCbaggtfYtAIKVIbWcO5+DzD1mCtRQJMfxBczHGk1NSCfB3yx&#13;&#10;OGYWF/ElkcLBmhrICnCxswd0y+UigcUVonPqHuK+5kClTofI5zhjVNDr/4RjtUbM&#13;&#10;/kjhQLUBx48cRWhpB2l2uslcZNPYgonjzDDP1TZsqFxeyUHTFPSNxRH1nMez8Rh4&#13;&#10;lcP31RwUlcLjdCAYfPnnK9iEs+joHUBiOYOVjASOee04U4dTHUNw2uqh8TqsrctI&#13;&#10;/FpnuD4IFjtKTCwUM9JxoRW8wnjlFGOn0BiE0xug6vTwDg5Bm+7mtg+S+Ra1t7RA&#13;&#10;zgGSKiNHdyPNVbJ9FyRNRUZWcfhQLb7l9xEkY2JJkMwnGzHq92E1l8XImA8/M2ms&#13;&#10;ScCSlMWwP4h0VsJwwI/TTbbtg5R8+1ysv3SPTE9FCk+VpPvJXqMRHyOsf9/NLagG&#13;&#10;Igh2hFwC91efxOp6Q2G6Q5vF0vj+0/oNRdDxkTDUjqQAAAAASUVORK5CYII=" xlink:type="simple" xlink:actuate="onLoad" height="16" preserveAspectRatio="none" xlink:show="embed"/>
<text xml:space="preserve" x="1321" y="230" clip-path="url(#clipPath6)" stroke="none">GetAlgo(GeomAbs_SurfaceType : , </text>
<text xml:space="preserve" x="1321" y="243" clip-path="url(#clipPath6)" stroke="none">IMeshTools_Parameters : )</text>
</g>
<g stroke-linecap="butt" fill="rgb(173,127,168)" font-family="'Segoe UI'" stroke="rgb(173,127,168)" font-weight="bold" stroke-width="1.1">
<line clip-path="url(#clipPath7)" fill="none" x1="1298" x2="1501" y1="216" y2="216"/>
<rect x="1298" y="347" clip-path="url(#clipPath8)" fill="rgb(131,122,133)" width="205" rx="2.5" opacity="0.2549" ry="2.5" height="85" stroke="none"/>
<rect x="1299" y="348" clip-path="url(#clipPath8)" fill="rgb(131,122,133)" width="205" rx="2.5" opacity="0.2549" ry="2.5" height="85" stroke="none"/>
<rect x="1297" y="346" clip-path="url(#clipPath9)" fill="white" width="205" rx="2.5" ry="2.5" height="85" stroke="none"/>
<rect x="1297" y="346" clip-path="url(#clipPath9)" fill="none" width="204" rx="2.5" ry="2.5" height="84" stroke="rgb(39,76,114)"/>
<image stroke="rgb(39,76,114)" width="16" xlink:show="embed" xlink:type="simple" fill="rgb(39,76,114)" clip-path="url(#clipPath10)" preserveAspectRatio="none" height="16" x="1309" y="351" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAr0lEQVR42mNkgIKC&#13;&#10;BUf+P3jyCchiBOL/QMgIYTFCREDg/38GOFCU4WWYkGDDyAITuP/4E8OkNDsGYkHu&#13;&#10;rINgGm7Av7//GNjZmIk2gOHff1QDwBxmJgZSAZoBjJQZwEqRC4BBzEqCC/6hh8H/&#13;&#10;//8YmJmIN+D/fyyB6NewCZwO/qORMIBIIUA2IyOmAUsrvIh2QUzndjBNeqjhiwWY&#13;&#10;qWQbsKTccwC9oKIkTJIXQOqpAgBzFjJ6SnHW0gAAAABJRU5ErkJggg==" xlink:actuate="onLoad"/>
<text x="1328" y="364" clip-path="url(#clipPath11)" fill="black" stroke="none" xml:space="preserve">BRepMesh_MeshAlgoFactory</text>
<line clip-path="url(#clipPath12)" fill="none" x1="1298" x2="1501" y1="372" y2="372" stroke="rgb(39,76,114)"/>
<rect x="14" y="167" clip-path="url(#clipPath13)" fill="rgb(131,122,133)" width="1152" rx="2.5" opacity="0.2549" ry="2.5" height="1393" stroke="none"/>
<rect x="15" y="168" clip-path="url(#clipPath13)" fill="rgb(131,122,133)" width="1152" rx="2.5" opacity="0.2549" ry="2.5" height="1393" stroke="none"/>
<rect x="13" y="166" clip-path="url(#clipPath14)" fill="white" width="1152" rx="2.5" ry="2.5" height="1393" stroke="none"/>
<rect x="13" y="166" clip-path="url(#clipPath14)" fill="none" width="1151" rx="2.5" ry="2.5" height="1392" stroke="rgb(39,76,114)"/>
<image stroke="rgb(39,76,114)" width="16" xlink:show="embed" xlink:type="simple" fill="rgb(39,76,114)" clip-path="url(#clipPath15)" preserveAspectRatio="none" height="16" x="14" y="171" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAWUlEQVR42mNgGPKA&#13;&#10;EUQoZCz4j0vBgxkJjPgMYIEx7k+Px5BUzFxIuQvwAZDr4C7YUBdAkuaApg2oXgAB&#13;&#10;MV5+kl3BRGksoLiAnZlCA5gZKTWAiQIDYKE6AgEAAu0P6M4KKrgAAAAASUVORK5C&#13;&#10;YII=" xlink:actuate="onLoad"/>
<text x="33" y="184" clip-path="url(#clipPath16)" fill="black" stroke="none" xml:space="preserve">Triangulation Algo</text>
<rect x="293" y="191" clip-path="url(#clipPath17)" fill="rgb(131,122,133)" width="228" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="294" y="192" clip-path="url(#clipPath17)" fill="rgb(131,122,133)" width="228" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="292" y="190" clip-path="url(#clipPath18)" fill="white" width="228" rx="2.5" ry="2.5" height="98" stroke="none"/>
<rect x="292" y="190" clip-path="url(#clipPath18)" fill="none" width="227" rx="2.5" ry="2.5" height="97"/>
<image x="335" y="195" clip-path="url(#clipPath19)" width="16" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABHElEQVR42mNkgIKC&#13;&#10;BUf+P3jyCchiBOL/QMgIYTFCREDg/38GOFCU4WWYkGDDyAITuP/4E8OkNDsGYkHu&#13;&#10;rINgGm7Av7//GNjZmMHsEzceMVhoyDFYVK6EazjRHg4Xh2iAOIcJ2VQWZiawooKF&#13;&#10;x8HsM12RYHEQDeKDxEHyIDYMoBnACFZ0oTcKzAZhmDgIg8QhhjNiN4AVaPKEeEsG&#13;&#10;g+JlYDYr1CYYGyQOkmfF6gJgELMCTfYyUYEqYmTQLlgClgLRID5IHCQPYv+DhgEL&#13;&#10;Qv8/BmYmiNN8zVTB9N2pcSghDxOHqEczAAT8GjaB08F/NBIGECkEyGZkxDRgaYUX&#13;&#10;0ekgpnM7ZiCSA1iwmUq2AUvKPQfQCypKwiR5AaSeKgAABcZdRqE6GaQAAAAASUVO&#13;&#10;RK5CYII=" xlink:type="simple" xlink:actuate="onLoad" height="16" preserveAspectRatio="none" xlink:show="embed"/>
<text x="354" y="208" clip-path="url(#clipPath20)" fill="black" stroke="none" xml:space="preserve">IMeshTools_MeshAlgo</text>
</g>
<g stroke-width="1.1" font-size="11" font-family="'Segoe UI'" stroke-linecap="butt">
<image x="297" y="223" clip-path="url(#clipPath21)" width="16" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAACM0lEQVR42sVTX0hT&#13;&#10;URz+7p3OCIqkaMLq0R6CCIRMaCMyr9ushkT17Fq9RNk/iJBk5z5s+BAhBQYh6Hyp&#13;&#10;B5W0NufcbCuNFcToIQrxz5g0IsVE5u52d+89HScMYw56CPo9nHN+/L7z8fGd7wD/&#13;&#10;u3TlBgIJ0nmlmljb3GQ2OiCWw/FbmyYxQGG+SzfOE+HXePfkNgLhwc3hiZvUSsK0&#13;&#10;rAKLK0gnwq8w+fA6vF950v/gCnZWVaGxoQ4jC3oy+agdnU/7IVzuIvNRb1ERV5Ri&#13;&#10;aqejXTcASsFxKjRaCbaggtfYtAIKVIbWcO5+DzD1mCtRQJMfxBczHGk1NSCfB3yx&#13;&#10;OGYWF/ElkcLBmhrICnCxswd0y+UigcUVonPqHuK+5kClTofI5zhjVNDr/4RjtUbM&#13;&#10;/kjhQLUBx48cRWhpB2l2uslcZNPYgonjzDDP1TZsqFxeyUHTFPSNxRH1nMez8Rh4&#13;&#10;lcP31RwUlcLjdCAYfPnnK9iEs+joHUBiOYOVjASOee04U4dTHUNw2uqh8TqsrctI&#13;&#10;/FpnuD4IFjtKTCwUM9JxoRW8wnjlFGOn0BiE0xug6vTwDg5Bm+7mtg+S+Ra1t7RA&#13;&#10;zgGSKiNHdyPNVbJ9FyRNRUZWcfhQLb7l9xEkY2JJkMwnGzHq92E1l8XImA8/M2ms&#13;&#10;ScCSlMWwP4h0VsJwwI/TTbbtg5R8+1ysv3SPTE9FCk+VpPvJXqMRHyOsf9/NLagG&#13;&#10;Igh2hFwC91efxOp6Q2G6Q5vF0vj+0/oNRdDxkTDUjqQAAAAASUVORK5CYII=" xlink:type="simple" xlink:actuate="onLoad" height="16" preserveAspectRatio="none" xlink:show="embed"/>
<text xml:space="preserve" x="316" y="230" clip-path="url(#clipPath22)" stroke="none">Perform(IMeshData_Face : , </text>
<text xml:space="preserve" x="316" y="243" clip-path="url(#clipPath22)" stroke="none">IMeshTools_Parameters : )</text>
</g>
<g stroke-linecap="butt" fill="rgb(173,127,168)" font-family="'Segoe UI'" stroke="rgb(173,127,168)" font-weight="bold" stroke-width="1.1">
<line clip-path="url(#clipPath23)" fill="none" x1="293" x2="519" y1="216" y2="216"/>
<rect x="448" y="467" clip-path="url(#clipPath24)" fill="rgb(131,122,133)" width="289" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="449" y="468" clip-path="url(#clipPath24)" fill="rgb(131,122,133)" width="289" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="447" y="466" clip-path="url(#clipPath25)" fill="white" width="289" rx="2.5" ry="2.5" height="98" stroke="none"/>
<rect x="447" y="466" clip-path="url(#clipPath25)" fill="none" width="288" rx="2.5" ry="2.5" height="97" stroke="rgb(39,76,114)"/>
<image stroke="rgb(39,76,114)" width="16" xlink:show="embed" xlink:type="simple" fill="rgb(39,76,114)" clip-path="url(#clipPath26)" preserveAspectRatio="none" height="16" x="483" y="471" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAr0lEQVR42mNkgIKC&#13;&#10;BUf+P3jyCchiBOL/QMgIYTFCREDg/38GOFCU4WWYkGDDyAITuP/4E8OkNDsGYkHu&#13;&#10;rINgGm7Av7//GNjZmIk2gOHff1QDwBxmJgZSAZoBjJQZwEqRC4BBzEqCC/6hh8H/&#13;&#10;//8YmJmIN+D/fyyB6NewCZwO/qORMIBIIUA2IyOmAUsrvIh2QUzndjBNeqjhiwWY&#13;&#10;qWQbsKTccwC9oKIkTJIXQOqpAgBzFjJ6SnHW0gAAAABJRU5ErkJggg==" xlink:actuate="onLoad"/>
<text x="502" y="484" clip-path="url(#clipPath27)" fill="black" stroke="none" xml:space="preserve">BRepMesh_DelaunayBaseMeshAlgo</text>
<line clip-path="url(#clipPath28)" fill="none" x1="448" x2="735" y1="492" y2="492" stroke="rgb(39,76,114)"/>
<rect x="446" y="779" clip-path="url(#clipPath29)" fill="rgb(131,122,133)" width="289" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="447" y="780" clip-path="url(#clipPath29)" fill="rgb(131,122,133)" width="289" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="445" y="778" clip-path="url(#clipPath30)" fill="white" width="289" rx="2.5" ry="2.5" height="98" stroke="none"/>
<rect x="445" y="778" clip-path="url(#clipPath30)" fill="none" width="288" rx="2.5" ry="2.5" height="97" stroke="rgb(39,76,114)"/>
<image stroke="rgb(39,76,114)" width="16" xlink:show="embed" xlink:type="simple" fill="rgb(39,76,114)" clip-path="url(#clipPath31)" preserveAspectRatio="none" height="16" x="453" y="790" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAr0lEQVR42mNkgIKC&#13;&#10;BUf+P3jyCchiBOL/QMgIYTFCREDg/38GOFCU4WWYkGDDyAITuP/4E8OkNDsGYkHu&#13;&#10;rINgGm7Av7//GNjZmIk2gOHff1QDwBxmJgZSAZoBjJQZwEqRC4BBzEqCC/6hh8H/&#13;&#10;//8YmJmIN+D/fyyB6NewCZwO/qORMIBIIUA2IyOmAUsrvIh2QUzndjBNeqjhiwWY&#13;&#10;qWQbsKTccwC9oKIkTJIXQOqpAgBzFjJ6SnHW0gAAAABJRU5ErkJggg==" xlink:actuate="onLoad"/>
<text x="472" y="796" clip-path="url(#clipPath32)" fill="black" stroke="none" xml:space="preserve">BRepMesh_DelaunayNodeInsertionMeshAlgo </text>
<text x="472" y="811" clip-path="url(#clipPath32)" fill="black" stroke="none" xml:space="preserve">&lt;RangeSplitter&gt;</text>
<line clip-path="url(#clipPath33)" fill="none" x1="446" x2="733" y1="818" y2="818" stroke="rgb(39,76,114)"/>
<rect x="794" y="779" clip-path="url(#clipPath34)" fill="rgb(131,122,133)" width="301" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="795" y="780" clip-path="url(#clipPath34)" fill="rgb(131,122,133)" width="301" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="793" y="778" clip-path="url(#clipPath35)" fill="white" width="301" rx="2.5" ry="2.5" height="98" stroke="none"/>
<rect x="793" y="778" clip-path="url(#clipPath35)" fill="none" width="300" rx="2.5" ry="2.5" height="97" stroke="rgb(39,76,114)"/>
<image stroke="rgb(39,76,114)" width="16" xlink:show="embed" xlink:type="simple" fill="rgb(39,76,114)" clip-path="url(#clipPath36)" preserveAspectRatio="none" height="16" x="797" y="790" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAr0lEQVR42mNkgIKC&#13;&#10;BUf+P3jyCchiBOL/QMgIYTFCREDg/38GOFCU4WWYkGDDyAITuP/4E8OkNDsGYkHu&#13;&#10;rINgGm7Av7//GNjZmIk2gOHff1QDwBxmJgZSAZoBjJQZwEqRC4BBzEqCC/6hh8H/&#13;&#10;//8YmJmIN+D/fyyB6NewCZwO/qORMIBIIUA2IyOmAUsrvIh2QUzndjBNeqjhiwWY&#13;&#10;qWQbsKTccwC9oKIkTJIXQOqpAgBzFjJ6SnHW0gAAAABJRU5ErkJggg==" xlink:actuate="onLoad"/>
<text x="816" y="796" clip-path="url(#clipPath37)" fill="black" stroke="none" xml:space="preserve">BRepMesh_DelaunayDeflectionControlMeshAlgo </text>
<text x="816" y="811" clip-path="url(#clipPath37)" fill="black" stroke="none" xml:space="preserve">&lt;RangeSplitter&gt;</text>
<line clip-path="url(#clipPath38)" fill="none" x1="794" x2="1093" y1="818" y2="818" stroke="rgb(39,76,114)"/>
<rect x="76" y="467" clip-path="url(#clipPath39)" fill="rgb(131,122,133)" width="289" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="77" y="468" clip-path="url(#clipPath39)" fill="rgb(131,122,133)" width="289" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="75" y="466" clip-path="url(#clipPath40)" fill="white" width="289" rx="2.5" ry="2.5" height="98" stroke="none"/>
<rect x="75" y="466" clip-path="url(#clipPath40)" fill="none" width="288" rx="2.5" ry="2.5" height="97" stroke="rgb(39,76,114)"/>
<image stroke="rgb(39,76,114)" width="16" xlink:show="embed" xlink:type="simple" fill="rgb(39,76,114)" clip-path="url(#clipPath41)" preserveAspectRatio="none" height="16" x="119" y="471" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAr0lEQVR42mNkgIKC&#13;&#10;BUf+P3jyCchiBOL/QMgIYTFCREDg/38GOFCU4WWYkGDDyAITuP/4E8OkNDsGYkHu&#13;&#10;rINgGm7Av7//GNjZmIk2gOHff1QDwBxmJgZSAZoBjJQZwEqRC4BBzEqCC/6hh8H/&#13;&#10;//8YmJmIN+D/fyyB6NewCZwO/qORMIBIIUA2IyOmAUsrvIh2QUzndjBNeqjhiwWY&#13;&#10;qWQbsKTccwC9oKIkTJIXQOqpAgBzFjJ6SnHW0gAAAABJRU5ErkJggg==" xlink:actuate="onLoad"/>
<text x="138" y="484" clip-path="url(#clipPath42)" fill="black" stroke="none" xml:space="preserve">BRepMesh_SweepLineMeshAlgo</text>
<line clip-path="url(#clipPath43)" fill="none" x1="76" x2="363" y1="492" y2="492" stroke="rgb(39,76,114)"/>
<rect x="74" y="779" clip-path="url(#clipPath44)" fill="rgb(131,122,133)" width="289" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="75" y="780" clip-path="url(#clipPath44)" fill="rgb(131,122,133)" width="289" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="73" y="778" clip-path="url(#clipPath45)" fill="white" width="289" rx="2.5" ry="2.5" height="98" stroke="none"/>
<rect x="73" y="778" clip-path="url(#clipPath45)" fill="none" width="288" rx="2.5" ry="2.5" height="97" stroke="rgb(39,76,114)"/>
<image stroke="rgb(39,76,114)" width="16" xlink:show="embed" xlink:type="simple" fill="rgb(39,76,114)" clip-path="url(#clipPath46)" preserveAspectRatio="none" height="16" x="76" y="790" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAr0lEQVR42mNkgIKC&#13;&#10;BUf+P3jyCchiBOL/QMgIYTFCREDg/38GOFCU4WWYkGDDyAITuP/4E8OkNDsGYkHu&#13;&#10;rINgGm7Av7//GNjZmIk2gOHff1QDwBxmJgZSAZoBjJQZwEqRC4BBzEqCC/6hh8H/&#13;&#10;//8YmJmIN+D/fyyB6NewCZwO/qORMIBIIUA2IyOmAUsrvIh2QUzndjBNeqjhiwWY&#13;&#10;qWQbsKTccwC9oKIkTJIXQOqpAgBzFjJ6SnHW0gAAAABJRU5ErkJggg==" xlink:actuate="onLoad"/>
<text x="95" y="796" clip-path="url(#clipPath47)" fill="black" stroke="none" xml:space="preserve">BRepMesh_SweepLineNodeInsertionMeshAlgo </text>
<text x="95" y="811" clip-path="url(#clipPath47)" fill="black" stroke="none" xml:space="preserve">&lt;RangeSplitter&gt;</text>
<line clip-path="url(#clipPath48)" fill="none" x1="74" x2="361" y1="818" y2="818" stroke="rgb(39,76,114)"/>
<rect x="38" y="946" clip-path="url(#clipPath49)" fill="rgb(131,122,133)" width="1105" rx="2.5" opacity="0.2549" ry="2.5" height="590" stroke="none"/>
<rect x="39" y="947" clip-path="url(#clipPath49)" fill="rgb(131,122,133)" width="1105" rx="2.5" opacity="0.2549" ry="2.5" height="590" stroke="none"/>
<rect x="37" y="945" clip-path="url(#clipPath50)" fill="white" width="1105" rx="2.5" ry="2.5" height="590" stroke="none"/>
<rect x="37" y="945" clip-path="url(#clipPath50)" fill="none" width="1104" rx="2.5" ry="2.5" height="589" stroke="rgb(39,76,114)"/>
<image stroke="rgb(39,76,114)" width="16" xlink:show="embed" xlink:type="simple" fill="rgb(39,76,114)" clip-path="url(#clipPath51)" preserveAspectRatio="none" height="16" x="38" y="950" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAWUlEQVR42mNgGPKA&#13;&#10;EUQoZCz4j0vBgxkJjPgMYIEx7k+Px5BUzFxIuQvwAZDr4C7YUBdAkuaApg2oXgAB&#13;&#10;MV5+kl3BRGksoLiAnZlCA5gZKTWAiQIDYKE6AgEAAu0P6M4KKrgAAAAASUVORK5C&#13;&#10;YII=" xlink:actuate="onLoad"/>
<text x="57" y="963" clip-path="url(#clipPath52)" fill="black" stroke="none" xml:space="preserve">RangeSplitter</text>
<rect x="494" y="982" clip-path="url(#clipPath53)" fill="rgb(131,122,133)" width="217" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="495" y="983" clip-path="url(#clipPath53)" fill="rgb(131,122,133)" width="217" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="493" y="981" clip-path="url(#clipPath54)" fill="white" width="217" rx="2.5" ry="2.5" height="98" stroke="none"/>
<rect x="493" y="981" clip-path="url(#clipPath54)" fill="none" width="216" rx="2.5" ry="2.5" height="97" stroke="rgb(39,76,114)"/>
<image stroke="rgb(39,76,114)" width="16" xlink:show="embed" xlink:type="simple" fill="rgb(39,76,114)" clip-path="url(#clipPath55)" preserveAspectRatio="none" height="16" x="500" y="986" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAr0lEQVR42mNkgIKC&#13;&#10;BUf+P3jyCchiBOL/QMgIYTFCREDg/38GOFCU4WWYkGDDyAITuP/4E8OkNDsGYkHu&#13;&#10;rINgGm7Av7//GNjZmIk2gOHff1QDwBxmJgZSAZoBjJQZwEqRC4BBzEqCC/6hh8H/&#13;&#10;//8YmJmIN+D/fyyB6NewCZwO/qORMIBIIUA2IyOmAUsrvIh2QUzndjBNeqjhiwWY&#13;&#10;qWQbsKTccwC9oKIkTJIXQOqpAgBzFjJ6SnHW0gAAAABJRU5ErkJggg==" xlink:actuate="onLoad"/>
<text x="519" y="999" clip-path="url(#clipPath56)" fill="black" stroke="none" xml:space="preserve">BRepMesh_DefaultRangeSplitter</text>
<line clip-path="url(#clipPath57)" fill="none" x1="494" x2="709" y1="1007" y2="1007" stroke="rgb(39,76,114)"/>
<rect x="62" y="1126" clip-path="url(#clipPath58)" fill="rgb(131,122,133)" width="217" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="63" y="1127" clip-path="url(#clipPath58)" fill="rgb(131,122,133)" width="217" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="61" y="1125" clip-path="url(#clipPath59)" fill="white" width="217" rx="2.5" ry="2.5" height="98" stroke="none"/>
<rect x="61" y="1125" clip-path="url(#clipPath59)" fill="none" width="216" rx="2.5" ry="2.5" height="97" stroke="rgb(39,76,114)"/>
<image stroke="rgb(39,76,114)" width="16" xlink:show="embed" xlink:type="simple" fill="rgb(39,76,114)" clip-path="url(#clipPath60)" preserveAspectRatio="none" height="16" x="75" y="1130" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAr0lEQVR42mNkgIKC&#13;&#10;BUf+P3jyCchiBOL/QMgIYTFCREDg/38GOFCU4WWYkGDDyAITuP/4E8OkNDsGYkHu&#13;&#10;rINgGm7Av7//GNjZmIk2gOHff1QDwBxmJgZSAZoBjJQZwEqRC4BBzEqCC/6hh8H/&#13;&#10;//8YmJmIN+D/fyyB6NewCZwO/qORMIBIIUA2IyOmAUsrvIh2QUzndjBNeqjhiwWY&#13;&#10;qWQbsKTccwC9oKIkTJIXQOqpAgBzFjJ6SnHW0gAAAABJRU5ErkJggg==" xlink:actuate="onLoad"/>
<text x="94" y="1143" clip-path="url(#clipPath61)" fill="black" stroke="none" xml:space="preserve">BRepMesh_ConeRangeSplitter</text>
<line clip-path="url(#clipPath62)" fill="none" x1="62" x2="277" y1="1151" y2="1151" stroke="rgb(39,76,114)"/>
<rect x="302" y="1126" clip-path="url(#clipPath63)" fill="rgb(131,122,133)" width="217" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="303" y="1127" clip-path="url(#clipPath63)" fill="rgb(131,122,133)" width="217" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="301" y="1125" clip-path="url(#clipPath64)" fill="white" width="217" rx="2.5" ry="2.5" height="98" stroke="none"/>
<rect x="301" y="1125" clip-path="url(#clipPath64)" fill="none" width="216" rx="2.5" ry="2.5" height="97" stroke="rgb(39,76,114)"/>
<image stroke="rgb(39,76,114)" width="16" xlink:show="embed" xlink:type="simple" fill="rgb(39,76,114)" clip-path="url(#clipPath65)" preserveAspectRatio="none" height="16" x="306" y="1130" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAr0lEQVR42mNkgIKC&#13;&#10;BUf+P3jyCchiBOL/QMgIYTFCREDg/38GOFCU4WWYkGDDyAITuP/4E8OkNDsGYkHu&#13;&#10;rINgGm7Av7//GNjZmIk2gOHff1QDwBxmJgZSAZoBjJQZwEqRC4BBzEqCC/6hh8H/&#13;&#10;//8YmJmIN+D/fyyB6NewCZwO/qORMIBIIUA2IyOmAUsrvIh2QUzndjBNeqjhiwWY&#13;&#10;qWQbsKTccwC9oKIkTJIXQOqpAgBzFjJ6SnHW0gAAAABJRU5ErkJggg==" xlink:actuate="onLoad"/>
<text x="325" y="1143" clip-path="url(#clipPath66)" fill="black" stroke="none" xml:space="preserve">BRepMesh_CylinderRangeSplitter</text>
<line clip-path="url(#clipPath67)" fill="none" x1="302" x2="517" y1="1151" y2="1151" stroke="rgb(39,76,114)"/>
<rect x="537" y="1126" clip-path="url(#clipPath68)" fill="rgb(131,122,133)" width="217" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="538" y="1127" clip-path="url(#clipPath68)" fill="rgb(131,122,133)" width="217" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="536" y="1125" clip-path="url(#clipPath69)" fill="white" width="217" rx="2.5" ry="2.5" height="98" stroke="none"/>
<rect x="536" y="1125" clip-path="url(#clipPath69)" fill="none" width="216" rx="2.5" ry="2.5" height="97" stroke="rgb(39,76,114)"/>
<image stroke="rgb(39,76,114)" width="16" xlink:show="embed" xlink:type="simple" fill="rgb(39,76,114)" clip-path="url(#clipPath70)" preserveAspectRatio="none" height="16" x="544" y="1130" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAr0lEQVR42mNkgIKC&#13;&#10;BUf+P3jyCchiBOL/QMgIYTFCREDg/38GOFCU4WWYkGDDyAITuP/4E8OkNDsGYkHu&#13;&#10;rINgGm7Av7//GNjZmIk2gOHff1QDwBxmJgZSAZoBjJQZwEqRC4BBzEqCC/6hh8H/&#13;&#10;//8YmJmIN+D/fyyB6NewCZwO/qORMIBIIUA2IyOmAUsrvIh2QUzndjBNeqjhiwWY&#13;&#10;qWQbsKTccwC9oKIkTJIXQOqpAgBzFjJ6SnHW0gAAAABJRU5ErkJggg==" xlink:actuate="onLoad"/>
<text x="563" y="1143" clip-path="url(#clipPath71)" fill="black" stroke="none" xml:space="preserve">BRepMesh_SphereRangeSplitter</text>
<line clip-path="url(#clipPath72)" fill="none" x1="537" x2="752" y1="1151" y2="1151" stroke="rgb(39,76,114)"/>
<rect x="777" y="1126" clip-path="url(#clipPath73)" fill="rgb(131,122,133)" width="216" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="778" y="1127" clip-path="url(#clipPath73)" fill="rgb(131,122,133)" width="216" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="776" y="1125" clip-path="url(#clipPath74)" fill="white" width="216" rx="2.5" ry="2.5" height="98" stroke="none"/>
<rect x="776" y="1125" clip-path="url(#clipPath74)" fill="none" width="215" rx="2.5" ry="2.5" height="97" stroke="rgb(39,76,114)"/>
<image stroke="rgb(39,76,114)" width="16" xlink:show="embed" xlink:type="simple" fill="rgb(39,76,114)" clip-path="url(#clipPath75)" preserveAspectRatio="none" height="16" x="777" y="1130" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAr0lEQVR42mNkgIKC&#13;&#10;BUf+P3jyCchiBOL/QMgIYTFCREDg/38GOFCU4WWYkGDDyAITuP/4E8OkNDsGYkHu&#13;&#10;rINgGm7Av7//GNjZmIk2gOHff1QDwBxmJgZSAZoBjJQZwEqRC4BBzEqCC/6hh8H/&#13;&#10;//8YmJmIN+D/fyyB6NewCZwO/qORMIBIIUA2IyOmAUsrvIh2QUzndjBNeqjhiwWY&#13;&#10;qWQbsKTccwC9oKIkTJIXQOqpAgBzFjJ6SnHW0gAAAABJRU5ErkJggg==" xlink:actuate="onLoad"/>
<text x="796" y="1143" clip-path="url(#clipPath76)" fill="black" stroke="none" xml:space="preserve">BRepMesh_UVParamRangeSplitter</text>
<line clip-path="url(#clipPath77)" fill="none" x1="777" x2="991" y1="1151" y2="1151" stroke="rgb(39,76,114)"/>
<rect x="651" y="1282" clip-path="url(#clipPath78)" fill="rgb(131,122,133)" width="216" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="652" y="1283" clip-path="url(#clipPath78)" fill="rgb(131,122,133)" width="216" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="650" y="1281" clip-path="url(#clipPath79)" fill="white" width="216" rx="2.5" ry="2.5" height="98" stroke="none"/>
<rect x="650" y="1281" clip-path="url(#clipPath79)" fill="none" width="215" rx="2.5" ry="2.5" height="97" stroke="rgb(39,76,114)"/>
<image stroke="rgb(39,76,114)" width="16" xlink:show="embed" xlink:type="simple" fill="rgb(39,76,114)" clip-path="url(#clipPath80)" preserveAspectRatio="none" height="16" x="662" y="1286" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAr0lEQVR42mNkgIKC&#13;&#10;BUf+P3jyCchiBOL/QMgIYTFCREDg/38GOFCU4WWYkGDDyAITuP/4E8OkNDsGYkHu&#13;&#10;rINgGm7Av7//GNjZmIk2gOHff1QDwBxmJgZSAZoBjJQZwEqRC4BBzEqCC/6hh8H/&#13;&#10;//8YmJmIN+D/fyyB6NewCZwO/qORMIBIIUA2IyOmAUsrvIh2QUzndjBNeqjhiwWY&#13;&#10;qWQbsKTccwC9oKIkTJIXQOqpAgBzFjJ6SnHW0gAAAABJRU5ErkJggg==" xlink:actuate="onLoad"/>
<text x="681" y="1299" clip-path="url(#clipPath81)" fill="black" stroke="none" xml:space="preserve">BRepMesh_TorusRangeSplitter</text>
<line clip-path="url(#clipPath82)" fill="none" x1="651" x2="865" y1="1307" y2="1307" stroke="rgb(39,76,114)"/>
<rect x="902" y="1282" clip-path="url(#clipPath83)" fill="rgb(131,122,133)" width="215" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="903" y="1283" clip-path="url(#clipPath83)" fill="rgb(131,122,133)" width="215" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="901" y="1281" clip-path="url(#clipPath84)" fill="white" width="215" rx="2.5" ry="2.5" height="98" stroke="none"/>
<rect x="901" y="1281" clip-path="url(#clipPath84)" fill="none" width="214" rx="2.5" ry="2.5" height="97" stroke="rgb(39,76,114)"/>
<image stroke="rgb(39,76,114)" width="16" xlink:show="embed" xlink:type="simple" fill="rgb(39,76,114)" clip-path="url(#clipPath85)" preserveAspectRatio="none" height="16" x="907" y="1286" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAr0lEQVR42mNkgIKC&#13;&#10;BUf+P3jyCchiBOL/QMgIYTFCREDg/38GOFCU4WWYkGDDyAITuP/4E8OkNDsGYkHu&#13;&#10;rINgGm7Av7//GNjZmIk2gOHff1QDwBxmJgZSAZoBjJQZwEqRC4BBzEqCC/6hh8H/&#13;&#10;//8YmJmIN+D/fyyB6NewCZwO/qORMIBIIUA2IyOmAUsrvIh2QUzndjBNeqjhiwWY&#13;&#10;qWQbsKTccwC9oKIkTJIXQOqpAgBzFjJ6SnHW0gAAAABJRU5ErkJggg==" xlink:actuate="onLoad"/>
<text x="926" y="1299" clip-path="url(#clipPath86)" fill="black" stroke="none" xml:space="preserve">BRepMesh_NURBSRangeSplitter</text>
<line clip-path="url(#clipPath87)" fill="none" x1="902" x2="1115" y1="1307" y2="1307" stroke="rgb(39,76,114)"/>
<rect x="854" y="1415" clip-path="url(#clipPath88)" fill="rgb(131,122,133)" width="263" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="855" y="1416" clip-path="url(#clipPath88)" fill="rgb(131,122,133)" width="263" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="853" y="1414" clip-path="url(#clipPath89)" fill="white" width="263" rx="2.5" ry="2.5" height="98" stroke="none"/>
<rect x="853" y="1414" clip-path="url(#clipPath89)" fill="none" width="262" rx="2.5" ry="2.5" height="97" stroke="rgb(39,76,114)"/>
<image stroke="rgb(39,76,114)" width="16" xlink:show="embed" xlink:type="simple" fill="rgb(39,76,114)" clip-path="url(#clipPath90)" preserveAspectRatio="none" height="16" x="857" y="1419" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAr0lEQVR42mNkgIKC&#13;&#10;BUf+P3jyCchiBOL/QMgIYTFCREDg/38GOFCU4WWYkGDDyAITuP/4E8OkNDsGYkHu&#13;&#10;rINgGm7Av7//GNjZmIk2gOHff1QDwBxmJgZSAZoBjJQZwEqRC4BBzEqCC/6hh8H/&#13;&#10;//8YmJmIN+D/fyyB6NewCZwO/qORMIBIIUA2IyOmAUsrvIh2QUzndjBNeqjhiwWY&#13;&#10;qWQbsKTccwC9oKIkTJIXQOqpAgBzFjJ6SnHW0gAAAABJRU5ErkJggg==" xlink:actuate="onLoad"/>
<text x="876" y="1432" clip-path="url(#clipPath91)" fill="black" stroke="none" xml:space="preserve">BRepMesh_BoundaryParamsRangeSplitter</text>
<line clip-path="url(#clipPath92)" fill="none" x1="854" x2="1115" y1="1440" y2="1440" stroke="rgb(39,76,114)"/>
<line clip-path="url(#clipPath49)" fill="none" x1="38" x2="134" y1="971" y2="971" stroke="rgb(39,76,114)"/>
<line clip-path="url(#clipPath49)" fill="none" x1="134" x2="134" y1="971" y2="971" stroke="rgb(39,76,114)"/>
<line clip-path="url(#clipPath49)" fill="none" x1="134" x2="139" y1="971" y2="966" stroke="rgb(39,76,114)"/>
<line clip-path="url(#clipPath49)" fill="none" x1="139" x2="139" y1="966" y2="966" stroke="rgb(39,76,114)"/>
<line clip-path="url(#clipPath49)" fill="none" x1="139" x2="139" y1="966" y2="945" stroke="rgb(39,76,114)"/>
<rect x="292" y="323" clip-path="url(#clipPath93)" fill="rgb(131,122,133)" width="229" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="293" y="324" clip-path="url(#clipPath93)" fill="rgb(131,122,133)" width="229" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="291" y="322" clip-path="url(#clipPath94)" fill="white" width="229" rx="2.5" ry="2.5" height="98" stroke="none"/>
<rect x="291" y="322" clip-path="url(#clipPath94)" fill="none" width="228" rx="2.5" ry="2.5" height="97" stroke="rgb(39,76,114)"/>
<image stroke="rgb(39,76,114)" width="16" xlink:show="embed" xlink:type="simple" fill="rgb(39,76,114)" clip-path="url(#clipPath95)" preserveAspectRatio="none" height="16" x="323" y="327" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAr0lEQVR42mNkgIKC&#13;&#10;BUf+P3jyCchiBOL/QMgIYTFCREDg/38GOFCU4WWYkGDDyAITuP/4E8OkNDsGYkHu&#13;&#10;rINgGm7Av7//GNjZmIk2gOHff1QDwBxmJgZSAZoBjJQZwEqRC4BBzEqCC/6hh8H/&#13;&#10;//8YmJmIN+D/fyyB6NewCZwO/qORMIBIIUA2IyOmAUsrvIh2QUzndjBNeqjhiwWY&#13;&#10;qWQbsKTccwC9oKIkTJIXQOqpAgBzFjJ6SnHW0gAAAABJRU5ErkJggg==" xlink:actuate="onLoad"/>
<text x="342" y="340" clip-path="url(#clipPath96)" fill="black" stroke="none" xml:space="preserve">BRepMesh_BaseMeshAlgo</text>
<line clip-path="url(#clipPath97)" fill="none" x1="292" x2="519" y1="348" y2="348" stroke="rgb(39,76,114)"/>
<rect x="824" y="323" clip-path="url(#clipPath98)" fill="rgb(131,122,133)" width="241" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="825" y="324" clip-path="url(#clipPath98)" fill="rgb(131,122,133)" width="241" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="823" y="322" clip-path="url(#clipPath99)" fill="white" width="241" rx="2.5" ry="2.5" height="98" stroke="none"/>
<rect x="823" y="322" clip-path="url(#clipPath99)" fill="none" width="240" rx="2.5" ry="2.5" height="97" stroke="rgb(39,76,114)"/>
<image stroke="rgb(39,76,114)" width="16" xlink:show="embed" xlink:type="simple" fill="rgb(39,76,114)" clip-path="url(#clipPath100)" preserveAspectRatio="none" height="16" x="835" y="327" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAr0lEQVR42mNkgIKC&#13;&#10;BUf+P3jyCchiBOL/QMgIYTFCREDg/38GOFCU4WWYkGDDyAITuP/4E8OkNDsGYkHu&#13;&#10;rINgGm7Av7//GNjZmIk2gOHff1QDwBxmJgZSAZoBjJQZwEqRC4BBzEqCC/6hh8H/&#13;&#10;//8YmJmIN+D/fyyB6NewCZwO/qORMIBIIUA2IyOmAUsrvIh2QUzndjBNeqjhiwWY&#13;&#10;qWQbsKTccwC9oKIkTJIXQOqpAgBzFjJ6SnHW0gAAAABJRU5ErkJggg==" xlink:actuate="onLoad"/>
<text x="854" y="340" clip-path="url(#clipPath101)" fill="black" stroke="none" xml:space="preserve">BRepMesh_DataStructureOfDelaun</text>
<line clip-path="url(#clipPath102)" fill="none" x1="824" x2="1063" y1="348" y2="348" stroke="rgb(39,76,114)"/>
<rect x="278" y="623" clip-path="url(#clipPath103)" fill="rgb(131,122,133)" width="258" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="279" y="624" clip-path="url(#clipPath103)" fill="rgb(131,122,133)" width="258" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="277" y="622" clip-path="url(#clipPath104)" fill="white" width="258" rx="2.5" ry="2.5" height="98" stroke="none"/>
<rect x="277" y="622" clip-path="url(#clipPath104)" fill="none" width="257" rx="2.5" ry="2.5" height="97" stroke="rgb(39,76,114)"/>
<image stroke="rgb(39,76,114)" width="16" xlink:show="embed" xlink:type="simple" fill="rgb(39,76,114)" clip-path="url(#clipPath105)" preserveAspectRatio="none" height="16" x="295" y="634" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAr0lEQVR42mNkgIKC&#13;&#10;BUf+P3jyCchiBOL/QMgIYTFCREDg/38GOFCU4WWYkGDDyAITuP/4E8OkNDsGYkHu&#13;&#10;rINgGm7Av7//GNjZmIk2gOHff1QDwBxmJgZSAZoBjJQZwEqRC4BBzEqCC/6hh8H/&#13;&#10;//8YmJmIN+D/fyyB6NewCZwO/qORMIBIIUA2IyOmAUsrvIh2QUzndjBNeqjhiwWY&#13;&#10;qWQbsKTccwC9oKIkTJIXQOqpAgBzFjJ6SnHW0gAAAABJRU5ErkJggg==" xlink:actuate="onLoad"/>
<text x="314" y="640" clip-path="url(#clipPath106)" fill="black" stroke="none" xml:space="preserve">BRepMesh_NodeInsertionMeshAlgo </text>
<text x="314" y="655" clip-path="url(#clipPath107)" fill="black" stroke="none" xml:space="preserve">&lt;RangeSplitter, BaseClass&gt;</text>
<line clip-path="url(#clipPath108)" fill="none" x1="278" x2="534" y1="662" y2="662" stroke="rgb(39,76,114)"/>
<line clip-path="url(#clipPath13)" fill="none" x1="14" x2="134" y1="192" y2="192" stroke="rgb(39,76,114)"/>
<line clip-path="url(#clipPath13)" fill="none" x1="134" x2="134" y1="192" y2="192" stroke="rgb(39,76,114)"/>
<line clip-path="url(#clipPath13)" fill="none" x1="134" x2="139" y1="192" y2="187" stroke="rgb(39,76,114)"/>
<line clip-path="url(#clipPath13)" fill="none" x1="139" x2="139" y1="187" y2="187" stroke="rgb(39,76,114)"/>
<line clip-path="url(#clipPath13)" fill="none" x1="139" x2="139" y1="187" y2="166" stroke="rgb(39,76,114)"/>
<rect x="794" y="11" clip-path="url(#clipPath109)" fill="rgb(131,122,133)" width="190" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="795" y="12" clip-path="url(#clipPath109)" fill="rgb(131,122,133)" width="190" rx="2.5" opacity="0.2549" ry="2.5" height="98" stroke="none"/>
<rect x="793" y="10" clip-path="url(#clipPath110)" fill="white" width="190" rx="2.5" ry="2.5" height="98" stroke="none"/>
<rect x="793" y="10" clip-path="url(#clipPath110)" fill="none" width="189" rx="2.5" ry="2.5" height="97" stroke="rgb(39,76,114)"/>
<image stroke="rgb(39,76,114)" width="16" xlink:show="embed" xlink:type="simple" fill="rgb(39,76,114)" clip-path="url(#clipPath111)" preserveAspectRatio="none" height="16" x="813" y="15" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAr0lEQVR42mNkgIKC&#13;&#10;BUf+P3jyCchiBOL/QMgIYTFCREDg/38GOFCU4WWYkGDDyAITuP/4E8OkNDsGYkHu&#13;&#10;rINgGm7Av7//GNjZmIk2gOHff1QDwBxmJgZSAZoBjJQZwEqRC4BBzEqCC/6hh8H/&#13;&#10;//8YmJmIN+D/fyyB6NewCZwO/qORMIBIIUA2IyOmAUsrvIh2QUzndjBNeqjhiwWY&#13;&#10;qWQbsKTccwC9oKIkTJIXQOqpAgBzFjJ6SnHW0gAAAABJRU5ErkJggg==" xlink:actuate="onLoad"/>
<text x="832" y="28" clip-path="url(#clipPath112)" fill="black" stroke="none" xml:space="preserve">BRepMesh_FaceDiscret</text>
<line clip-path="url(#clipPath113)" fill="none" x1="794" x2="982" y1="36" y2="36" stroke="rgb(39,76,114)"/>
</g>
<g stroke-linecap="butt" font-size="11" fill="rgb(69,69,69)" font-family="'Segoe UI'" stroke-dasharray="5,5" stroke="rgb(69,69,69)" stroke-width="1.1">
<line clip-path="url(#clipPath114)" fill="none" x1="219" x2="375" y1="564" y2="622"/>
<image x="23" y="598" clip-path="url(#clipPath115)" width="16" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAhklEQVR42mNkIALo&#13;&#10;ebX+R+Zf2lbNCGMzEWMASENTbQhWOaIM2HD85v+65jUMCbF2DGSDvuWH/2PzDkGb&#13;&#10;CWlgoouzSQLEOBunF2jmbJCL8LoKl7NB4shRB6Nh4mAv4HI2TBzZFbAkDBJHNgTD&#13;&#10;2eguQncBjI1iCD5vYaPxasYWBsi2E6WZWAAAvauBMFaKJxwAAAAASUVORK5CYII=" xlink:type="simple" xlink:actuate="onLoad" height="16" preserveAspectRatio="none" xlink:show="embed"/>
<text x="42" y="611" clip-path="url(#clipPath116)" fill="black" stroke="none" xml:space="preserve">RangeSplitter-&gt;T, BaseClass-&gt;BRepMesh_SweepLineMeshAlgo</text>
<line clip-path="url(#clipPath117)" fill="none" x1="367" x2="375" y1="622" y2="622" stroke-dasharray="none"/>
<line clip-path="url(#clipPath117)" fill="none" x1="375" x2="369" y1="622" y2="617" stroke-dasharray="none"/>
<line clip-path="url(#clipPath114)" fill="none" x1="661" x2="913" y1="981" y2="876"/>
<image x="829" y="909" clip-path="url(#clipPath118)" width="16" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAhklEQVR42mNkIALo&#13;&#10;ebX+R+Zf2lbNCGMzEWMASENTbQhWOaIM2HD85v+65jUMCbF2DGSDvuWH/2PzDkGb&#13;&#10;CWlgoouzSQLEOBunF2jmbJCL8LoKl7NB4shRB6Nh4mAv4HI2TBzZFbAkDBJHNgTD&#13;&#10;2eguQncBjI1iCD5vYaPxasYWBsi2E6WZWAAAvauBMFaKJxwAAAAASUVORK5CYII=" xlink:type="simple" xlink:actuate="onLoad" height="16" preserveAspectRatio="none" xlink:show="embed"/>
<text x="848" y="922" clip-path="url(#clipPath119)" fill="black" stroke="none" xml:space="preserve">RangeSplitter-&gt;RangeSplitter</text>
<line clip-path="url(#clipPath120)" fill="none" x1="908" x2="913" y1="881" y2="876" stroke-dasharray="none"/>
<line clip-path="url(#clipPath120)" fill="none" x1="913" x2="905" y1="876" y2="876" stroke-dasharray="none"/>
<line clip-path="url(#clipPath114)" fill="none" x1="672" x2="672" y1="1125" y2="1097" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="672" x2="602" y1="1097" y2="1097" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="602" x2="602" y1="1097" y2="1079" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<polygon fill="white" stroke-dasharray="none" clip-path="url(#clipPath121)" points=" 602 1079 606 1087 598 1087" stroke="none"/>
<polygon fill="none" stroke-dasharray="none" clip-path="url(#clipPath121)" points=" 602 1079 606 1087 598 1087" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="602" x2="602" y1="466" y2="438" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="602" x2="405" y1="438" y2="438" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="405" x2="405" y1="438" y2="420" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<polygon fill="white" stroke-dasharray="none" clip-path="url(#clipPath122)" points=" 405 420 409 428 401 428" stroke="none"/>
<polygon fill="none" stroke-dasharray="none" clip-path="url(#clipPath122)" points=" 405 420 409 428 401 428" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="757" x2="757" y1="1281" y2="1241" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="757" x2="885" y1="1241" y2="1241" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="885" x2="885" y1="1241" y2="1223" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<polygon fill="white" stroke-dasharray="none" clip-path="url(#clipPath123)" points=" 885 1223 889 1231 881 1231" stroke="none"/>
<polygon fill="none" stroke-dasharray="none" clip-path="url(#clipPath123)" points=" 885 1223 889 1231 881 1231" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="538" x2="253" y1="981" y2="876"/>
<image x="128" y="903" clip-path="url(#clipPath124)" width="16" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAhklEQVR42mNkIALo&#13;&#10;ebX+R+Zf2lbNCGMzEWMASENTbQhWOaIM2HD85v+65jUMCbF2DGSDvuWH/2PzDkGb&#13;&#10;CWlgoouzSQLEOBunF2jmbJCL8LoKl7NB4shRB6Nh4mAv4HI2TBzZFbAkDBJHNgTD&#13;&#10;2eguQncBjI1iCD5vYaPxasYWBsi2E6WZWAAAvauBMFaKJxwAAAAASUVORK5CYII=" xlink:type="simple" xlink:actuate="onLoad" height="16" preserveAspectRatio="none" xlink:show="embed"/>
<text x="147" y="916" clip-path="url(#clipPath125)" fill="black" stroke="none" xml:space="preserve">RangeSplitter-&gt;RangeSplitter</text>
<line clip-path="url(#clipPath126)" fill="none" x1="261" x2="253" y1="876" y2="876" stroke-dasharray="none"/>
<line clip-path="url(#clipPath126)" fill="none" x1="253" x2="259" y1="876" y2="881" stroke-dasharray="none"/>
<line clip-path="url(#clipPath114)" fill="none" x1="214" x2="214" y1="466" y2="438" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="214" x2="405" y1="438" y2="438" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="405" x2="405" y1="438" y2="420" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<polygon fill="white" stroke-dasharray="none" clip-path="url(#clipPath122)" points=" 405 420 409 428 401 428" stroke="none"/>
<polygon fill="none" stroke-dasharray="none" clip-path="url(#clipPath122)" points=" 405 420 409 428 401 428" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="600" x2="447" y1="564" y2="622"/>
<image x="435" y="598" clip-path="url(#clipPath127)" width="16" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAhklEQVR42mNkIALo&#13;&#10;ebX+R+Zf2lbNCGMzEWMASENTbQhWOaIM2HD85v+65jUMCbF2DGSDvuWH/2PzDkGb&#13;&#10;CWlgoouzSQLEOBunF2jmbJCL8LoKl7NB4shRB6Nh4mAv4HI2TBzZFbAkDBJHNgTD&#13;&#10;2eguQncBjI1iCD5vYaPxasYWBsi2E6WZWAAAvauBMFaKJxwAAAAASUVORK5CYII=" xlink:type="simple" xlink:actuate="onLoad" height="16" preserveAspectRatio="none" xlink:show="embed"/>
<text x="454" y="611" clip-path="url(#clipPath128)" fill="black" stroke="none" xml:space="preserve">RangeSplitter-&gt;T, BaseClass-&gt;BRepMesh_DelaunayBaseMeshAlgo</text>
<line clip-path="url(#clipPath129)" fill="none" x1="452" x2="447" y1="617" y2="622" stroke-dasharray="none"/>
<line clip-path="url(#clipPath129)" fill="none" x1="447" x2="455" y1="622" y2="622" stroke-dasharray="none"/>
<line clip-path="url(#clipPath114)" fill="none" x1="1009" x2="1009" y1="1414" y2="1397" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="1009" x2="1009" y1="1397" y2="1397" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="1009" x2="1009" y1="1397" y2="1379" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<polygon fill="white" stroke-dasharray="none" clip-path="url(#clipPath130)" points=" 1009 1379 1013 1387 1005 1387" stroke="none"/>
<polygon fill="none" stroke-dasharray="none" clip-path="url(#clipPath130)" points=" 1009 1379 1013 1387 1005 1387" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="428" x2="428" y1="1125" y2="1097" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="428" x2="602" y1="1097" y2="1097" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="602" x2="602" y1="1097" y2="1079" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<polygon fill="white" stroke-dasharray="none" clip-path="url(#clipPath121)" points=" 602 1079 606 1087 598 1087" stroke="none"/>
<polygon fill="none" stroke-dasharray="none" clip-path="url(#clipPath121)" points=" 602 1079 606 1087 598 1087" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="1013" x2="1013" y1="1281" y2="1241" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="1013" x2="885" y1="1241" y2="1241" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="885" x2="885" y1="1241" y2="1223" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<polygon fill="white" stroke-dasharray="none" clip-path="url(#clipPath123)" points=" 885 1223 889 1231 881 1231" stroke="none"/>
<polygon fill="none" stroke-dasharray="none" clip-path="url(#clipPath123)" points=" 885 1223 889 1231 881 1231" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="793" x2="734" y1="827" y2="827" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath131)" fill="none" x1="741" x2="734" y1="824" y2="827" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath131)" fill="none" x1="734" x2="741" y1="827" y2="830" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="218" x2="218" y1="778" y2="738" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="218" x2="407" y1="738" y2="738" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="407" x2="407" y1="738" y2="720" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<polygon fill="white" stroke-dasharray="none" clip-path="url(#clipPath132)" points=" 407 720 411 728 403 728" stroke="none"/>
<polygon fill="none" stroke-dasharray="none" clip-path="url(#clipPath132)" points=" 407 720 411 728 403 728" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="590" x2="590" y1="981" y2="876"/>
<image x="409" y="897" clip-path="url(#clipPath133)" width="16" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAhklEQVR42mNkIALo&#13;&#10;ebX+R+Zf2lbNCGMzEWMASENTbQhWOaIM2HD85v+65jUMCbF2DGSDvuWH/2PzDkGb&#13;&#10;CWlgoouzSQLEOBunF2jmbJCL8LoKl7NB4shRB6Nh4mAv4HI2TBzZFbAkDBJHNgTD&#13;&#10;2eguQncBjI1iCD5vYaPxasYWBsi2E6WZWAAAvauBMFaKJxwAAAAASUVORK5CYII=" xlink:type="simple" xlink:actuate="onLoad" height="16" preserveAspectRatio="none" xlink:show="embed"/>
<text x="428" y="910" clip-path="url(#clipPath134)" fill="black" stroke="none" xml:space="preserve">RangeSplitter-&gt;RangeSplitter</text>
<line clip-path="url(#clipPath135)" fill="none" x1="593" x2="590" y1="883" y2="876" stroke-dasharray="none"/>
<line clip-path="url(#clipPath135)" fill="none" x1="590" x2="587" y1="876" y2="883" stroke-dasharray="none"/>
<line clip-path="url(#clipPath114)" fill="none" x1="520" x2="823" y1="376" y2="376" stroke="rgb(136,136,136)"/>
<text x="667" y="372" clip-path="url(#clipPath136)" fill="black" stroke="none" xml:space="preserve">&lt;&lt;use&gt;&gt;</text>
<line clip-path="url(#clipPath137)" fill="none" x1="816" x2="823" y1="379" y2="376" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath137)" fill="none" x1="823" x2="816" y1="376" y2="373" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="176" x2="176" y1="1125" y2="1097" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="176" x2="602" y1="1097" y2="1097" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="602" x2="602" y1="1097" y2="1079" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<polygon fill="white" stroke-dasharray="none" clip-path="url(#clipPath121)" points=" 602 1079 606 1087 598 1087" stroke="none"/>
<polygon fill="none" stroke-dasharray="none" clip-path="url(#clipPath121)" points=" 602 1079 606 1087 598 1087" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="896" x2="896" y1="1125" y2="1097" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="896" x2="602" y1="1097" y2="1097" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="602" x2="602" y1="1097" y2="1079" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<polygon fill="white" stroke-dasharray="none" clip-path="url(#clipPath121)" points=" 602 1079 606 1087 598 1087" stroke="none"/>
<polygon fill="none" stroke-dasharray="none" clip-path="url(#clipPath121)" points=" 602 1079 606 1087 598 1087" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="601" x2="601" y1="778" y2="738" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="601" x2="407" y1="738" y2="738" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="407" x2="407" y1="738" y2="720" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<polygon fill="white" stroke-dasharray="none" clip-path="url(#clipPath132)" points=" 407 720 411 728 403 728" stroke="none"/>
<polygon fill="none" stroke-dasharray="none" clip-path="url(#clipPath132)" points=" 407 720 411 728 403 728" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="406" x2="406" y1="322" y2="288" stroke="rgb(136,136,136)"/>
<polygon fill="white" clip-path="url(#clipPath138)" points=" 406 288 410 296 402 296" stroke="none"/>
<polygon fill="none" stroke-dasharray="none" clip-path="url(#clipPath138)" points=" 406 288 410 296 402 296" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="1399" x2="1399" y1="346" y2="288" stroke="rgb(136,136,136)"/>
<polygon fill="white" clip-path="url(#clipPath139)" points=" 1399 288 1403 296 1395 296" stroke="none"/>
<polygon fill="none" stroke-dasharray="none" clip-path="url(#clipPath139)" points=" 1399 288 1403 296 1395 296" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="793" x2="734" y1="827" y2="827" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath131)" fill="none" x1="741" x2="734" y1="824" y2="827" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath131)" fill="none" x1="734" x2="741" y1="827" y2="830" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="1297" x2="1165" y1="398" y2="398" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath140)" fill="none" x1="1172" x2="1165" y1="395" y2="398" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath140)" fill="none" x1="1165" x2="1172" y1="398" y2="401" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="176" x2="176" y1="1125" y2="1097" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="176" x2="602" y1="1097" y2="1097" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="602" x2="602" y1="1097" y2="1079" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<polygon fill="white" stroke-dasharray="none" clip-path="url(#clipPath121)" points=" 602 1079 606 1087 598 1087" stroke="none"/>
<polygon fill="none" stroke-dasharray="none" clip-path="url(#clipPath121)" points=" 602 1079 606 1087 598 1087" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="428" x2="428" y1="1125" y2="1097" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="428" x2="602" y1="1097" y2="1097" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="602" x2="602" y1="1097" y2="1079" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<polygon fill="white" stroke-dasharray="none" clip-path="url(#clipPath121)" points=" 602 1079 606 1087 598 1087" stroke="none"/>
<polygon fill="none" stroke-dasharray="none" clip-path="url(#clipPath121)" points=" 602 1079 606 1087 598 1087" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="672" x2="672" y1="1125" y2="1097" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="672" x2="602" y1="1097" y2="1097" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="602" x2="602" y1="1097" y2="1079" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<polygon fill="white" stroke-dasharray="none" clip-path="url(#clipPath121)" points=" 602 1079 606 1087 598 1087" stroke="none"/>
<polygon fill="none" stroke-dasharray="none" clip-path="url(#clipPath121)" points=" 602 1079 606 1087 598 1087" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="896" x2="896" y1="1125" y2="1097" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="896" x2="602" y1="1097" y2="1097" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="602" x2="602" y1="1097" y2="1079" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<polygon fill="white" stroke-dasharray="none" clip-path="url(#clipPath121)" points=" 602 1079 606 1087 598 1087" stroke="none"/>
<polygon fill="none" stroke-dasharray="none" clip-path="url(#clipPath121)" points=" 602 1079 606 1087 598 1087" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="757" x2="757" y1="1281" y2="1241" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="757" x2="885" y1="1241" y2="1241" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="885" x2="885" y1="1241" y2="1223" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<polygon fill="white" stroke-dasharray="none" clip-path="url(#clipPath123)" points=" 885 1223 889 1231 881 1231" stroke="none"/>
<polygon fill="none" stroke-dasharray="none" clip-path="url(#clipPath123)" points=" 885 1223 889 1231 881 1231" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="1013" x2="1013" y1="1281" y2="1241" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="1013" x2="885" y1="1241" y2="1241" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="885" x2="885" y1="1241" y2="1223" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<polygon fill="white" stroke-dasharray="none" clip-path="url(#clipPath123)" points=" 885 1223 889 1231 881 1231" stroke="none"/>
<polygon fill="none" stroke-dasharray="none" clip-path="url(#clipPath123)" points=" 885 1223 889 1231 881 1231" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="538" x2="253" y1="981" y2="876"/>
<image x="128" y="903" clip-path="url(#clipPath124)" width="16" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAhklEQVR42mNkIALo&#13;&#10;ebX+R+Zf2lbNCGMzEWMASENTbQhWOaIM2HD85v+65jUMCbF2DGSDvuWH/2PzDkGb&#13;&#10;CWlgoouzSQLEOBunF2jmbJCL8LoKl7NB4shRB6Nh4mAv4HI2TBzZFbAkDBJHNgTD&#13;&#10;2eguQncBjI1iCD5vYaPxasYWBsi2E6WZWAAAvauBMFaKJxwAAAAASUVORK5CYII=" xlink:type="simple" xlink:actuate="onLoad" height="16" preserveAspectRatio="none" xlink:show="embed"/>
<text x="147" y="916" clip-path="url(#clipPath125)" fill="black" stroke="none" xml:space="preserve">RangeSplitter-&gt;RangeSplitter</text>
<line clip-path="url(#clipPath126)" fill="none" x1="261" x2="253" y1="876" y2="876" stroke-dasharray="none"/>
<line clip-path="url(#clipPath126)" fill="none" x1="253" x2="259" y1="876" y2="881" stroke-dasharray="none"/>
<line clip-path="url(#clipPath114)" fill="none" x1="590" x2="590" y1="981" y2="876"/>
<image x="409" y="897" clip-path="url(#clipPath133)" width="16" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAhklEQVR42mNkIALo&#13;&#10;ebX+R+Zf2lbNCGMzEWMASENTbQhWOaIM2HD85v+65jUMCbF2DGSDvuWH/2PzDkGb&#13;&#10;CWlgoouzSQLEOBunF2jmbJCL8LoKl7NB4shRB6Nh4mAv4HI2TBzZFbAkDBJHNgTD&#13;&#10;2eguQncBjI1iCD5vYaPxasYWBsi2E6WZWAAAvauBMFaKJxwAAAAASUVORK5CYII=" xlink:type="simple" xlink:actuate="onLoad" height="16" preserveAspectRatio="none" xlink:show="embed"/>
<text x="428" y="910" clip-path="url(#clipPath134)" fill="black" stroke="none" xml:space="preserve">RangeSplitter-&gt;RangeSplitter</text>
<line clip-path="url(#clipPath135)" fill="none" x1="593" x2="590" y1="883" y2="876" stroke-dasharray="none"/>
<line clip-path="url(#clipPath135)" fill="none" x1="590" x2="587" y1="876" y2="883" stroke-dasharray="none"/>
<line clip-path="url(#clipPath114)" fill="none" x1="661" x2="913" y1="981" y2="876"/>
<image x="829" y="909" clip-path="url(#clipPath118)" width="16" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAhklEQVR42mNkIALo&#13;&#10;ebX+R+Zf2lbNCGMzEWMASENTbQhWOaIM2HD85v+65jUMCbF2DGSDvuWH/2PzDkGb&#13;&#10;CWlgoouzSQLEOBunF2jmbJCL8LoKl7NB4shRB6Nh4mAv4HI2TBzZFbAkDBJHNgTD&#13;&#10;2eguQncBjI1iCD5vYaPxasYWBsi2E6WZWAAAvauBMFaKJxwAAAAASUVORK5CYII=" xlink:type="simple" xlink:actuate="onLoad" height="16" preserveAspectRatio="none" xlink:show="embed"/>
<text x="848" y="922" clip-path="url(#clipPath119)" fill="black" stroke="none" xml:space="preserve">RangeSplitter-&gt;RangeSplitter</text>
<line clip-path="url(#clipPath120)" fill="none" x1="908" x2="913" y1="881" y2="876" stroke-dasharray="none"/>
<line clip-path="url(#clipPath120)" fill="none" x1="913" x2="905" y1="876" y2="876" stroke-dasharray="none"/>
<line clip-path="url(#clipPath114)" fill="none" x1="828" x2="409" y1="108" y2="190" stroke="rgb(136,136,136)"/>
<text x="580" y="136" clip-path="url(#clipPath141)" fill="black" stroke="none" xml:space="preserve">&lt;&lt;use&gt;&gt;</text>
<line clip-path="url(#clipPath142)" fill="none" x1="415" x2="409" y1="186" y2="190" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath142)" fill="none" x1="409" x2="416" y1="190" y2="192" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="966" x2="1393" y1="108" y2="190" stroke="rgb(136,136,136)"/>
<text x="1141" y="142" clip-path="url(#clipPath143)" fill="black" stroke="none" xml:space="preserve">&lt;&lt;use&gt;&gt;</text>
<line clip-path="url(#clipPath144)" fill="none" x1="1386" x2="1393" y1="192" y2="190" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath144)" fill="none" x1="1393" x2="1387" y1="190" y2="186" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="406" x2="406" y1="322" y2="288" stroke="rgb(136,136,136)"/>
<polygon fill="white" clip-path="url(#clipPath138)" points=" 406 288 410 296 402 296" stroke="none"/>
<polygon fill="none" stroke-dasharray="none" clip-path="url(#clipPath138)" points=" 406 288 410 296 402 296" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="214" x2="214" y1="466" y2="438" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="214" x2="405" y1="438" y2="438" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="405" x2="405" y1="438" y2="420" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<polygon fill="white" stroke-dasharray="none" clip-path="url(#clipPath122)" points=" 405 420 409 428 401 428" stroke="none"/>
<polygon fill="none" stroke-dasharray="none" clip-path="url(#clipPath122)" points=" 405 420 409 428 401 428" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="520" x2="823" y1="376" y2="376" stroke="rgb(136,136,136)"/>
<text x="667" y="372" clip-path="url(#clipPath136)" fill="black" stroke="none" xml:space="preserve">&lt;&lt;use&gt;&gt;</text>
<line clip-path="url(#clipPath137)" fill="none" x1="816" x2="823" y1="379" y2="376" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath137)" fill="none" x1="823" x2="816" y1="376" y2="373" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="602" x2="602" y1="466" y2="438" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="602" x2="405" y1="438" y2="438" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="405" x2="405" y1="438" y2="420" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<polygon fill="white" stroke-dasharray="none" clip-path="url(#clipPath122)" points=" 405 420 409 428 401 428" stroke="none"/>
<polygon fill="none" stroke-dasharray="none" clip-path="url(#clipPath122)" points=" 405 420 409 428 401 428" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="600" x2="447" y1="564" y2="622"/>
<image x="435" y="598" clip-path="url(#clipPath127)" width="16" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAhklEQVR42mNkIALo&#13;&#10;ebX+R+Zf2lbNCGMzEWMASENTbQhWOaIM2HD85v+65jUMCbF2DGSDvuWH/2PzDkGb&#13;&#10;CWlgoouzSQLEOBunF2jmbJCL8LoKl7NB4shRB6Nh4mAv4HI2TBzZFbAkDBJHNgTD&#13;&#10;2eguQncBjI1iCD5vYaPxasYWBsi2E6WZWAAAvauBMFaKJxwAAAAASUVORK5CYII=" xlink:type="simple" xlink:actuate="onLoad" height="16" preserveAspectRatio="none" xlink:show="embed"/>
<text x="454" y="611" clip-path="url(#clipPath128)" fill="black" stroke="none" xml:space="preserve">RangeSplitter-&gt;T, BaseClass-&gt;BRepMesh_DelaunayBaseMeshAlgo</text>
<line clip-path="url(#clipPath129)" fill="none" x1="452" x2="447" y1="617" y2="622" stroke-dasharray="none"/>
<line clip-path="url(#clipPath129)" fill="none" x1="447" x2="455" y1="622" y2="622" stroke-dasharray="none"/>
<line clip-path="url(#clipPath114)" fill="none" x1="219" x2="375" y1="564" y2="622"/>
<image x="23" y="598" clip-path="url(#clipPath115)" width="16" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAhklEQVR42mNkIALo&#13;&#10;ebX+R+Zf2lbNCGMzEWMASENTbQhWOaIM2HD85v+65jUMCbF2DGSDvuWH/2PzDkGb&#13;&#10;CWlgoouzSQLEOBunF2jmbJCL8LoKl7NB4shRB6Nh4mAv4HI2TBzZFbAkDBJHNgTD&#13;&#10;2eguQncBjI1iCD5vYaPxasYWBsi2E6WZWAAAvauBMFaKJxwAAAAASUVORK5CYII=" xlink:type="simple" xlink:actuate="onLoad" height="16" preserveAspectRatio="none" xlink:show="embed"/>
<text x="42" y="611" clip-path="url(#clipPath116)" fill="black" stroke="none" xml:space="preserve">RangeSplitter-&gt;T, BaseClass-&gt;BRepMesh_SweepLineMeshAlgo</text>
<line clip-path="url(#clipPath117)" fill="none" x1="367" x2="375" y1="622" y2="622" stroke-dasharray="none"/>
<line clip-path="url(#clipPath117)" fill="none" x1="375" x2="369" y1="622" y2="617" stroke-dasharray="none"/>
<line clip-path="url(#clipPath114)" fill="none" x1="218" x2="218" y1="778" y2="738" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="218" x2="407" y1="738" y2="738" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="407" x2="407" y1="738" y2="720" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<polygon fill="white" stroke-dasharray="none" clip-path="url(#clipPath132)" points=" 407 720 411 728 403 728" stroke="none"/>
<polygon fill="none" stroke-dasharray="none" clip-path="url(#clipPath132)" points=" 407 720 411 728 403 728" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="601" x2="601" y1="778" y2="738" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="601" x2="407" y1="738" y2="738" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="407" x2="407" y1="738" y2="720" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<polygon fill="white" stroke-dasharray="none" clip-path="url(#clipPath132)" points=" 407 720 411 728 403 728" stroke="none"/>
<polygon fill="none" stroke-dasharray="none" clip-path="url(#clipPath132)" points=" 407 720 411 728 403 728" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="1009" x2="1009" y1="1414" y2="1397" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="1009" x2="1009" y1="1397" y2="1397" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<line clip-path="url(#clipPath114)" fill="none" x1="1009" x2="1009" y1="1397" y2="1379" stroke-dasharray="none" stroke="rgb(136,136,136)"/>
<polygon fill="white" stroke-dasharray="none" clip-path="url(#clipPath130)" points=" 1009 1379 1013 1387 1005 1387" stroke="none"/>
<polygon fill="none" stroke-dasharray="none" clip-path="url(#clipPath130)" points=" 1009 1379 1013 1387 1005 1387" stroke="rgb(136,136,136)"/>
</g>
</g>
</svg>
