DOXYFILE_ENCODING      = UTF-8
CREATE_SUBDIRS         = NO
OUTPUT_LANGUAGE        = English
MULTILINE_CPP_IS_BRIEF = YES
INHERIT_DOCS           = YES
REPEAT_BRIEF           = YES
ALWAYS_DETAILED_SEC    = NO
INLINE_INHERITED_MEMB  = NO
FULL_PATH_NAMES        = NO
OPTIMIZE_OUTPUT_FOR_C  = YES
SUBGROUPING            = YES
DISTRIBUTE_GROUP_DOC   = YES
EXTRACT_ALL            = YES
EXTRACT_PRIVATE        = NO
EXTRACT_LOCAL_CLASSES  = NO
EXTRACT_LOCAL_METHODS  = NO
HIDE_FRIEND_COMPOUNDS  = YES
HIDE_UNDOC_MEMBERS     = NO
INLINE_INFO            = YES
VERBATIM_HEADERS       = NO
QUIET                  = YES
WARNINGS               = NO
ENABLE_PREPROCESSING   = YES
MACRO_EXPANSION        = YES
EXPAND_ONLY_PREDEF     = YES
PREDEFINED             = Standard_EXPORT Standard_NODISCARD Standard_OVERRIDE:=override __Standard_API __Draw_API Handle(a):=Handle<a> DEFINE_STANDARD_ALLOC DEFINE_NCOLLECTION_ALLOC "Standard_DEPRECATED=//! @deprecated "
GENERATE_HTML          = YES
GENERATE_LATEX         = NO
SEARCH_INCLUDES        = YES
ALLEXTERNALS           = NO
EXTERNAL_GROUPS        = NO
COLLABORATION_GRAPH    = NO
ENABLE_PREPROCESSING   = YES
INCLUDE_FILE_PATTERNS  = *.hxx *.pxx
EXCLUDE_PATTERNS       = */Handle_*.hxx
SKIP_FUNCTION_MACROS   = YES
INLINE_SOURCES         = NO
HAVE_DOT               = YES
DOT_GRAPH_MAX_NODES    = 100
INCLUDE_GRAPH          = NO
INCLUDED_BY_GRAPH      = NO
DOT_MULTI_TARGETS      = YES
DOT_IMAGE_FORMAT       = png
GENERATE_LEGEND        = YES
DOT_CLEANUP            = YES
GRAPHICAL_HIERARCHY    = NO
