﻿Tutorials and Samples {#samples}
=====================

- @subpage samples__tutorials
  * @ref samples__novice_guide
    <br>A document providing an introductory information to newcomers.
  * @ref samples__draw_scripts
    <br>A set of demo scripts demonstrating OCCT functionality from DRAW.
    These scripts can be also considered as a tutorials on **Tcl** usage within @ref occt_user_guides__test_harness "Draw Harness".
  * @ref occt__tutorial
    <br>A programming tutorial teaching how to use OCCT services to model a 3D object.
    See also @ref samples_qt_tutorial
  * @ref samples__ocaf
    <br>A set of code snippets performing typical actions with @ref occt_user_guides__ocaf "OCAF" services for newcomers.
  * @ref samples__ocaf_func
    <br>A simple example dedicated to the usage of "Function Mechanism" of @ref occt_user_guides__ocaf "OCCT Application Framework".
  * @ref tutorials__ais_object
    <br>A programming tutorial teaching how to compute presentation within AIS_InteractiveObject subclass for displaying in @ref occt_user_guides__visualization "OCCT 3D Viewer".
- @subpage samples__projects
  * @ref samples_qt_iesample
    <br>A cross-platform multi-document 3D Viewer sample with CAD import / export functionality based on **Qt Widgets** framework.
  * @ref samples_qml_android_occt
    <br>A cross-platform 3D Viewer sample with CAD import based on **QtQuick** framework.
  * @ref samples_qt_tutorial
    <br>A cross-platform sample application based on **Qt Widgets** framework and implementing @ref occt__tutorial.
  * @ref samples_qt_overview
    <br>A sample application interactively demonstrating OCCT C++ usage with code snippets for newcomers.
  * @ref samples_mfc_standard
    <br>A set of projects for Windows platform demonstrating OCCT usage based on **Microsoft Foundation Class** (**MFC**) library.
  * @ref samples_csharp_occt
    <br>A Multi-document 3D Viewer sample with CAD import / export functionality based on .NET and **Windows Forms** or **WPF**.
  * @ref samples_csharp_direct3d
    <br>3D Viewer sample wrapped into Direct3D context based on .NET and **Windows Presentation Foundation** (**WPF**).
  * @ref occt_samples_webgl
    <br>3D Viewer sample based on **Emscripten SDK** representing a static HTML page to be opened in Web Browser.
  * @ref samples_java_android_occt
    <br>3D Viewer sample with CAD import for Android mobile platform based on Android SDK and JNI layer.
  * @ref occt_samples_ios_uikit
    <br>3D Viewer sample for iOS platform based on Apple **UIKit** framework.
  * @ref occt_samples_glfw
    <br>A cross-platform 3D Viewer sample using **GLFW** library.

@page samples__tutorials Tutorials and Demos
- @subpage samples__novice_guide
- @subpage samples__draw_scripts
- @subpage occt__tutorial
- @subpage samples__ocaf
- @subpage samples__ocaf_func
- @subpage tutorials__ais_object

@page samples__projects Sample Projects
- @subpage samples_qt_iesample
- @subpage samples_qml_android_occt
- @subpage samples_qt_tutorial
- @subpage samples_qt_overview
- @subpage samples_mfc_standard
- @subpage samples_csharp_occt
- @subpage samples_csharp_direct3d
- @subpage occt_samples_webgl
- @subpage samples_java_android_occt
- @subpage occt_samples_ios_uikit
- @subpage occt_samples_glfw
