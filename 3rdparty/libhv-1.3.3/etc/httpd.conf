# [root]

# logfile = logs/httpd.log
# loglevel = [VERBOSE,DEBUG,INFO,WARN,ERROR,FATAL,SILENT]
loglevel = INFO
log_remain_days = 3
log_filesize = 64M

# multi-processes mode
# auto = ncpu
worker_processes = auto
worker_threads = 1

# multi-threads mode
# worker_processes = 1
# worker_threads = auto

# Disable multi-processes mode for debugging
# worker_processes = 0

# max_connections = workers * worker_connections
worker_connections = 1024

# http server
http_port = 8080
https_port = 8443
#base_url = /api/v1
document_root = html
home_page = index.html
#error_page = error.html
index_of = /downloads/
keepalive_timeout = 75000 # ms
limit_rate = 500 # KB/s
access_log = off
cors = true

# SSL/TLS
ssl_certificate = cert/server.crt
ssl_privatekey = cert/server.key
ssl_ca_certificate = cert/cacert.pem

# proxy
[proxy]
proxy_connect_timeout   = 10000 # ms
proxy_read_timeout      = 60000 # ms
proxy_write_timeout     = 60000 # ms
# forward proxy
forward_proxy = true
trust_proxies = *httpbin.org;*postman-echo.com;*apifox.com
#no_proxies = *
# reverse proxy
/httpbin/ => http://httpbin.org/
/postman/ => http://postman-echo.com/
/apifox/  => https://echo.apifox.com/
