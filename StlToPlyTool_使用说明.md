# StlToPlyTool 使用说明

## 功能概述
StlToPlyTool 现在支持两种输入文件格式：
- **STL 文件**：转换为 PLY 点云并应用缩放
- **PLY 文件**：仅应用缩放，不进行转换

## 命令行参数
```
用法: ./StlToPlyTool [options]
选项:
  -i, --input <value>             输入模型文件路径 (STL或PLY) (必填)
  -n, --cloud-points <value>      点云采样点数 (仅STL转换时使用, 默认: 100000)
  -o, --output-dir <path>         PLY点云输出目录 (可选, 默认: 同输入文件目录)
  -c, --scale <value>             缩放因子 (可选, 默认: 1.0, 1000表示m转mm, 0.001表示mm转m)

说明:
  - STL输入: 转换为PLY点云并应用缩放
  - PLY输入: 仅应用缩放，不进行转换
```

## 使用示例

### 1. STL 转 PLY（带缩放）
```bash
# 将 STL 文件转换为 PLY 点云，并将单位从米转换为毫米
./StlToPlyTool -i model.stl -c 1000 -n 50000

# 将 STL 文件转换为 PLY 点云，并将单位从毫米转换为米
./StlToPlyTool -i model.stl -c 0.001 -n 100000
```

### 2. PLY 文件缩放
```bash
# 对现有 PLY 文件进行缩放（米转毫米）
./StlToPlyTool -i pointcloud.ply -c 1000

# 对现有 PLY 文件进行缩放（毫米转米）
./StlToPlyTool -i pointcloud.ply -c 0.001

# 指定输出目录
./StlToPlyTool -i pointcloud.ply -c 2.0 -o output_dir
```

### 3. 输出文件命名规则
- **STL 输入**：`输入文件名.ply`
- **PLY 输入**：`输入文件名_scaled.ply`

## 常用缩放因子
- `1000`：米 → 毫米
- `0.001`：毫米 → 米
- `1.0`：不缩放（默认值）

## 注意事项
1. PLY 输入时，`-n` 参数会被忽略，因为不进行重新采样
2. 支持的文件格式：`.stl` 和 `.ply`（大小写不敏感）
3. 输出目录不存在时会自动创建
4. PCL 读取某些 PLY 文件时可能会有警告信息，但不影响功能
