#ifndef SIMPLE_3D_BUILDER_H
#define SIMPLE_3D_BUILDER_H
#include <string>
#include <TopoDS_Shape.hxx>

/**
 * @class Simple3DBuilder
 * @brief 通用三维模型创建工具
 */
class Simple3DBuilder {
 public:
  // 网格化参数常量 - 针对米单位优化
  static constexpr double MESH_LINEAR_DEFLECTION = 0.01;   // 线性偏差 (1cm，适合米单位)
  static constexpr double MESH_ANGULAR_DEFLECTION = 0.1;   // 角度偏差 (弧度)

  /**
   * @brief 创建开口箱子三维模型
   *
   * @param inner_length  内框长度（单位：毫米）
   * @param inner_width   内框宽度（单位：毫米）
   * @param inner_height  内框高度（单位：毫米）
   * @param outer_length  外框长度（单位：毫米）
   * @param outer_width   外框宽度（单位：毫米）
   * @param outer_height  外框高度（单位：毫米）
   * @param corner_radius 倒角半径（单位：毫米）, 0: 不使用倒角
   * @param cloud_points  PLY点云数量(STL模型采样点个数)
   * @param output_dir    模型文件保存文件夹
   * @param output_stl    是否输出STL模型文件，默认: false
   * @return 模型文件路径
   */
  static std::string generate_openbox(
      double inner_length,
      double inner_width,
      double inner_height,
      double outer_length,
      double outer_width,
      double outer_height,
      double corner_radius,
      size_t cloud_points,
      const std::string& output_dir,
      bool output_stl = false);

  /**
   * @brief STL模型文件转PLY点云文件（随机采样）
   *
   * @param stl_path          STL文件路径
   * @param ply_path          输出PLY文件路径
   * @param target_total_points PLY点云数量(STL模型采样点个数)
   * @param scale             缩放因子（默认1.0不缩放，1000表示m转mm，0.001表示mm转m）
   */
  static void sample_stl_to_ply(const std::string& stl_path, const std::string& ply_path, int target_total_points, double scale = 1.0);

  // 新增函数：生成机器人末端执行工具
  static std::string generate_robot_tool(
        int tool_type, double tool_diameter, double tool_length, double tip_length,
        int flange_type, double flange_diameter, double flange_height, double bolt_circle, int bolt_number,
        double bolt_radius, double center_hole_radius, double center_hole_length, double angle_offset,
        size_t cloud_points, const std::string& output_dir, bool output_stl);

  /**
   * @brief 将三维形状表面采样为点云并保存为PLY文件
   * @param shape 要采样的三维形状
   * @param ply_path PLY文件输出路径
   * @param target_total_points 目标点云总数
   */
  static void sample_shape_surface_to_ply(const TopoDS_Shape& shape, const std::string& ply_path, int target_total_points);

};

#endif  // SIMPLE_3D_BUILDER_H