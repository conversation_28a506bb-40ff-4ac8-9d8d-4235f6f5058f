#include <iostream>
#include <getopt.h>
#include <string>
#include <stdexcept>
#include <filesystem>
#include <algorithm>
#include "Simple3DBuilder.h"

void print_usage() {
    std::cout << "用法: ./StlToPlyTool [options]\n"
              << "选项:\n"
              << "  -i, --input <value>             输入模型文件路径 (STL或PLY) (必填)\n"
              << "  -n, --cloud-points <value>      点云采样点数 (仅STL转换时使用, 默认: 100000)\n"
              << "  -o, --output-dir <path>         PLY点云输出目录 (可选, 默认: 同输入文件目录)\n"
              << "  -c, --scale <value>             缩放因子 (可选, 默认: 1.0, 1000表示m转mm, 0.001表示mm转m)\n"
              << "\n说明:\n"
              << "  - STL输入: 转换为PLY点云并应用缩放\n"
              << "  - PLY输入: 仅应用缩放，不进行转换\n";
}

int main(int argc, char* argv[]) {
    size_t cloud_points = 100000;
    std::string output_dir = "";
    std::string input_path = "";
    double scale = 1.0;

    const struct option long_options[] = {
        {"input", required_argument, nullptr, 'i'},
        {"cloud-points", required_argument, nullptr, 'n'},
        {"output-dir", required_argument, nullptr, 'o'},
        {"scale", required_argument, nullptr, 'c'},
        {nullptr, 0, nullptr, 0}
    };

    int opt;
    while ((opt = getopt_long(argc, argv, "i:n:o:c:", long_options, nullptr)) != -1) {
        try {
            switch (opt) {
                case 'i': input_path = optarg ? optarg : ""; break;
                case 'n': cloud_points = std::stoi(optarg); break;
                case 'o': output_dir = optarg ? optarg : ""; break;
                case 'c': scale = std::stod(optarg); break;
                default:
                    print_usage();
                    return 1;
            }
        } catch (const std::invalid_argument&) {
            std::cerr << "参数值无效，请输入有效数字。" << std::endl;
            return 1;
        }
    }

    if (input_path.empty() || cloud_points <= 0) {
        print_usage();
        return 1;
    }

    if (scale <= 0) {
        std::cerr << "错误：缩放因子必须大于0。" << std::endl;
        return 1;
    }

    try {
        std::filesystem::path input_fs_path(input_path);
        if (output_dir.empty()) {
            output_dir = input_fs_path.parent_path();
        }

        // 检查输入文件扩展名
        std::string extension = input_fs_path.extension().string();
        std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);

        if (extension == ".stl") {
            // STL 输入：转换为 PLY 点云并应用缩放
            auto ply_path = std::filesystem::path(output_dir) / (input_fs_path.stem().string() + ".ply");
            Simple3DBuilder::sample_stl_to_ply(input_path, ply_path, cloud_points, scale);
        } else if (extension == ".ply") {
            // PLY 输入：仅应用缩放
            auto output_ply_path = std::filesystem::path(output_dir) / (input_fs_path.stem().string() + "_scaled.ply");
            Simple3DBuilder::scale_ply_file(input_path, output_ply_path, scale);
        } else {
            std::cerr << "错误：不支持的文件格式。仅支持 .stl 和 .ply 文件。" << std::endl;
            return 1;
        }
    } catch (const std::exception& e) {
        std::cerr << "错误：" << e.what() << std::endl;
        return 1;
    }

    return 0;
}
