#include <iostream>
#include <getopt.h>
#include <string>
#include <stdexcept>
#include <filesystem>
#include "Simple3DBuilder.h"

void print_usage() {
    std::cout << "用法: ./StlToPlyTool [options]\n"
              << "选项:\n"
              << "  -s, --stl <value>               STL模型文件路径 (必填)\n"
              << "  -n, --cloud-points <value>      点云采样点数 (可选, 默认: 100000)\n"
              << "  -o, --output-dir <path>         PLY点云输出目录 (可选, 默认: 同STL文件目录)\n"
              << "  -c, --scale <value>             缩放因子 (可选, 默认: 1.0, 1000表示m转mm, 0.001表示mm转m)\n";
}

int main(int argc, char* argv[]) {
    size_t cloud_points = 100000;
    std::string output_dir = "";
    std::string stl_path = "";
    double scale = 1.0;

    const struct option long_options[] = {
        {"stl", required_argument, nullptr, 's'},
        {"cloud-points", required_argument, nullptr, 'n'},
        {"output-dir", required_argument, nullptr, 'o'},
        {"scale", required_argument, nullptr, 'c'},
        {nullptr, 0, nullptr, 0}
    };

    int opt;
    while ((opt = getopt_long(argc, argv, "s:n:o:c:", long_options, nullptr)) != -1) {
        try {
            switch (opt) {
                case 's': stl_path = optarg ? optarg : ""; break;
                case 'n': cloud_points = std::stoi(optarg); break;
                case 'o': output_dir = optarg ? optarg : ""; break;
                case 'c': scale = std::stod(optarg); break;
                default:
                    print_usage();
                    return 1;
            }
        } catch (const std::invalid_argument&) {
            std::cerr << "参数值无效，请输入有效数字。" << std::endl;
            return 1;
        }
    }

    if (stl_path.empty() || cloud_points <= 0) {
        print_usage();
        return 1;
    }

    if (scale <= 0) {
        std::cerr << "错误：缩放因子必须大于0。" << std::endl;
        return 1;
    }

    try {

        std::filesystem::path stl_fs_path(stl_path);
        if (output_dir.empty()) {
            output_dir = stl_fs_path.parent_path();
        }
        auto ply_path = std::filesystem::path(output_dir) / (stl_fs_path.stem().string() + ".ply");

        Simple3DBuilder::sample_stl_to_ply(stl_path, ply_path, cloud_points, scale);
    } catch (const std::exception& e) {
        std::cerr << "错误：" << e.what() << std::endl;
        return 1;
    }

    return 0;
}
